#!/usr/bin/env python3
"""
Test script for background removal functionality.
Tests the backend service with images from the uploads folder.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print("⚠️  python-dotenv not installed, using system environment variables")

# Set up fallback environment variables for testing
os.environ.setdefault('FAL_KEY', 'test_key_not_set')
os.environ.setdefault('BACKGROUND_REMOVAL_ENABLED', 'true')

from app.services.background_removal import background_removal_service
from app.services.landmark_detection import save_uploaded_image, image_store


def test_service_status():
    """Test if the background removal service is properly configured."""
    print("🔍 Testing Background Removal Service Status...")

    is_enabled = background_removal_service.is_enabled()
    print(f"Service enabled: {is_enabled}")

    if not is_enabled:
        print("❌ Service is not enabled. Possible reasons:")
        print("   - FAL_KEY environment variable not set")
        print("   - fal-client not installed")
        print("   - BACKGROUND_REMOVAL_ENABLED set to false")
        return False

    print("✅ Service is enabled and ready")
    return True


def load_test_image():
    """Load a test image from the uploads folder."""
    print("\n📁 Loading test image from uploads folder...")

    uploads_dir = project_root / "app" / "uploads"
    image_files = list(uploads_dir.glob("*.jpg")) + list(uploads_dir.glob("*.jpeg")) + list(uploads_dir.glob("*.png"))

    if not image_files:
        print("❌ No image files found in uploads folder")
        return None

    test_image_path = image_files[0]
    print(f"📸 Using test image: {test_image_path.name}")

    try:
        # Read the image file
        with open(test_image_path, 'rb') as f:
            image_data = f.read()

        # Save it to the image store (simulating upload)
        image_id, width, height, format_type = save_uploaded_image(image_data)
        print(f"✅ Image loaded successfully:")
        print(f"   - Image ID: {image_id}")
        print(f"   - Dimensions: {width}x{height}")
        print(f"   - Format: {format_type}")

        return image_id

    except Exception as e:
        print(f"❌ Error loading image: {str(e)}")
        return None


async def test_background_removal(image_id):
    """Test the background removal functionality."""
    print(f"\n🎨 Testing background removal for image {image_id}...")

    try:
        # Test the async background removal
        processed_image_id, error = await background_removal_service.remove_background_async(image_id)

        if error:
            print(f"❌ Background removal failed: {error}")
            return False

        if processed_image_id:
            print(f"✅ Background removal successful!")
            print(f"   - Original image ID: {image_id}")
            print(f"   - Processed image ID: {processed_image_id}")

            # Check if the processed image is in the store
            if processed_image_id in image_store:
                processed_data = image_store[processed_image_id]
                print(f"   - Processed image dimensions: {processed_data['width']}x{processed_data['height']}")
                print(f"   - Background removed: {processed_data.get('background_removed', False)}")
                print(f"   - Original image ID: {processed_data.get('original_image_id', 'N/A')}")

                # Check metadata
                metadata = processed_data.get('processing_metadata', {})
                if metadata:
                    print(f"   - Model used: {metadata.get('model', 'N/A')}")
                    print(f"   - Resolution: {metadata.get('resolution', 'N/A')}")

            return True
        else:
            print("❌ No processed image ID returned")
            return False

    except Exception as e:
        print(f"❌ Exception during background removal: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_sync_wrapper(image_id):
    """Test the synchronous wrapper function."""
    print(f"\n🔄 Testing synchronous wrapper for image {image_id}...")

    try:
        processed_image_id, error = background_removal_service.remove_background_sync(image_id)

        if error:
            print(f"❌ Sync background removal failed: {error}")
            return False

        if processed_image_id:
            print(f"✅ Sync background removal successful!")
            print(f"   - Processed image ID: {processed_image_id}")
            return True
        else:
            print("❌ No processed image ID returned from sync method")
            return False

    except Exception as e:
        print(f"❌ Exception during sync background removal: {str(e)}")
        return False


def check_dependencies():
    """Check if required dependencies are installed."""
    print("📦 Checking dependencies...")

    try:
        import fal_client
        print("✅ fal-client is installed")
        return True
    except ImportError:
        print("❌ fal-client is not installed")
        print("   Run: pip install fal-client")
        return False


def check_environment():
    """Check environment configuration."""
    print("\n🌍 Checking environment configuration...")

    fal_key = os.getenv('FAL_KEY')
    if not fal_key or fal_key == 'test_key_not_set':
        print("❌ FAL_KEY environment variable not set")
        print("   Set your fal.ai API key: export FAL_KEY=your_api_key_here")
        return False
    else:
        print(f"✅ FAL_KEY is set (length: {len(fal_key)} characters)")
        if fal_key == 'test_key_for_demo':
            print("   ⚠️  Using demo key - API calls will fail but service will be enabled")

    bg_enabled = os.getenv('BACKGROUND_REMOVAL_ENABLED', 'true').lower()
    print(f"✅ BACKGROUND_REMOVAL_ENABLED: {bg_enabled}")

    return True


def test_frontend_integration():
    """Test the frontend integration by checking the API endpoints."""
    print("\n🌐 Testing Frontend Integration...")

    try:
        import requests

        # Test status endpoint
        print("   Testing /api/background-removal/status endpoint...")
        response = requests.get('http://localhost:8000/api/background-removal/status')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Status endpoint working: enabled={data.get('enabled')}")
            print(f"      Message: {data.get('message')}")
            return data.get('enabled', False)
        else:
            print(f"   ❌ Status endpoint failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"   ❌ Frontend integration test failed: {str(e)}")
        print("   Make sure the server is running on http://localhost:8000")
        return False


async def main():
    """Main test function."""
    print("🧪 Background Removal Backend Test")
    print("=" * 50)

    # Check dependencies
    deps_ok = check_dependencies()
    if not deps_ok:
        return

    # Check environment
    env_ok = check_environment()

    # Test service status
    service_ok = test_service_status()

    # Test frontend integration
    frontend_ok = test_frontend_integration()

    # Load test image
    image_id = load_test_image()
    if not image_id:
        return

    # Only proceed with actual API tests if environment is properly configured
    if env_ok and service_ok and os.getenv('FAL_KEY') != 'test_key_for_demo':
        print("\n🚀 Proceeding with API tests...")

        # Test async background removal
        success = await test_background_removal(image_id)

        if success:
            print("\n🎉 All tests passed! Background removal is working correctly.")
        else:
            print("\n❌ Background removal test failed.")
    else:
        print("\n⚠️  Skipping actual API tests.")
        if os.getenv('FAL_KEY') == 'test_key_for_demo':
            print("   Using demo key - get a real API key from https://fal.ai/ to test actual background removal.")
        else:
            print("   Configure FAL_KEY to test actual background removal.")

    print("\n📊 Test Summary:")
    print(f"   - Dependencies: {'✅' if deps_ok else '❌'}")
    print(f"   - Environment: {'✅' if env_ok else '❌'}")
    print(f"   - Service Status: {'✅' if service_ok else '❌'}")
    print(f"   - Frontend Integration: {'✅' if frontend_ok else '❌'}")
    print(f"   - Image Loading: {'✅' if image_id else '❌'}")

    if frontend_ok:
        print("\n🎯 Frontend Integration Status:")
        print("   ✅ Background removal section will be visible in the web interface")
        print("   ✅ Users can upload images and see the background removal option")
        print("   ✅ API endpoints are working correctly")
        print("\n🌐 To test the frontend:")
        print("   1. Open http://localhost:8000 in your browser")
        print("   2. Upload an image")
        print("   3. You should see the 'Background Removal' section appear")
        print("   4. Click 'Remove Background' to test (requires real API key)")
    else:
        print("\n❌ Frontend Integration Issues:")
        print("   - Make sure the server is running: python3 run.py")
        print("   - Check that FAL_KEY is set in the environment")
        print("   - Verify the background removal router is included")


if __name__ == "__main__":
    asyncio.run(main())
