#!/usr/bin/env python3

import json
import sys
import os
import argparse
from pathlib import Path

def load_photo_standards(json_path):
    """Load photo standards from JSON file."""
    with open(json_path, 'r') as f:
        return json.load(f)

def find_standard_by_id(standards, standard_id):
    """Find a specific photo standard by ID."""
    for standard in standards:
        if standard['id'] == standard_id:
            return standard
    return None

def find_standard_by_country_doc(standards, country, doc_type):
    """Find a photo standard by country and document type."""
    for standard in standards:
        if standard['country'] == country and standard['docType'] == doc_type:
            return standard
    return None

def convert_to_pixels(value, units, dpi):
    """Convert measurements to pixels based on units and DPI."""
    if units == "pixel":
        return value
    elif units == "inch":
        return value * dpi
    elif units == "mm":
        return value * dpi / 25.4
    else:
        raise ValueError(f"Unknown units: {units}")

def generate_svg(standard, output_path):
    """Generate an SVG preview based on the photo standard."""
    dimensions = standard['dimensions']
    units = dimensions['units']

    # Get dimensions in the original units
    width = dimensions['pictureWidth']
    height = dimensions['pictureHeight']

    # Get face height and crown position if available
    face_height = dimensions.get('faceHeight', height * 0.7)  # Default to 70% of height if not specified
    crown_top = dimensions.get('crownTop', height * 0.1)  # Default to 10% from top if not specified

    # For display purposes, we'll scale everything to fit nicely in the SVG
    # Let's set a maximum SVG size
    max_svg_size = 300

    # Calculate scaling factor to fit within max_svg_size
    # Leave some margin for the labels and borders
    max_content_size = max_svg_size * 0.6
    scale_factor = min(max_content_size / width, max_content_size / height)
    if units == "pixel":
        # For pixel units, we need a more aggressive scaling
        scale_factor = min(max_content_size / width, max_content_size / height) * 0.3

    # Calculate SVG dimensions
    svg_width = width * scale_factor
    svg_height = height * scale_factor

    # Calculate border positions - center in the SVG
    border_x = (max_svg_size - svg_width) / 2
    border_y = (max_svg_size - svg_height) / 2
    border_width = svg_width
    border_height = svg_height

    # Calculate crown and chin positions
    crown_y = border_y + (crown_top * scale_factor)
    chin_y = crown_y + (face_height * scale_factor)

    # Format dimensions for display
    if units == "inch":
        width_display = f"{width} inch"
        height_display = f"{height} inch"
        face_height_display = f"{face_height:.1f} inch"
    elif units == "mm":
        width_display = f"{width} mm"
        height_display = f"{height} mm"
        face_height_display = f"{face_height:.1f} mm"
    else:  # pixel
        width_display = f"{int(width)} px"
        height_display = f"{int(height)} px"
        face_height_display = f"{int(face_height)} px"

    # Create the SVG content
    svg_content = f'''<svg width="{max_svg_size}" height="{max_svg_size}" viewBox="0 0 {max_svg_size} {max_svg_size}" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g>
<rect width="{max_svg_size}" height="{max_svg_size}" fill="white"/>

<!-- Photo border -->
<rect x="{border_x}" y="{border_y}" width="{border_width}" height="{border_height}" fill="white" stroke="#102043" stroke-width="2"/>

<!-- Horizontal measurement line at top -->
<line x1="{border_x}" y1="{border_y - 10}" x2="{border_x + border_width}" y2="{border_y - 10}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Horizontal measurement ticks at top -->
<line x1="{border_x}" y1="{border_y - 15}" x2="{border_x}" y2="{border_y - 5}" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="{border_x + border_width}" y1="{border_y - 15}" x2="{border_x + border_width}" y2="{border_y - 5}" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="{border_x + border_width/2}" y="{border_y - 20}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12">{width_display}</text>

<!-- No vertical measurement line on left as per design -->

<!-- Vertical measurement line on right -->
<line x1="{border_x + border_width + 10}" y1="{border_y}" x2="{border_x + border_width + 10}" y2="{border_y + border_height}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Vertical measurement ticks on right -->
<line x1="{border_x + border_width + 15}" y1="{border_y}" x2="{border_x + border_width + 5}" y2="{border_y}" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="{border_x + border_width + 15}" y1="{border_y + border_height}" x2="{border_x + border_width + 5}" y2="{border_y + border_height}" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="{border_x + border_width + 20}" y="{border_y + border_height/2}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12" transform="rotate(90, {border_x + border_width + 20}, {border_y + border_height/2})">{height_display}</text>

<!-- Integrated crown and chin markers with vertical measurement -->
<!-- Vertical measurement line -->
<line x1="{border_x - 15}" y1="{crown_y}" x2="{border_x - 15}" y2="{chin_y}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Crown marker - horizontal line extending from measurement line -->
<line x1="{border_x - 15}" y1="{crown_y}" x2="{border_x + border_width * 0.65}" y2="{crown_y}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Chin marker - horizontal line extending from measurement line -->
<line x1="{border_x - 15}" y1="{chin_y}" x2="{border_x + border_width * 0.65}" y2="{chin_y}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Face height text -->
<text x="{border_x - 25}" y="{(crown_y + chin_y)/2}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12" transform="rotate(-90, {border_x - 25}, {(crown_y + chin_y)/2})">{face_height_display}</text>

<!-- Vizaphoto.com branding inside the box -->
<rect x="{border_x}" y="{border_y + border_height - 25}" width="{border_width}" height="25" fill="#B3E0EA" fill-opacity="0.6"/>
<text x="{border_x + border_width/2}" y="{border_y + border_height - 8}" text-anchor="middle" fill="#102043" font-family="Arial" font-size="12">Vizaphoto.com</text>

<!-- Document info below the box -->
<text x="{border_x + border_width/2}" y="{border_y + border_height + 20}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12">{standard['country']} {standard['docType']} Photo</text>

</g>
</svg>
'''

    # Write the SVG to the output file
    with open(output_path, 'w') as f:
        f.write(svg_content)

    print(f"Generated SVG preview at {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Generate SVG preview for photo standards')
    parser.add_argument('--json', default='photo_standards.json', help='Path to photo standards JSON file')
    parser.add_argument('--id', help='ID of the photo standard to use')
    parser.add_argument('--country', help='Country for the photo standard')
    parser.add_argument('--doc-type', help='Document type for the photo standard')
    parser.add_argument('--output', default='preview.svg', help='Output SVG file path')

    args = parser.parse_args()

    # Find the photo_standards.json file
    json_path = args.json
    if not os.path.exists(json_path):
        # Try to find it in common locations
        possible_paths = [
            'photo_standards.json',
            'backend/face_detection/photo_standards.json',
            'webapp/src/app/data/photo-standards.json',
            'test_images/photo_standards.json',
            '../photo_standards.json',
            '../../photo_standards.json'
        ]

        for path in possible_paths:
            if os.path.exists(path):
                json_path = path
                break

        if not os.path.exists(json_path):
            print(f"Error: Could not find photo_standards.json file")
            sys.exit(1)

    # Load the photo standards
    standards = load_photo_standards(json_path)

    # Find the requested standard
    standard = None
    if args.id:
        standard = find_standard_by_id(standards, args.id)
    elif args.country and args.doc_type:
        standard = find_standard_by_country_doc(standards, args.country, args.doc_type)
    else:
        # Default to US passport if no specific standard is requested
        standard = find_standard_by_id(standards, "us_passport_photo")

    if not standard:
        print("Error: Could not find the requested photo standard")
        sys.exit(1)

    # Generate the SVG
    generate_svg(standard, args.output)

if __name__ == "__main__":
    main()
