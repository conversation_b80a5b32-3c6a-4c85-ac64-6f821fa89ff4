<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g>
<rect width="300" height="300" fill="white"/>

<!-- Photo border -->
<rect x="80.0" y="60.0" width="140.0" height="180.0" fill="white" stroke="#102043" stroke-width="2"/>

<!-- Horizontal measurement line at top -->
<line x1="80.0" y1="50.0" x2="220.0" y2="50.0" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Horizontal measurement ticks at top -->
<line x1="80.0" y1="45.0" x2="80.0" y2="55.0" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="220.0" y1="45.0" x2="220.0" y2="55.0" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="150.0" y="40.0" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12">35.0 mm</text>

<!-- No vertical measurement line on left as per design -->

<!-- Vertical measurement line on right -->
<line x1="230.0" y1="60.0" x2="230.0" y2="240.0" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Vertical measurement ticks on right -->
<line x1="235.0" y1="60.0" x2="225.0" y2="60.0" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="235.0" y1="240.0" x2="225.0" y2="240.0" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="240.0" y="150.0" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12" transform="rotate(90, 240.0, 150.0)">45.0 mm</text>

<!-- Integrated crown and chin markers with vertical measurement -->
<!-- Vertical measurement line -->
<line x1="65.0" y1="72.0" x2="65.0" y2="210.0" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Crown marker - horizontal line extending from measurement line -->
<line x1="65.0" y1="72.0" x2="171.0" y2="72.0" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Chin marker - horizontal line extending from measurement line -->
<line x1="65.0" y1="210.0" x2="171.0" y2="210.0" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Face height text -->
<text x="55.0" y="141.0" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12" transform="rotate(-90, 55.0, 141.0)">34.5 mm</text>

<!-- Vizaphoto.com branding inside the box -->
<rect x="80.0" y="215.0" width="140.0" height="25" fill="#B3E0EA" fill-opacity="0.6"/>
<text x="150.0" y="232.0" text-anchor="middle" fill="#102043" font-family="Arial" font-size="12">Vizaphoto.com</text>

<!-- Document info below the box -->
<text x="150.0" y="260.0" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12">European Union Visa Photo</text>

</g>
</svg>
