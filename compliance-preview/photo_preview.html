<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passport Photo Standards Preview</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #102043;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            width: 100%;
        }
        select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            min-width: 200px;
        }
        .preview {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            width: 100%;
            max-width: 600px;
            height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 5px;
            width: 100%;
            max-width: 600px;
        }
        .info h3 {
            margin-top: 0;
            color: #102043;
        }
        .info p {
            margin: 5px 0;
        }
        .logo {
            text-align: center;
            margin-top: 20px;
            color: #102043;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Passport Photo Standards Preview</h1>

        <div class="controls">
            <div>
                <label for="country">Country:</label>
                <select id="country" onchange="updateStandards()">
                    <option value="">Select a country</option>
                    <!-- Countries will be populated by JavaScript -->
                </select>
            </div>

            <div>
                <label for="docType">Document Type:</label>
                <select id="docType" onchange="updatePreview()">
                    <option value="">Select document type</option>
                    <!-- Document types will be populated based on country selection -->
                </select>
            </div>
        </div>

        <div class="preview" id="svgPreview">
            <p>Select a country and document type to see the preview</p>
        </div>

        <div class="info" id="photoInfo">
            <h3>Photo Information</h3>
            <p>Select a standard to see detailed information</p>
        </div>

        <div class="logo">Vizaphoto.com</div>
    </div>

    <script>
        // Store the photo standards data
        let photoStandards = [];

        // Fallback data for common photo standards
        const fallbackStandards = [
            {
                "id": "us_passport_photo",
                "text": "United States Passport 2x2 inches",
                "country": "United States",
                "docType": "Passport",
                "dimensions": {
                    "pictureWidth": 2.0,
                    "pictureHeight": 2.0,
                    "units": "inch",
                    "dpi": 300.0,
                    "faceHeight": 1.2,
                    "crownTop": 0.25
                },
                "backgroundColor": "#ffffff"
            },
            {
                "id": "eu_visa_photo",
                "text": "European Union Visa 35x45mm",
                "country": "European Union",
                "docType": "Visa",
                "dimensions": {
                    "pictureWidth": 35.0,
                    "pictureHeight": 45.0,
                    "units": "mm",
                    "dpi": 600.0,
                    "faceHeight": 34.0,
                    "crownTop": 3.0
                },
                "backgroundColor": "#eeeeee"
            },
            {
                "id": "bh_passport_photo",
                "text": "Bahrain Passport 4x6cm",
                "country": "Bahrain",
                "docType": "Passport",
                "dimensions": {
                    "pictureWidth": 40.0,
                    "pictureHeight": 60.0,
                    "units": "mm",
                    "dpi": 300.0,
                    "faceHeight": 38.0,
                    "crownTop": 6.0
                },
                "backgroundColor": "#ffffff"
            },
            {
                "id": "ca_passport_photo",
                "text": "Canada Passport 50x70mm",
                "country": "Canada",
                "docType": "Passport",
                "dimensions": {
                    "pictureWidth": 50.0,
                    "pictureHeight": 70.0,
                    "units": "mm",
                    "dpi": 300.0,
                    "faceHeight": 31.0,
                    "crownTop": 7.0
                },
                "backgroundColor": "#ffffff"
            },
            {
                "id": "uk_passport_photo",
                "text": "United Kingdom Passport 35x45mm",
                "country": "United Kingdom",
                "docType": "Passport",
                "dimensions": {
                    "pictureWidth": 35.0,
                    "pictureHeight": 45.0,
                    "units": "mm",
                    "dpi": 600.0,
                    "faceHeight": 29.0,
                    "crownTop": 5.0
                },
                "backgroundColor": "#eeeeee"
            },
            {
                "id": "in_passport_photo",
                "text": "India Passport 35x45mm",
                "country": "India",
                "docType": "Passport",
                "dimensions": {
                    "pictureWidth": 35.0,
                    "pictureHeight": 45.0,
                    "units": "mm",
                    "dpi": 600.0,
                    "faceHeight": 25.0,
                    "crownTop": 8.0
                },
                "backgroundColor": "#ffffff"
            }
        ];

        // Try to fetch the photo standards JSON
        fetch('photo-standards.json')
            .then(response => response.json())
            .then(data => {
                photoStandards = data;
                populateCountries();
            })
            .catch(error => {
                console.error('Error loading photo standards:', error);
                // Use fallback data instead
                photoStandards = fallbackStandards;
                populateCountries();

                // Show warning message
                const warningHtml = `
                    <div style="background-color: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #ffeeba;">
                        <strong>Note:</strong> Unable to load the full photo standards database due to browser security restrictions.
                        Using a limited set of common standards instead.
                        <br><br>
                        <strong>To see all standards:</strong>
                        <ul>
                            <li>Run a local web server in the 'research/scripts' folder, or</li>
                            <li>Use the Python http.server module: <code>python -m http.server</code></li>
                        </ul>
                    </div>
                `;

                document.querySelector('.container').insertAdjacentHTML('afterbegin', warningHtml);
            });

        // Populate the country dropdown
        function populateCountries() {
            const countrySelect = document.getElementById('country');
            const countries = new Set();

            photoStandards.forEach(standard => {
                countries.add(standard.country);
            });

            // Sort countries alphabetically
            const sortedCountries = Array.from(countries).sort();

            sortedCountries.forEach(country => {
                const option = document.createElement('option');
                option.value = country;
                option.textContent = country;
                countrySelect.appendChild(option);
            });
        }

        // Update document types based on selected country
        function updateStandards() {
            const country = document.getElementById('country').value;
            const docTypeSelect = document.getElementById('docType');

            // Clear previous options
            docTypeSelect.innerHTML = '<option value="">Select document type</option>';

            if (!country) return;

            // Find all document types for the selected country
            const docTypes = new Set();
            photoStandards.forEach(standard => {
                if (standard.country === country) {
                    docTypes.add(standard.docType);
                }
            });

            // Sort document types alphabetically
            const sortedDocTypes = Array.from(docTypes).sort();

            sortedDocTypes.forEach(docType => {
                const option = document.createElement('option');
                option.value = docType;
                option.textContent = docType;
                docTypeSelect.appendChild(option);
            });

            // Clear the preview
            document.getElementById('svgPreview').innerHTML = '<p>Select a document type</p>';
            document.getElementById('photoInfo').innerHTML = '<h3>Photo Information</h3><p>Select a standard to see detailed information</p>';
        }

        // Generate and display the SVG preview
        function updatePreview() {
            const country = document.getElementById('country').value;
            const docType = document.getElementById('docType').value;

            if (!country || !docType) return;

            // Find the matching standard
            const standard = photoStandards.find(s => s.country === country && s.docType === docType);

            if (!standard) {
                document.getElementById('svgPreview').innerHTML = '<p>No standard found for the selected combination</p>';
                return;
            }

            // Generate SVG
            const svg = generateSVG(standard);
            document.getElementById('svgPreview').innerHTML = svg;

            // Update info panel
            updateInfoPanel(standard);
        }

        // Update the information panel with details about the selected standard
        function updateInfoPanel(standard) {
            const dimensions = standard.dimensions;
            const infoPanel = document.getElementById('photoInfo');

            let html = `
                <h3>Photo Information: ${standard.text}</h3>
                <p><strong>Dimensions:</strong> ${dimensions.pictureWidth}${dimensions.units === 'inch' ? '"' : ' mm'} × ${dimensions.pictureHeight}${dimensions.units === 'inch' ? '"' : ' mm'}</p>
                <p><strong>Face Height:</strong> ${dimensions.faceHeight}${dimensions.units === 'inch' ? '"' : ' mm'}</p>
            `;

            if (dimensions.crownTop) {
                html += `<p><strong>Crown Position:</strong> ${dimensions.crownTop}${dimensions.units === 'inch' ? '"' : ' mm'} from top</p>`;
            }

            if (standard.backgroundColor) {
                html += `<p><strong>Background Color:</strong> ${standard.backgroundColor === '#ffffff' ? 'White' : standard.backgroundColor === '#eeeeee' ? 'Light Gray' : standard.backgroundColor}</p>`;
            }

            if (standard.officialLinks && standard.officialLinks.length > 0) {
                html += `<p><strong>Official References:</strong> ${standard.officialLinks.length} available</p>`;
            }

            if (standard.comments) {
                html += `<p><strong>Notes:</strong> ${standard.comments}</p>`;
            }

            infoPanel.innerHTML = html;
        }

        // Generate SVG based on the standard
        function generateSVG(standard) {
            const dimensions = standard.dimensions;
            const units = dimensions.units;

            // Get dimensions in the original units
            const width = dimensions.pictureWidth;
            const height = dimensions.pictureHeight;

            // Get face height and crown position if available
            const faceHeight = dimensions.faceHeight || height * 0.7;  // Default to 70% of height if not specified
            const crownTop = dimensions.crownTop || height * 0.1;  // Default to 10% from top if not specified

            // For display purposes, we'll scale everything to fit nicely in the SVG
            // Let's set a maximum SVG size
            const maxSvgSize = 300;

            // Calculate scaling factor to fit within maxSvgSize
            // Leave some margin for the labels and borders
            const maxContentSize = maxSvgSize * 0.6;
            let scaleFactor = Math.min(maxContentSize / width, maxContentSize / height);
            if (units === "pixel") {
                // For pixel units, we need a more aggressive scaling
                scaleFactor = Math.min(maxContentSize / width, maxContentSize / height) * 0.3;
            }

            // Calculate SVG dimensions
            const svgWidth = width * scaleFactor;
            const svgHeight = height * scaleFactor;

            // Calculate border positions - center in the SVG
            const borderX = (maxSvgSize - svgWidth) / 2;
            const borderY = (maxSvgSize - svgHeight) / 2;
            const borderWidth = svgWidth;
            const borderHeight = svgHeight;

            // Calculate crown and chin positions
            const crownY = borderY + (crownTop * scaleFactor);
            const chinY = crownY + (faceHeight * scaleFactor);

            // Format dimensions for display
            let widthDisplay, heightDisplay, faceHeightDisplay;
            if (units === "inch") {
                widthDisplay = `${width} inch`;
                heightDisplay = `${height} inch`;
                faceHeightDisplay = `${faceHeight.toFixed(1)} inch`;
            } else if (units === "mm") {
                widthDisplay = `${width} mm`;
                heightDisplay = `${height} mm`;
                faceHeightDisplay = `${faceHeight.toFixed(1)} mm`;
            } else {  // pixel
                widthDisplay = `${Math.round(width)} px`;
                heightDisplay = `${Math.round(height)} px`;
                faceHeightDisplay = `${Math.round(faceHeight)} px`;
            }

            // Create the SVG content
            return `<svg width="${maxSvgSize}" height="${maxSvgSize}" viewBox="0 0 ${maxSvgSize} ${maxSvgSize}" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g>
<rect width="${maxSvgSize}" height="${maxSvgSize}" fill="white"/>

<!-- Photo border -->
<rect x="${borderX}" y="${borderY}" width="${borderWidth}" height="${borderHeight}" fill="white" stroke="#102043" stroke-width="2"/>

<!-- Horizontal measurement line at top -->
<line x1="${borderX}" y1="${borderY - 5}" x2="${borderX + borderWidth}" y2="${borderY - 5}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Horizontal measurement ticks at top -->
<line x1="${borderX}" y1="${borderY - 8}" x2="${borderX}" y2="${borderY - 2}" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="${borderX + borderWidth}" y1="${borderY - 8}" x2="${borderX + borderWidth}" y2="${borderY - 2}" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="${borderX + borderWidth/2}" y="${borderY - 10}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12">${widthDisplay}</text>

<!-- Vertical measurement line on right -->
<line x1="${borderX + borderWidth + 5}" y1="${borderY}" x2="${borderX + borderWidth + 5}" y2="${borderY + borderHeight}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Vertical measurement ticks on right -->
<line x1="${borderX + borderWidth + 8}" y1="${borderY}" x2="${borderX + borderWidth + 2}" y2="${borderY}" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="${borderX + borderWidth + 8}" y1="${borderY + borderHeight}" x2="${borderX + borderWidth + 2}" y2="${borderY + borderHeight}" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="${borderX + borderWidth + 10}" y="${borderY + borderHeight/2}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12" transform="rotate(90, ${borderX + borderWidth + 10}, ${borderY + borderHeight/2})">${heightDisplay}</text>

<!-- Integrated crown and chin markers with vertical measurement -->
<!-- Vertical measurement line -->
<line x1="${borderX - 5}" y1="${crownY}" x2="${borderX - 5}" y2="${chinY}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Crown marker - horizontal line extending from measurement line -->
<line x1="${borderX - 5}" y1="${crownY}" x2="${borderX + borderWidth * 0.65}" y2="${crownY}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Chin marker - horizontal line extending from measurement line -->
<line x1="${borderX - 5}" y1="${chinY}" x2="${borderX + borderWidth * 0.65}" y2="${chinY}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Face height text -->
<text x="${borderX - 10}" y="${(crownY + chinY)/2}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12" transform="rotate(-90, ${borderX - 10}, ${(crownY + chinY)/2})">${faceHeightDisplay}</text>

<!-- Vizaphoto.com branding inside the box -->
<rect x="${borderX}" y="${borderY + borderHeight - 25}" width="${borderWidth}" height="25" fill="#B3E0EA" fill-opacity="0.6"/>
<text x="${borderX + borderWidth/2}" y="${borderY + borderHeight - 8}" text-anchor="middle" fill="#102043" font-family="Arial" font-size="12">Vizaphoto.com</text>

<!-- Document info below the box -->
<text x="${borderX + borderWidth/2}" y="${borderY + borderHeight + 20}" text-anchor="middle" fill="#0EAAC5" font-family="Arial" font-size="12">${standard.country} ${standard.docType} Photo</text>

</g>
</svg>`;
        }
    </script>
