import os
import re
from collections import OrderedDict
import requests
from bs4 import BeautifulSoup
import json

standards = ["af_passport_40x45_photo",
            "af_e_tazkira_3x4cm_photo",
            "af_passport_5x5_photo",
            "af_visa_35x45_photo",
            "af_visa_2x2_photo",
            "al_visa_47x36mm_photo",
            "dz_passport_photo",
            "dz_visa_photo",
            "dz_id_card_35x45mm_photo",
            "dz_residence_35x45mm_photo",
            "dz_work_permit_35x45mm_photo",
            "ao_visa_photo",
            "ar_dni_4x4_cm_photo",
            "ar_passport_4x4_cm_photo",
            "ar_visa_4x4_cm_photo",
            "ar_passport_15x15_inch_photo",
            "ar_visa_15x15_inch_photo",
            "am_evisa_photo",
            "am_visa_35x45mm_photo",
            "am_return_certificate_35x45mm_photo",
            "am_id_card_3x4cm_photo",
            "au_passport_photo",
            "au_visa_photo",
            "au_proof_of_age_card_35x45mm_photo",
            "au_nsw_driving_photo",
            "au_vic_driving_photo",
            "au_qld_driving_photo",
            "at_personalausweis_35x45mm_photo",
            "at_passport_photo",
            "at_visa_photo",
            "at_fuhrerschein_35x45mm_photo",
            "at_aufenthaltstitel_35x45mm_photo",
            "bh_passport_photo",
            "bh_visa_photo",
            "bh_id_card_4x6cm_photo",
            "bd_passport_40x50_photo",
            "bd_passport_55x45_photo",
            "bd_passport_45x35_photo",
            "bd_passport_30x25_photo",
            "bd_dual_nationality_40x50_photo",
            "bd_electronic_visa_35x45_photo",
            "bd_visa_45x35_photo",
            "bd_visa_37x37_photo",
            "bb_passport_photo",
            "by_passport_photo",
            "by_visa_photo",
            "be_id_card_45x35mm_photo",
            "be_kids_id_45x35mm_photo",
            "be_visa_photo",
            "be_passport_45x35mm_photo",
            "be_residence_permit_45x35mm_photo",
            "bj_visa_35x45mm_photo",
            "bj_passport_35x45mm_photo",
            "bj_passport_2x2in_photo",
            "bt_passport_45x35mm_photo",
            "bw_visa_3x4cm_photo",
            "bw_passport_photo",
            "bw_residence_permit_3x4cm_photo",
            "br_visa_online_vfsglobal_413x531_photo",
            "br_visa_online_431x531_photo",
            "br_carteira_de_identidade_3x4_photo",
            "br_visa_photo",
            "br_passport_online_431x531_photo",
            "br_passport_5x7_photo",
            "bn_passport_52x40mm_photo",
            "bn_sijil_darurat_photo",
            "bg_passport_photo",
            "bg_visa_photo",
            "bg_id_card_35x45mm_photo",
            "bf_passport_45x35mm_photo",
            "bf_visa_45x35mm_photo",
            "kh_passport_4x6_photo",
            "kh_visa_photo",
            "kh_visa_4x6_photo",
            "kh_visa_2x2_photo",
            "cm_passport_4x4_photo",
            "cm_passport_4x5_photo",
            "cm_passport_35x45_photo",
            "cm_passport_2x2inch_photo",
            "cm_visa_4x4_photo",
            "cm_visa_2x2inch_photo",
            "cm_visa_online_500x500px_photo",
            "ca_visa_photo",
            "ca_visa_temp_resident_photo",
            "ca_passport_photo",
            "ca_permanent_resident_card_online_1680x1200_photo",
            "ca_permanent_resident_card_photo",
            "ca_citizenship_photo",
            "td_passport_50x50mm_photo",
            "cn_visa_photo",
            "cn_passport_online_photo",
            "cn_passport_photo",
            "cn_visa_online_photo",
            "cn_green_card_33x48mm_photo",
            "co_visa_online_3x4cm_photo",
            "co_id_cedula_4x5_photo",
            "co_visa_residente_3x4cm_photo",
            "km_visa_photo",
            "km_id_card_photo",
            "cd_passport_35x45mm_photo",
            "cg_evisa_photo",
            "cg_visa_4x4cm_photo",
            "cg_visa_2x2in_photo",
            "cg_passport_35x45mm_photo",
            "cg_passport_4x4cm_photo",
            "cg_passport_2x2in_photo",
            "hr_osobna_iskaznica_35x45mm_photo",
            "hr_visa_photo",
            "cy_id_card_35x45mm_photo",
            "cy_passport_4x5cm_photo",
            "cy_visa_35x45mm_photo",
            "cy_id_card_4x3cm_photo",
            "cy_visa_2x2in_photo",
            "cz_passport_photo",
            "cz_passport_50x50mm_photo",
            "cz_visa_photo",
            "dk_visa_photo",
            "dk_passport_photo",
            "dj_visa_photo",
            "dj_passport_photo",
            "dj_id_card_photo",
            "dm_passport_45x38mm_photo",
            "eg_passport_4x6_photo",
            "eg_id_card_4x6_photo",
            "eg_visa_4x6cm_photo",
            "eg_passport_2x2_photo",
            "eg_visa_2x2_photo",
            "eg_military_service_certificate_6x4cm_photo",
            "gq_visa_35x54mm_photo",
            "ee_id_card_40x50mm_photo",
            "ee_passport_40x50mm_photo",
            "ee_aliens_passport_40x50mm_photo",
            "ee_residents_digital_identity_card_1300x1600px_photo",
            "ee_visa_40x50mm_photo",
            "ee_long_stay_visa_35x45mm_photo",
            "ee_weapons_permit_30x40mm_photo",
            "et_visa_photo",
            "et_visa_3x4_photo",
            "et_passport_3x4_photo",
            "et_origin_card_3x4_photo",
            "et_origin_card_2x3_photo",
            "eu_visa_photo",
            "fj_passport_35x45mm_photo",
            "fi_passport_photo",
            "fi_visa_photo",
            "fi_passport_500x653px_photo",
            "fi_id_henkilokortti_500x653px_photo",
            "fi_id_henkilokortti_36x47mm_photo",
            "fr_passport_photo",
            "fr_visa_photo",
            "fr_id_card_35x45mm_photo",
            "fr_permis_de_conduire_35x45mm_photo",
            "fr_demande_dasile_35x45_photo",
            "ga_evisa_photo",
            "ga_visa_35x35mm_photo",
            "ge_passport_photo",
            "ge_visa_photo",
            "ge_evisa_photo",
            "de_passport_photo",
            "de_ausweis_35x45mm_photo",
            "de_visa_photo",
            "de_driving_photo",
            "gh_visa_35x45mm_photo",
            "gh_passport_35x45mm_photo",
            "gh_visa_3x4cm_photo",
            "gr_passport_photo",
            "gr_visa_photo",
            "gr_id_card_36x36mm_photo",
            "gr_visa_us_photo",
            "gw_visa_3x4cm_photo",
            "gw_evisa_photo",
            "gy_passport_32x26mm_photo",
            "gy_passport_45x35mm_photo",
            "hk_passport_photo",
            "hk_visa_photo",
            "hu_passport_photo",
            "hu_visa_photo",
            "in_visa_photo",
            "in_visa_vfs_190px_photo",
            "in_passport_oci_online_photo",
            "in_passport_photo",
            "in_pan_photo",
            "in_voter_id_photo",
            "in_pio_photo",
            "in_birthcertificate_photo",
            "in_ffro_photo",
            "in_passport_bls_usa_photo",
            "in_dcga_online_photo",
            "id_visa_3x4_red_bg_photo",
            "id_passport_photo",
            "id_passport_white_photo",
            "id_visa_2x2inches_photo",
            "id_visa_photo",
            "ir_evisa_photo",
            "iq_passport_35x45mm_photo",
            "iq_visa_photo",
            "iq_id_card_photo",
            "iq_residence_35x45mm_photo",
            "iq_passport_5x5_photo",
            "ie_passport_online_photo",
            "ie_passport_photo",
            "ie_visa_photo",
            "ie_employment_permit_application_photo",
            "il_id_card_35x45mm_photo",
            "il_passport_photo",
            "il_passport_5x5cm_2x2in_photo",
            "il_visa_2_photo",
            "il_visa_photo",
            "it_carta_di_identita_35x45_photo",
            "it_passport_photo",
            "it_visa_photo",
            "it_passport_40x40_photo",
            "ci_visa_45x35mm_photo",
            "jm_passport35x45mm_photo",
            "jp_visa_photo",
            "jp_visa_3_photo",
            "jp_visa_2_photo",
            "jp_passport_photo",
            "jp_certificate_eligibility_photo",
            "jo_passport_photo",
            "jo_visa_35x45mm_photo",
            "jo_id_card_35x45mm_photo",
            "jo_residence_35x45mm_photo",
            "jo_work_permit_35x45mm_photo",
            "jo_passport_2x2_photo",
            "jo_id_card_2x2_photo",
            "kz_passport_photo",
            "kz_id_card_35x45mm_photo",
            "kz_passport_online_413x531_photo",
            "kz_id_card_online_413x531_photo",
            "kz_visa_photo",
            "ke_visa_eastern_africa_photo",
            "ke_passport_photo",
            "kw_passport_photo",
            "kw_visa_5x5cm_2x2in_photo",
            "kw_id_card_4x6cm_photo",
            "kw_residence_4x6_photo",
            "kw_work_permit_4x6cm_photo",
            "kg_passport_4x6cm_photo",
            "la_visa_4x6_photo",
            "la_visa_3x4_photo",
            "la_passport_4x6_photo",
            "la_adoption_visa_photo",
            "lb_visa_35x45mm_photo",
            "lb_passport_photo",
            "lb_id_card_35x45mm_photo",
            "lb_residence_35x45mm_photo",
            "lb_work_permit_35x45mm_photo",
            "ls_evisa_2x2inch_photo",
            "lr_passport_photo",
            "ly_visa_4x6cm_photo",
            "ly_passport_4x6cm_photo",
            "ly_id_card_4x6cm_photo",
            "lt_passport_35x45mm_photo",
            "mo_resident_identity_card_45x35mm_photo",
            "mo_passport_45x35mm_photo",
            "mo_visa_photo",
            "mg_visa_35x45mm_photo",
            "mg_visa_5x5cm_photo",
            "mg_visa_2x2inch_photo",
            "mw_passport_45x35mm_photo",
            "my_evisa_photo",
            "my_passport_photo",
            "my_visa_35x50_photo",
            "my_visa_35x50_white_photo",
            "my_passport_white_photo",
            "my_visa_photo",
            "my_visa_white_photo",
            "my_emgs_online_photo",
            "mv_passport_35x45mm_photo",
            "mt_passport_40x30mm_photo",
            "mt_passport_disabled_35x45mm_photo",
            "mr_visa_photo",
            "mr_passport_photo",
            "mr_id_card_photo",
            "mu_passport_35x45mm_photo",
            "mx_visa_photo",
            "mx_visa_permanent_residents_photo",
            "mx_visa_15x175_38x44_photo",
            "md_buletin_de_identitate_3x4cm_photo",
            "md_buletin_de_identitate_10x15cm_photo",
            "md_visa_35x45mm_photo",
            "md_dreptul_munca_sedere_50x60mm_photo",
            "mn_visa_3x4cm_photo",
            "mn_passport_35x45mm_photo",
            "mn_citizenship_4x6cm_photo",
            "mn_residence_permit_3x4cm_photo",
            "ma_visa_35x45mm_photo",
            "ma_passport_photo",
            "ma_nid_photo",
            "ma_residence_35x45mm_photo",
            "mz_visa_35x45mm_photo",
            "mm_visa_photo",
            "mm_permanent_residence_15x2_photo",
            "mm_visa_38x48_photo",
            "na_passport_37x52mm_photo",
            "na_passport_2x2_inch_photo",
            "na_visa_37x52mm_photo",
            "na_visa_35x45mm_photo",
            "np_online_visa_15x15in_photo",
            "np_visa_2_photo",
            "np_visa_photo",
            "np_passport_35x45_photo",
            "np_nrn_id_25x30_photo",
            "nl_passport_photo",
            "nl_visa_photo",
            "nl_id_kaart_35x45mm_photo",
            "nl_rijbewijs_35x45_photo",
            "nz_passport_el_photo",
            "nz_visa_online_photo",
            "nz_passport_photo",
            "nz_visa_photo",
            "nz_firearms_licence_35x45mm_photo",
            "nz_refugee_travel_document_35x45mm_photo",
            "nz_evidence_of_age_document_35x45mm_photo",
            "ne_visa_2x2inch_photo",
            "ng_visa_35x45mm_photo",
            "no_passport_photo",
            "no_visa_photo",
            "om_passport_4x6cm_photo",
            "om_visa_4x6cm_photo",
            "om_id_card_4x6cm_photo",
            "om_residence_4x6cm_photo",
            "om_work_permit_4x6cm_photo",
            "pk_id_nadra_babies_photo",
            "pk_id_card_photo",
            "pk_id_card_2_photo",
            "pk_id_card_3_photo",
            "pk_origin_card_photo",
            "pk_family_registration_certificate_photo",
            "pk_visa_photo",
            "pk_visa_2x2_photo",
            "ps_passport_35x45mm_photo",
            "ps_visa_3x4cm_photo",
            "ps_id_card_35x45mm_photo",
            "pg_passport_35x45mm_photo",
            "pg_citizenship_35x45mm_photo",
            "ph_visa_2x2inches_photo",
            "ph_rush_id_photo",
            "ph_1x1_photo",
            "ph_passport_45x35mm_photo",
            "ph_visa_35x45mm_photo",
            "ph_cir_25x25mm_photo",
            "ph_acknowledgement_of_employment_contracts_3x4cm_photo",
            "pl_passport_photo",
            "pl_dowod_osobisty_492x633_photo",
            "pl_dowod_osobisty_35x45mm_photo",
            "pl_visa_photo",
            "pr_bilhete_de_identidade_32x32mm_photo",
            "pt_passport_photo",
            "pt_visa_photo",
            "pr_cartao_de_cidadao_3x4cm_photo",
            "pt_visa_id_photo",
            "qa_visa_38x48mm_photo",
            "qa_passport_2x2in_photo",
            "qa_passport_38x48mm_photo",
            "qa_id_card_38x48mm_photo",
            "ro_cartea_de_identitate_3x4cm_photo",
            "ro_visa_photo",
            "ru_passport_int_photo",
            "ru_passport_int2_photo",
            "ru_passport_photo",
            "ru_passport_gosuslugi_photo",
            "ru_passport_head12mm_photo",
            "ru_pension_photo",
            "ru_driving_gosuslugi_photo",
            "ru_army_photo",
            "ru_work_photo",
            "ru_medical_photo",
            "ru_temp_resid_photo",
            "ru_student_photo",
            "ru_student_25x35_photo",
            "ru_visa_photo",
            "ru_evisa_photo",
            "ru_visa_vfs_35x45mm_photo",
            "ru_okhotnichiy_bilet_3x4cm_photo",
            "ru_krasnoyarsk_2019_photo",
            #"ru_world_cup_2018_fan_id_photo",
            "rw_visa_eastern_africa_online_photo",
            "rw_visa_eastern_africa_photo",
            "kn_passport_photo",
            "ws_visa_45x35mm_photo",
            "ws_passport_45x35mm_photo",
            "sa_passport_4x6cm_photo",
            "sa_evisa_200x200px_photo",
            "sa_visa_2x2in_photo",
            "sa_id_card_4x6cm_photo",
            "sa_work_permit_4x6cm_photo",
            "rs_visa_photo",
            "sc_passport_35x45mm_photo",
            "sl_visa_35x45mm_photo",
            "sg_visa_online_photo",
            "sg_passport_online_photo",
            "sg_passport_offline_photo",
            "sg_visa_photo",
            "sk_id_card_30x35_photo",
            "sk_visa_photo",
            "so_visa_35x45mm_photo",
            "so_id_card_photo",
            "za_passport_photo",
            "za_visa_photo",
            "kr_visa_photo",
            "kr_passport_35x45mm_photo",
            "kr_alien_registration_card_3x4_photo",
            "es_dni_32x26mm_photo",
            "es_passport_32x26mm_photo",
            "es_permiso_de_conduccion_32x26mm_photo",
            "es_tie_32x26mm_photo",
            "es_nie_32x26mm_photo",
            "es_tarjeta_de_armas_32x26mm_photo",
            "es_passport_40x53mm_photo",
            "es_visa_photo",
            "es_visa_us_photo",
            "lk_visa_35x45mm_photo",
            "lk_passport_35x45mm_photo",
            "lk_id_card_35x45mm_photo",
            "lk_dual_citizenship_35x45mm_photo",
            "lk_driving_licence_35x45mm_photo",
            "sd_passport_4x5cm_photo",
            "sd_id_card_4x5cm_photo",
            "sd_visa_4x5cm_photo",
            "sr_visa_online_photo",
            "sr_passport_45x35mm_photo",
            "sr_visa_45x35mm_photo",
            "sr_passport_50x35mm_photo",
            "se_visa_photo",
            "ch_visa_photo",
            "ch_id_card_35x45mm_photo",
            "sy_passport_2x2in_5x5cm_photo",
            "sy_residence_photo",
            "sy_passport_photo",
            "sy_id_card_photo",
            "sy_visa_photo",
            "tw_passport_photo",
            "tw_passport_from_us_photo",
            "tw_visa_photo",
            "tw_id_card_2x2inch_photo",
            "tw_id_card_30x25mm_photo",
            "tj_passport_35x45mm_photo",
            "th_visa_photo",
            "th_visa_us_photo",
            "th_visa_132_170_photo",
            "th_1x1_photo",
            "tg_visa_45x35mm_photo",
            "tg_passport_45x35mm_photo",
            "tn_passport_photo",
            "tn_visa_35x45mm_photo",
            "tn_id_card_35x45mm_photo",
            "tn_residence_35x45mm_photo",
            "tn_passport_2x2_photo",
            "tr_visa_photo",
            "tr_passport_photo",
            "tm_visa_5x6cm_photo",
            "tm_passport_30x40mm_photo",
            "ug_passport_photo",
            "ug_visa_photo",
            "ug_visa_eastern_africa_photo",
            "ua_passport_photo",
            "ua_passport_int_photo",
            "ua_driving_photo",
            "ua_visa_online_photo",
            "ua_visa_photo",
            "ae_visa_photo",
            "ae_visa_300_369_photo",
            "ae_passport_4x6cm_photo",
            "ae_id_card_4x6cm_photo",
            "ae_residence_4x6cm_photo",
            "gb_passport_photo",
            "gb_id_card_45x35mm_photo",
            "gb_passport_online_photo",
            "gb_visa_photo",
            "gb_driving_photo",
            "gb_gun_photo",
            "gb_oyster_travelcard_photo",
            "us_visa_photo",
            "us_passport_photo",
            "us_green_card_photo",
            "us_diversity_visa_lottery_photo",
            "us_citizenship_photo",
            "us_employment_authorization_photo",
            "us_ny_gun_photo",
            "us_metrocard_photo",
            "us_cibt_photo",
            "us_visacentral_photo",
            "us_travisa_photo",
            "us_visahq_photo",
            "us_visa_headquarters_photo",
            "uz_visa_35x45mm_photo",
            "vn_visa_photo",
            "vn_the_can_cuoc_cong_dan_3x4cm_photo",
            "vn_visa_2x2_photo",
            "ye_passport_6x4cm_photo",
            "ye_id_card_4x6cm_photo",
            "ye_visa_4x6cm_photo",
            "zm_visa_photo",
            "zm_visa_2x2inch_photo",
            "zm_passport_1_5x2inch_photo",
            "zw_visa_photo",
            "zw_passport_35x45mm_photo",
            "un_us_berkley_photo"]

data = []

re_size = re.compile(r'Width: ([0-9]*\.?[0-9]*)(mm|cm|in|pixel), Height: ([0-9]*\.?[0-9]*)(mm|cm|in|pixel)')
re_faceHeight = re.compile(r'Head height \(up to the top of the hair\): ([0-9]*\.?[0-9]*)(%|mm|cm|in|pixel)')
re_crownTop = re.compile(r'Distance from top the of the photo to the top of the hair: ([0-9]*\.?[0-9]*)(%|mm|cm|in|pixel)')
re_bottomEyeLine = re.compile(r'Distance from the bottom of the photo to the eye line: ([0-9]*\.?[0-9]*)(%|mm|cm|in|pixel)')
re_chinEyeLine = re.compile(r'Distance from the bottom of chin to the eye line: ([0-9]*\.?[0-9]*)(%|mm|cm|in|pixel)')

this_dir = os.path.dirname(os.path.realpath(__file__))

i = 0
for std in standards:
    i += 1
    print(f'Processing {std} .. [{i}/{len(standards)}]')
    local_file = os.path.join(this_dir, std + '.html')
    if os.path.isfile(local_file):
        with open(local_file, 'rb') as fp:
            page_content = fp.read().decode('utf8')
    else:
        url = 'https://visafoto.com/' + std
        page = requests.get(url)
        if page.status_code != 200:
            raise Exception('Unable to download "' + url + '"')
        page_content = page.content
        with open(local_file, 'wb') as fp:
            fp.write(page_content)

    soup = BeautifulSoup(page_content, 'html.parser')
    photo_title = soup.find_all('h1')[0].get_text().replace(' Photo Requirements and Online Tool', '')

    table = soup.find_all('table', class_="table table-bordered docTable")
    if table:
        entry = OrderedDict()
        entry['name'] = std
        entry['title'] = photo_title
        trs = table[0].find_all('tr')
        for row in trs:
            th = row.find_all('th')
            td = row.find_all('td')
            if th and td:
                prop_name = th[0].get_text()
                prop_value = td[0].get_text()

                if prop_name == 'Country':
                    entry['country']=prop_value
                elif prop_name == 'Document Type':
                    entry['docType']=prop_value
                elif prop_name == 'Background color':
                    style = td[0].find('span')['style']
                    m = re.search('background: ([#A-z0-9]+);', style)
                    entry['backgroundColor'] =  m.group(1)
                elif prop_name == 'Comments':
                    entry['comments'] = prop_value.strip()
                elif prop_name == 'Printable?':
                    entry['pritable'] = 'yes' in prop_value.lower()
                elif prop_name == 'Suitable for online submission?':
                    entry['pritable'] = 'yes' in prop_value.lower()
                elif prop_name == 'Web links to official documents':
                    entry['officialLinks'] = [a['href'] for a in td[0].find_all('a')]
                elif prop_name == 'Passport picture size' or prop_name == 'Size':
                    m = re_size.search(prop_value)
                    if m:
                        if m.group(2) != m.group(4):
                            raise Exception('Not same units')
                        prop_value = {
                            "pictureWidth": float(m.group(1)),
                            "pictureHeight": float(m.group(3)),
                            "units": m.group(2)
                        }
                    else:
                        raise Exception('Improve the REGEX for  "' + prop_value + '"')
                    entry['dimensions'] = prop_value
                elif prop_name == 'Resolution (dpi)':
                    entry['dimensions']['dpi'] = float(prop_value)
                elif prop_name == 'Image definition parameters':
                    a = prop_value
                    m1 = (re_faceHeight.search(prop_value), 'faceHeight')
                    m2 = (re_crownTop.search(prop_value), 'crownTop')
                    m3 = (re_bottomEyeLine.search(prop_value), 'bottomEyeLine')
                    m4 = (re_chinEyeLine.search(prop_value), 'chinEyeLine')
                    prop_value = {}
                    for m, v in [m1, m2, m3, m4]:
                        if m:
                            dim = float(m.group(1))
                            units = m.group(2)
                            if units == '%':
                                units = entry['dimensions']['units']
                                height = entry['dimensions']['pictureHeight']
                                dim = height * dim / 100.0
                            if 'units' in prop_value:
                                if prop_value['units'] != units:
                                    raise Exception('Bad units')
                            else:
                                prop_value['units'] = units
                            prop_value[v] = dim
                    if prop_value and len(prop_value) < 3:
                        raise Exception('Not all values are set for a definition')
                    entry['dimensions'].update(prop_value)
                elif prop_name == 'Required Size in Kilobytes':
                    pass
                else:
                    raise Exception('Unprocessed attribute')
        data.append(entry)
    else:
        print('Problem processing "' + std + '"')

data.append({
        "name": "cu_2x2in_photo",
        "Country": "Cuba",
        "Document Type": "Passport/Visa",
        "Size": {
            "width": 2,
            "height": 2,
            "units": "in",
            "faceHeight": 1.29,
            "bottomEyeLine": 1.18
        },
        "Resolution (dpi)": 300.0,
        "Background color": "#ffffff",
        "Printable?": "Yes",
        "Suitable for online submission?": "Yes",
        "Web links to official documents": [],
        "Comments": " "
    })

# Save all the data we have right now
out_file = os.path.join(this_dir, '../../webapp/src/assets/photo-standards.json')
with open(out_file, 'w') as fp:
    json.dump(data, fp, indent=4)

print("All data now saved. Time to start processing it.")
