<!-- Copyright (c) 2014-2024 Visafoto.com -->
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8"/>
<meta name="language" content="en"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<link rel="stylesheet" href="/css/bootstrap.3.4.1.min.css">

<link rel="stylesheet" href="/css/font-awesome.min.css">

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"/>
<link href="/img/favicon180.png" rel="apple-touch-icon-precomposed" sizes="180x180" />
<link href="/img/favicon192.png" rel="icon" sizes="192x192" />
<link rel="canonical" href="https://visafoto.com/at-personalausweis-35x45mm-photo" />
<style type="text/css">
body{padding-top: 30px !important; font-size: 16px;}
.jumbotron {padding: 30px 15px;}
@media (max-width: 429px){
 img#imgLogoTop{margin-top: 4px;max-width: 167px;max-height: 24px}   
 .langLong{display: none;}
 .langShort{display: inline;}
 span.liPhoneNumberNumber{display: none;}
}
@media (max-width: 961px) {li#liMakePhotoLink {display: none;}}
@media (min-width: 415px) {.langShort {display: none;} .langLong {display: inline;}}
@media (max-width: 359px) {li#liPhoneNumber {display: none;}}
@media (min-width: 360px) {li#liPhoneNumber {display: list-item;}}
.breadcrumb > li + li::before {padding: 0 5px;color: #ccc;content: ">";}
.zz-box {margin: 12px 0;padding: 8px;background-color: #fff;border: 1px solid #ddd;-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px;}
ul.qp {list-style-type: square}
ul.nav > li > a {padding-left: 10px; padding-right: 10px;}
</style>






    
    <title>Austria ID card photo 35x45 mm size, tool, requirements</title>
    
    
    <meta name="description" content="Make an Austrian ID card photo in 1 click and get a professional result: a 3.5x4.5 cm image with light background that meets all requirements for both online and in-person applications" />
    <meta property="og:description" content="Make an Austrian ID card photo in 1 click and get a professional result: a 3.5x4.5 cm image with light background that meets all requirements for both online and in-person applications" />
    
    <meta property="og:image" content="https://visafoto.com/img/docs/at_personalausweis_35x45mm.jpg" />
    <meta property="og:title" content="Austria ID card photo 35x45 mm size, tool, requirements" />
    <meta property="og:type" content="article" />
    <style type="text/css">
        .docTable th { text-align: left; }
    </style>

</head>
<body>
<nav class="navbar navbar-default navbar-fixed-top">
	<div class="container" style="padding-top: 5px; padding-left: 8px">
		<ul class="nav nav-pills" id="ulNavbar">			
			<li><a href="/" style="padding: 0px 5px 10px 1px"><img id="imgLogoTop" src="/img/logo209x30.png" style="width:209px;height:30px" alt="visafoto logo"></a></li>
      <li id="liMakePhotoLink"><a href="/"><i class="fa fa-user"></i><span class="hidden-xs hidden-sm"> Get photo</span></a></li>
      <li><a href="/requirements"><i class="fa fa-info-circle"></i><span class="hidden-xs hidden-sm"> Requirements</span></a></li>
			<li><a href="/contact"><i class="fa fa-envelope-o"></i><span class="hidden-xs hidden-sm"> Contacts</span></a></li>
      
      <li><a href="/7id/"><i class="fa fa-mobile" aria-hidden="true"></i>&nbsp;<i class="fa fa-apple" aria-hidden="true"></i>&nbsp;<i class="fa fa-android" aria-hidden="true"></i></a></li>
		</ul>
    <!-- div style="font-size: 1.2em;"><a href="/7id/"><i class="fa fa-mobile"></i> 7ID app: Visafoto on your phone!</a></div -->
	</div>
</nav>
<div style="margin-top: 20px;"></div>
<div class="jumbotron" style="padding-top: 20px; padding-bottom: 20px;">
	<h1 style="font-size: 32px; text-align: center">
    Get photo for Austrian ID card 35x45 mm (3.5x4.5 cm) in 2 seconds
</h1>
</div>
<div class="container" style="margin-bottom: 16px" id="divUploadForm">
  <div class="row" style="margin-top: 16px;">
      <div class="col-sm-12" style="padding: 8px 8px 12px 8px; -moz-box-shadow: 0 0 1px #a0a0a0; -webkit-box-shadow: 0 0 1px #a0a0a0; box-shadow: 0 0 1px #a0a0a0; border: 1px 1px 0px 1px solid #a0a0a0; border-radius: 4px; margin-bottom: 12px">
<!-- Entrance form -->
<div style="font-size:1.1em">
    Take an image with a smartphone or camera against any background, upload it here and instantly get a professional photo for your document: Austrian ID card 35x45 mm (3.5x4.5 cm) 
    <ul class="fa-ul text-muted" style="font-size: 0.9em; margin-top: 12px">
      <li><i class="fa-li fa fa-check-square-o"></i>Guaranteed to be accepted</li><li><i class="fa-li fa fa-check-square-o"></i>You will get your photo in several seconds</li><li><i class="fa-li fa fa-check-square-o"></i>Your result photo will fully match the requirements and example listed below (image size, head size, eye position, background color, size in kilobytes)</li>
    </ul>
</div>
<div class="row" >
    <div class="col-sm-6">
<form class="form-horizontal" role="form" id="formUpload">
<input type="hidden" id="selCountry" value="AT" />
<input type="hidden" id="selDocType" value="at_personalausweis_35x45mm" />
<div style="margin-top: 12px; margin-bottom: 12px;">
    <a href="#" onclick="return OptionsUI.show();">Additional options <i class="fa fa-chevron-down"></i></a>
    <div id="divOptions" style="display:none">
        <label ><input type="checkbox" name="tl" value="1" checked="checked" id="chkbxFixTilt"/> Fix head tilt</label>
        <br/><label ><input type="checkbox" name="bg" value="1" checked="checked" id="chkbxFixBg"/> Fix background</label>
        <br/><label ><input type="checkbox" name="ct" value="1" id="chkbxFixCont"/> Fix image contrast</label>
    </div>

</div>
</form>
<div id="divUpload">
    <div id="divUploadButton">
        <span class="btn btn-success btn-lg" style="position: relative; overflow: hidden; display: inline-block;">
			<span>Upload photo &amp; Continue</span>
            <i class="fa fa-angle-double-right"></i>
			<input id="inputImageFile" type="file" accept="image/*" name="imageFile" style="display: hidden; position: absolute; font-size: 72px; top: 0; right: 0; direction: ltr; opacity: 0; filter: alpha(opacity=0); height: 90px; line-height:90px;"/>
		</span>
    </div>
	<div id="divUploadError" class="modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                <a type="button" class="close" data-dismiss="modal" aria-hidden="true">x</a>
                <h4>Image Upload Error</h4>
                </div>
                <div class="modal-body"></div>
                <div class="modal-footer">
                    <a class="btn btn-primary" data-dismiss="modal" aria-hidden="true">Close</a>
                </div>
            </div>
        </div>
    </div>
</div>
    </div> <!-- col-sm-6 -->
</div> <!-- row -->
    

<!-- end of entrance -->
    </div> <!-- col -->
  </div> <!-- row -->

  <div class="row">
        <div class="col-sm-12" style="padding: 8px 8px 12px 8px; -moz-box-shadow: 0 0 1px #a0a0a0; -webkit-box-shadow: 0 0 1px #a0a0a0; box-shadow: 0 0 1px #a0a0a0; border: 1px 1px 0px 1px solid #a0a0a0; border-radius: 4px; ">
          <div class="container-fluid">
          <div class="row" >
              <div class="col-sm-2" style="text-align: center"></div>
              <div class="col-sm-4" style="text-align: center">
                  <h4 >Source</h4>
                  <img src="/img/source355x388.jpg" class="center-block img-responsive" alt="Source photo example that you need to take in order to make a passport or visa photo at visafoto.com"/>
              </div>
              <div class="col-sm-4" style="text-align: center">
                  <h4 >Result</h4>
                  <img src="/img/docs/at_personalausweis_35x45mm.jpg" class="center-block img-responsive" alt="Example of photo for __name__ with exact size specification" style="max-height: 388px;"/>
              </div>
              <div class="col-sm-2" style="text-align: center"></div>
          </div>
          </div>
      </div>
  </div>
</div>    

<script>
(function() {
    var f = false;
    var events = ['click','touchstart','keydown','scroll','mouseenter','mousemove'];
    function h() {
        if (f) { return; }
        f = true;
        for (var i = 0; i < events.length; i++) {
            window.removeEventListener(events[i], h);
        }
        $.ajax({
            url: '/api/human?t=' + (+ new Date()),
            type: 'POST',
            timeout: 3000
        }).always(function() {
            //
        });
    }
    for (var i = 0; i < events.length; i++) {
        window.addEventListener(events[i], h);
    }
})();
</script>
<script src="/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript">
  var OptionsUI = {
      show: function() {
          var div = $('#divOptions').get(0);
          if (!div) { return false; }
          div.style.display = (div.style.display === 'none') ? 'block' : 'none';
          return false;
      }
  };
</script>
<script src="/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/js/jquery-ui-1.10.3.custom.min.js"></script>
<script type="text/javascript" src="/js/jquery.iframe-transport_fileupload.min.js"></script>
<script type="text/javascript">
var noService = false;
function tempStop() { return false; }
function doFA() {
/*if ('KE' in {"MA":1,"ET":1,"EG":1,"DZ":1,"TR":1,"NP":1,"IN":1,"KE":1,"IR":1,"PH":1,"ID":1,"GH":1,"BR":1,"UZ":1,"MY":1,"CM":1,"JO":1,"KZ":1,"BD":1,"PK":1,"SN":1,"QA":1,"AF":1,"PE":1,"YE":1,"CN":1,"SO":1,"LK":1,"IQ":1,"AZ":1,"BJ":1,"SD":1,"BY":1,"KW":1,"CO":1,"KR":1,"GE":1,"EC":1,"VN":1,"KG":1,"TZ":1,"SY":1,"TN":1,"TG":1,"KH":1,"AR":1,"LY":1,"UG":1,"RW":1,"MD":1,"MM":1,"AM":1,"PS":1,"AL":1,"OM":1,"TJ":1,"BH":1,"TW":1,"TD":1,"NG":1,"MR":1,"BI":1,"BF":1,"LR":1,"LB":1,"MU":1,"SL":1,"VE":1,"TM":1,"CR":1,"BO":1,"MN":1,"CG":1,"BT":1,"DJ":1,"ZM":1,"ME":1,"FJ":1,"NE":1,"BA":1,"MK":1,"GM":1,"MV":1,"CU":1,"MW":1,"HN":1}) {
return true;
}*/
return false;
}
$(function() {
    const urlParams = new URLSearchParams(window.location.search);
    var fa = urlParams.get('fa');
    if (fa !== '0') {
        var doF = doFA();
        doF ||= (fa === '1');
        if (doF) {
            $('#formUpload').append('<input type="hidden" name="fr" value="1"/>');
        }
    }

    function optionsToParams () {
        var p = '';
        $('#formUpload').find('input[type=checkbox],input[type=hidden]').each(function() {
            var ok = (this.type != 'checkbox') || this.checked;
            if (!ok) { return; }
            p += '&' + this.name + '=' + this.value;
        });
        return p;
    }
    
    var isFileInputSupported = (function () {
        var el = document.createElement("input");
        el.type = "file";
        return !el.disabled;
    })();
    if (!isFileInputSupported) { return; }

    var sid = '87acc4c3f0fa411f887661e077ef7c5a';
    var UPLOAD_PERCENT = 20;
    var MAX_PROCESSING = 30*60;
    var url = '/upload?z=' + sid;
    
    var divUploadBox = $('#divUpload');
    var progressInterval = null;

    function urlAdd() {
        return '?z=' + sid + '&time=' + new Date().getTime();
    }

    function uploadError(str) {
        $('#divUploadButton').css('display', 'block');
        str = (str && (typeof str === 'string') && str.length) ?
            str : 'Network connection failure or server error. Please re-try the upload.';
        var mod = $('#divUploadError');
        mod.find('div.modal-body').text(str);
        mod.modal();
        return false;
    }

    function handleUploadResult(result) {
        if (result === true) {
            url = "/size/";
            window.location.href = '//' + window.location.host + url + sid + '?t=' + (+new Date());
        } else {
            if (progressInterval) { clearInterval(progressInterval); }
            $('#divUploadProgress').alert('close');
            uploadError(result);
        }
    }

    function afterUploadOK() {
        $('#pProgressCaption').text("Photo is being processed...");
        // Run animation
        $('#divUploadProgress .progress-bar').addClass('progress-bar-striped active');

        var timerStart = Date.now();

        function pollAgain() {
            var to = Math.round((Date.now() - timerStart) / 1000);
            if (to >= MAX_PROCESSING) {
                return handleUploadResult("Network connection failure or server error. Please re-try the upload.");
            }
            setTimeout(photoChecker, 1000);
        }

        function photoChecker () {
            $.ajax({
                url: '/api/photoDone' + urlAdd(),
                dataType: 'json',
                timeout: 37*1000
            }).
            done(function(data) {
                if (data && data.res) {
                    handleUploadResult(data.result);
                } else {
                    pollAgain();
                }
            }).
            fail(function() {
                pollAgain();
            });
        }
        setTimeout(photoChecker);

        var percent = UPLOAD_PERCENT;
        var started = + new Date();
        function setProgress() {
            var delta = ((+ new Date()) - started) / 1000;
            percent = UPLOAD_PERCENT + Math.round(delta) * 4;
            if (percent > 100) {
                percent = UPLOAD_PERCENT;
                started = + new Date();
            }       
            $('#divUploadProgress .progress-bar').css('width', percent + '%');
        }
        progressInterval = setInterval(setProgress, 1000);

        return false;
    }
    
    function createProgress() {
        $('<div class="alert alert-info" style="margin-top: 8px;">' +
            "<p id=\"pProgressCaption\">Photo is loading...</p>" +
            '<div class="progress" id="divUploadProgress" tabindex="100">' +
            '<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0;">' +
            '</div>' +
            '</div>' +
            '</div>').
            appendTo(divUploadBox);
        $('#divUploadProgress').focus();
        $('#divUploadProgress .progress-bar').removeClass('progress-bar-striped progress-bar-success active');        
    }

    $('#inputImageFile').fileupload({
      sequentialUploads: true,
      url: url,
      dataType: 'json',
      fail: function (e, data) {
          $('#divUploadProgress').alert('close');
          var m = null;
          if (data._response && data._response.jqXHR && (data._response.jqXHR.status == 413)) {
              m = "Your image is too big. Size down the image or select another one (its size should not exceed 9 MB).";
          } else if (data._response && data._response.jqXHR &&
              data._response.jqXHR.responseText && JSON) {
              try {               
                  m = JSON.parse(data._response.jqXHR.responseText);                 
                  m = m.result ? m.result: (m.error ? m.error.message : '');
              } catch (err) {
                  // do nothing
              }
          }
          return uploadError(m);
      },
      done: function (e, data) {
          if (!(data.result && data.result.url)) {
              $('#divUploadProgress').alert('close');
              return uploadError(null);
          }
          sid = data.result.url;
          afterUploadOK(data.result);
          return false;
      },
      add: function (e, data) {
          if (tempStop()) {
              return uploadError('Temporarily unavailable. Please re-try in 4 hours');
          }
          createProgress();
          data.url = url + '&docType=' + $('#selDocType').val() +
              optionsToParams();
          $('#divUploadButton').css('display', 'none');
          data.submit();
          return false;
      },
      progress: function (e, data) {
          var progress = Math.round((data.loaded / data.total) * UPLOAD_PERCENT);  
          $('#divUploadProgress .progress-bar').css('width', progress + '%');
      }
    });
});
</script>


<div class="container">
<h2>Requirements</h2>
<div>
    <table class="table table-bordered docTable">
        <tr><th>Country</th><td>Austria</td></tr>
        <tr><th>Document Type</th><td>ID card / Identification</td></tr>
        <tr><th>Size</th><td>Width: 35mm, Height: 45mm</td></tr>
        <tr><th>Resolution (dpi)</th><td>600</td></tr>
        
        <tr><th>Image definition parameters</th><td>Head height (up to the top of the hair): 34.5mm; Distance from top the of the photo to the top of the hair: 3mm</td></tr>
		<tr><th>Background color</th><td><span style="width: 30px; height: 14px; background: #eeeeee; border: 1px solid #808080;">&nbsp; &nbsp; &nbsp; &nbsp;</span></td></tr>
		<tr><th>Printable?</th><td>Yes</td></tr>
		<tr><th>Suitable for online submission?</th><td>Yes</td></tr>
        <tr><th>Web links to official documents</th><td><a href="https://www.bmeia.gv.at/fileadmin/user_upload/Vertretungen/London/Dokumente/Passport_Photographs_Criteria.pdf" rel="nofollow" target="_blank">https://www.bmeia.gv.at/fileadmin/user_upload/Vertretungen/London/Dokumente/Passport_Photographs_Criteria.pdf</a></td></tr>
        <tr><th>Comments</th><td> </td></tr>
    </table>
</div>

<p class="alert alert-success" style="margin-top: 12px">Do not worry about the photo size requirements. Visafoto.com guarantees compliance. It makes correct photos and fixes background.</p>


</div>


<div id="id7BottomBanner" class="container card bg-success" style="border-radius: 8px; margin-top: 16px">
  <div class="card-body ">
    <div>
      <h3><img src="/img/7id.png" style="width: 32px;height: 32px; border:none" alt="Download and install the Visafoto (7ID) app on your phone!"> 
        Download and install the Visafoto (7ID) app on your phone!</h3>
      <ul class="fa-ul">
        <li><i class="fa-li fa fa-check-square"></i>Access to complete photo history</li>
        <li><i class="fa-li fa fa-check-square"></i>Support for paid photos through the in-app chat</li>
        <li><i class="fa-li fa fa-check-square"></i>7ID includes free QR and barcode storage, free PIN code storage, and a free signature maker</li>
      </ul>
      <p>
        <a href="https://apps.apple.com/app/7id-passport-photos/id6447795199"><img src="/img/appstore.png" alt="Download 7ID App for iOS" width="121" /></a>
        &nbsp;
        <a href="https://play.google.com/store/apps/details?id=app.id7.free_passport_photo&referrer=vf1"><img src="/img/googleplay.png" alt="Download 7ID App for Android" width="135" /></a>
      </p>
      <p style="font-size: 1.2em; font-weight: 700;"><a href="/7id/?utm_source=vf1">Visit the 7ID website for more information &gt;</a></p>
    </div>
  </div>
</div>

<div id="div7idBottomMobile" class="container" style="display: none;">
  <div style="font-size: 0.9em; line-height: 14px;" style="text-align: center;">Download and install the Visafoto (7ID) app on your phone!</div>
  <img src="/img/7id.png" style="border: none; width: 48px; height: 48px; margin-right: 8px;" alt="Install 7ID now!">
  <a id="iosDownloadButton" target="_blank" href="https://apps.apple.com/app/7id-passport-photos/id6447795199" class="" style="margin-top: 10px;"><img src="/img/appstore.png" alt="Download on the App Store" style="" width="121" height="40"></a>
  <a id="androidDownloadButton" target="_blank" href="https://play.google.com/store/apps/details?id=app.id7.free_passport_photo" class="" style="margin-top: 10px"><img src="/img/googleplay.png" alt="Get it on Google Play" style="" width="135" height="40"></a>

</div>
<script>
function detectMobileOS() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    if (/android/i.test(userAgent)) {
        return 'android';
    }
    if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'ios';
    }
    return false;
}
if (typeof window.show7IDPopup === 'undefined') {
  window.show7IDPopup = true;
}
function enableMobileBottom() {
  var divBttm = document.getElementById("div7idBottomMobile");
  divBttm.style.display = "block";
  divBttm.style.position = "fixed";
  divBttm.style.bottom = "2px";
  divBttm.style.left = "2px";
  divBttm.style.width = "96%";
  divBttm.style.height = "78px";
  divBttm.style.lineHeight = "50px";
  divBttm.style.padding = "6px";
  divBttm.style.backgroundColor = "#ffffff";
  divBttm.style.border = "1px solid rgba(0,0,0,.125)"; 
  divBttm.style.borderRadius = ".25rem";
  divBttm.style.textAlign = "center";
}
if (window.show7IDPopup && (screen.width <= 480)) {
  var os = detectMobileOS();
  if (os == 'ios') {
    document.getElementById("androidDownloadButton").style.display = 'none';
  } else if (os == 'android') {
    document.getElementById("iosDownloadButton").style.display = 'none';
  }
  if (os !== false) {
    enableMobileBottom();
  }
}
</script>

<div class="panel panel-default" style="margin-top: 64px;">
    <div class="panel-body">&copy; 2025 Visafoto.com | 
        <a href="/">Home</a> |
        <a href="/requirements">Requirements</a> |
        <a href="/contact">Contacts</a> |
        <a href="/guide">Photographer's guide</a> |
		<a href="/terms">Terms of Service</a> |
        <a href="/langs">Other languages</a>
        | <a href="/blog">Blog</a>
        
| <a href="/visafoto-reviews">Visafoto Reviews</a>
| <a href="/visafoto-prices">Visafoto Prices</a>
| <a href="/is-visafoto-legitimate">Is Visafoto Legitimate?</a>
| <a href="/visafoto-free-alternative">Visafoto Free Alternative</a>
| <a href="/usa/diversity-visa-lottery-2025-dates-guide">The Diversity Visa Lottery 2025 Dates: A Time Guide</a>
| <a href="/free-green-card-lottery-photo-checker">Free Green Card Lottery photo checker</a>
| <a href="/diversity-visa-lottery-photo">DV Lottery photo</a>
| <a href="/usa-passport-photo-tool-problems">US Photo Tool problems</a>
| <a href="/zz-2x2-photo">2x2 inches photo</a>
| <a href="/us-passport-photo">US passport photo</a>
| <a href="/us-visa-photo">US visa photo</a>
| <a href="/zz-1x1-photo">Photo 1x1 inch</a>
| <a href="/passport-size-photo">Passport size photo</a>
| <a href="/bangladesh/passport-photo-size">Bangladesh passport photo size</a>
| <a href="/passport-photo-background-editor">Passport photo background editor</a>
| <a href="/35x45-photo">35x45 mm photo</a>
| <a href="/photo-4x6">4x6 cm photo</a>
| <a href="/usa/us-photo-tool-your-photo-does-not-need-cropping">Error: Your photo does not need cropping</a>
| <a href="/usa/dv-lottery-photo-errors">DV Lottery photo errors</a>
| <a href="/passport-photo-with-blue-background">Passport photo with blue background</a>
| <a href="/zz-30x40-photo">3x4 cm photo</a>
| <a href="/malaysia/passport-photo-size">Malaysia passport photo size</a>
| <a href="/ca-visa-photo">Canada Visa Photo</a>
| <a href="/usa/usa-visa-types">US visa types</a>
| <a href="/uae/passport-photo">Emirates passport photo</a>
| <a href="/passport-photo-printing">Printing passport photo</a>
| <a href="/baby-passport-photo">Baby passport photo</a>
| <a href="/singapore/visa-photo">Singapore visa photo</a>
| <a href="/saudi-arabia/visa-photo">Saudi Arabia visa photo</a>
| <a href="/usa/ds-160-form-how-to-fill">DS-160 Form</a>
| <a href="/producthunt">Promocode</a>

































    </div>
</div>


<script type="text/javascript">
    if ((typeof qpDoWebVisor == 'undefined') || qpDoWebVisor) {
        var _gaq = _gaq || [];
        _gaq.push(['_setAccount', 'UA-********-1']);
        _gaq.push(['_trackPageview']);
        (function() {
        var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
        })();
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
        (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
        ga('create', 'UA-********-1', 'qphoto.me');
        ga('require', 'displayfeatures');
        ga('send', 'pageview');
    }
</script>

</body>
</html>
