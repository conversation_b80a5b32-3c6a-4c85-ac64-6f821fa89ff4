# Payment Integration Roadmap - IntaSend Integration

## Overview
Integrate IntaSend payment gateway to monetize the passport photo maker app. Users will see previews for free but must pay to download high-quality digital photos and print layouts.

## Payment Strategy Analysis

### Current App Flow
1. **Upload** → Image processing
2. **Landmark Detection** → Face alignment
3. **Background Removal** (optional)
4. **Crop & Preview** → Watermarked preview (FREE)
5. **Print Layouts** → Tiled print preview (FREE)
6. **Download** → Currently free (NEEDS PAYWALL)

### Proposed Payment Flow
1. **Upload & Process** → FREE (as marketing/lead generation)
2. **Preview Generation** → FREE (shows value proposition)
3. **Payment Gateway** → Collect payment before downloads
4. **Download Access** → Only after successful payment

## IntaSend Integration Decision

### Recommendation: **Checkout Link API** (Backend-driven)

**Why Checkout Link over Payment Button:**

✅ **Better for our use case:**
- Backend control over payment flow
- Better integration with user management
- Easier to track payment status
- More secure (API keys stay on server)
- Better for generating receipts and order tracking

✅ **Technical advantages:**
- FastAPI backend can generate checkout links
- Can pre-populate user data (name, email)
- Better error handling and validation
- Easier to implement webhooks for payment confirmation

❌ **Payment Button limitations:**
- Frontend-heavy (less secure)
- Harder to integrate with user management
- Limited backend control

## Implementation Roadmap

### Phase 1: Foundation Setup (Week 1)
- [ ] **Environment Configuration**
  - Add IntaSend API keys to `.env`
  - Install `intasend-python` package
  - Configure test/production environments

- [ ] **Database Schema Design**
  - User management (name, email, phone)
  - Payment tracking (orders, transactions)
  - Download permissions

- [ ] **Payment Service Architecture**
  - Payment service class
  - Order management system
  - User session handling

### Phase 2: User Management (Week 1-2)
- [ ] **User Information Collection**
  - Name and email form before payment
  - Phone number (optional for M-Pesa)
  - User session management

- [ ] **Order System**
  - Order creation with unique IDs
  - Link orders to processed images
  - Order status tracking

### Phase 3: Payment Integration (Week 2)
- [ ] **IntaSend Service Implementation**
  - Checkout link generation
  - Payment status checking
  - Webhook handling for payment confirmation

- [ ] **Payment Flow UI**
  - Payment information form
  - Checkout redirect handling
  - Payment success/failure pages

### Phase 4: Access Control (Week 2-3)
- [ ] **Download Protection**
  - Secure download endpoints
  - Payment verification before download
  - Time-limited download links

- [ ] **User Dashboard**
  - Payment history
  - Download access management
  - Receipt generation

### Phase 5: Testing & Optimization (Week 3)
- [ ] **Testing Strategy**
  - Sandbox environment testing
  - Payment flow testing
  - Security testing

- [ ] **User Experience**
  - Error handling
  - Loading states
  - Mobile responsiveness

## Technical Architecture

### 1. Database Models

```python
# User Model
class User:
    id: str
    email: str
    first_name: str
    last_name: str
    phone_number: Optional[str]
    created_at: datetime

# Order Model
class Order:
    id: str
    user_id: str
    image_id: str
    amount: float
    currency: str
    status: str  # pending, paid, failed, expired
    intasend_ref: str
    checkout_url: str
    created_at: datetime
    paid_at: Optional[datetime]

# Download Permission Model
class DownloadPermission:
    id: str
    order_id: str
    user_id: str
    image_id: str
    download_type: str  # digital, print
    expires_at: datetime
    download_count: int
    max_downloads: int
```

### 2. Payment Service Structure

```python
class IntaSendService:
    def __init__(self):
        self.api_service = APIService(
            token=os.getenv('INTASEND_TOKEN'),
            publishable_key=os.getenv('INTASEND_PUBLISHABLE_KEY'),
            test=os.getenv('INTASEND_TEST_MODE', 'true').lower() == 'true'
        )
    
    def create_checkout_link(self, order: Order, user: User) -> str:
        """Generate IntaSend checkout link"""
        
    def check_payment_status(self, intasend_ref: str) -> dict:
        """Check payment status from IntaSend"""
        
    def handle_webhook(self, webhook_data: dict) -> bool:
        """Process IntaSend webhook notifications"""
```

### 3. Protected Download Endpoints

```python
@router.get("/download/digital/{order_id}")
async def download_digital_photo(order_id: str):
    """Download high-quality digital photo after payment verification"""
    
@router.get("/download/print/{order_id}")
async def download_print_layout(order_id: str):
    """Download print layout after payment verification"""
```

## Pricing Strategy

### Suggested Pricing (Kenyan Market)
- **Digital Photo**: KES 100 ($0.75)
- **Print Layout**: KES 150 ($1.10)
- **Bundle (Digital + Print)**: KES 200 ($1.50)

### International Pricing
- **Digital Photo**: $2.00
- **Print Layout**: $3.00
- **Bundle**: $4.00

## User Experience Flow

### 1. Free Preview Experience
```
Upload → Process → Preview (Watermarked) → "Get High Quality Version"
```

### 2. Payment Experience
```
Click "Download" → Enter Details → Select Package → Pay → Download
```

### 3. Post-Payment Experience
```
Payment Success → Email Receipt → Download Links → User Dashboard
```

## Security Considerations

### 1. Payment Security
- API keys stored securely in environment variables
- HTTPS for all payment-related endpoints
- Webhook signature verification
- Order expiration (24 hours)

### 2. Download Security
- Time-limited download URLs
- Download count limits
- User authentication for downloads
- Secure file serving

### 3. Data Protection
- User data encryption
- GDPR compliance considerations
- Payment data handling (PCI compliance)

## Integration Points

### 1. Current App Integration
- **Preview endpoints**: Add payment CTA
- **Download endpoints**: Add payment verification
- **Print functionality**: Protect behind paywall
- **User interface**: Add payment forms and status

### 2. IntaSend Integration
- **Checkout API**: Generate payment links
- **Webhook handling**: Process payment confirmations
- **Status checking**: Verify payment status
- **Error handling**: Handle payment failures

## Success Metrics

### 1. Conversion Metrics
- Preview-to-payment conversion rate
- Payment completion rate
- Average order value
- User retention rate

### 2. Technical Metrics
- Payment processing time
- Download success rate
- Error rates
- System uptime

## Risk Mitigation

### 1. Technical Risks
- **Payment failures**: Implement retry mechanisms
- **Webhook delays**: Add status polling backup
- **Download issues**: Implement download retry
- **Security breaches**: Regular security audits

### 2. Business Risks
- **Low conversion**: A/B test pricing and UX
- **Payment disputes**: Clear refund policy
- **Competition**: Unique value proposition
- **Regulatory**: Compliance with local laws

## Implementation Timeline

### Week 1: Foundation
- Environment setup
- Database design
- User management system

### Week 2: Payment Integration
- IntaSend service implementation
- Payment flow UI
- Webhook handling

### Week 3: Access Control
- Download protection
- User dashboard
- Testing and optimization

### Week 4: Launch Preparation
- Security audit
- Performance testing
- Documentation
- Go-live preparation

## Next Steps

1. **Confirm pricing strategy** for your target market
2. **Set up IntaSend account** and get API keys
3. **Choose database solution** (SQLite for start, PostgreSQL for scale)
4. **Design user interface** for payment flow
5. **Plan testing strategy** with sandbox environment

## Questions for Decision

1. **Pricing**: What pricing feels right for your target market?
2. **Database**: SQLite (simple) or PostgreSQL (scalable)?
3. **User accounts**: Simple session-based or full user registration?
4. **Payment methods**: M-Pesa + Cards or cards only?
5. **Download limits**: How many downloads per purchase?

---

**Recommendation**: Start with **Checkout Link API** implementation as it provides better control and security for your use case. The payment button can be added later for simpler flows if needed.
