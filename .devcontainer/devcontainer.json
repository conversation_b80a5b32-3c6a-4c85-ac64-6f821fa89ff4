{"name": "PPP-DevEnv", "dockerFile": "../Dockerfile.devenv", "context": ".", "containerUser": "${env:USER}", "remoteUser": "${env:USER}", "customizations": {"vscode": {"extensions": ["llvm-vs-code-extensions.vscode-clangd", "xaver.clang-format", "twxs.cmake", "ms-python.python", "vadimcn.vscode-lldb", "matepek.vscode-catch2-test-adapter", "streetsidesoftware.code-spell-checker", "eamodio.gitlens", "DavidAnson.vscode-markdownlint", "natewallace.angular2-inline", "jakethashi.vscode-angular2-emmet", "angular.ng-template", "vscode-icons-team.vscode-icons", "esbenp.prettier-vscode", "Orta.vscode-jest"]}, "settings": {"terminal.integrated.shell.linux.profile": "/bin/bash"}}, "workspaceFolder": "/src", "workspaceMount": "source=${localWorkspaceFolder},target=/src,type=bind", "mounts": ["source=ppp-vscode-extensions,target=/home/<USER>/.vscode-server/extensions,type=volume", "source=/,target=/hostfs,type=bind,consistency=delegated"], "postCreateCommand": "sudo chown -R ${env:USER} /src", "build": {"args": {"USER_NAME": "${env:USER}", "USER_UID": "1000", "USER_GID": "1000", "REPO_ROOT": "${localWorkspaceFolder}"}}, "runArgs": ["-v", "/var/run/docker.sock:/var/run/docker.sock", "-h", "ppp-devenv"]}