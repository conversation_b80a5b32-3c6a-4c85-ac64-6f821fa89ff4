input_stream: "input_video"
output_stream: "multi_face_landmarks"
output_stream: "face_landmarks_presence"

# Defines side packets for further use in the graph.
node {
    calculator: "ConstantSidePacketCalculator"
    output_side_packet: "PACKET:0:num_faces"
    output_side_packet: "PACKET:1:with_attention"
    output_side_packet: "PACKET:2:use_prev_landmarks"
    node_options: {
        [type.googleapis.com/mediapipe.ConstantSidePacketCalculatorOptions]: {
            packet { int_value: 1 }
            packet { bool_value: true }
            packet { bool_value: false }
        }
    }
}

# Subgraph that detects faces and corresponding landmarks.
node {
    calculator: "FaceLandmarkFrontCpu"
    input_stream: "IMAGE:input_video"
    input_side_packet: "NUM_FACES:num_faces"
    input_side_packet: "WITH_ATTENTION:with_attention"
    input_side_packet: "USE_PREV_LANDMARKS:use_prev_landmarks"
    output_stream: "LANDMARKS:multi_face_landmarks"
    output_stream: "ROIS_FROM_LANDMARKS:face_rects_from_landmarks"
    output_stream: "DETECTIONS:face_detections"
    output_stream: "ROIS_FROM_DETECTIONS:face_rects_from_detections"
}

node {
    calculator: "PacketPresenceCalculator"
    input_stream: "PACKET:multi_face_landmarks"
    output_stream: "PRESENCE:face_landmarks_presence"
}