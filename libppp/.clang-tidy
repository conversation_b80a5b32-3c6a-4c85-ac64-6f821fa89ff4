Checks: >
  *,
  -abseil-*,
  -altera-*,
  -bugprone-easily-swappable-parameters,
  -bugprone-implicit-widening-of-multiplication-result,
  -bugprone-narrowing-conversions,
  -cert-err58-cpp,
  -cert-err60-cpp,
  -cppcoreguidelines-avoid-goto,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-avoid-non-const-global-variables,
  -cppcoreguidelines-init-variables,
  -cppcoreguidelines-macro-usage,
  -cppcoreguidelines-narrowing-conversions,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -cppcoreguidelines-owning-memory,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-bounds-constant-array-index,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-type-reinterpret-cast,
  -cppcoreguidelines-pro-type-vararg,
  -cppcoreguidelines-special-member-functions,
  -fuchsia-default-arguments-calls,
  -fuchsia-default-arguments-declarations,
  -fuchsia-overloaded-operator,
  -fuchsia-statically-constructed-objects,
  -google-build-using-namespace,
  -google-default-arguments,
  -google-readability-avoid-underscore-in-googletest-name,
  -google-readability-braces-around-statements,
  -google-readability-namespace-comments,
  -hicpp-avoid-goto,
  -hicpp-exception-baseclass,
  -hicpp-braces-around-statements,
  -hicpp-no-array-decay,
  -hicpp-signed-bitwise,
  -hicpp-special-member-functions,
  -hicpp-uppercase-literal-suffix,
  -hicpp-vararg,
  -llvm-namespace-comment,
  -llvmlibc-*,
  -llvm-include-order,
  -misc-non-private-member-variables-in-classes,
  -modernize-avoid-bind,
  -modernize-concat-nested-namespaces,
  -modernize-use-nodiscard,
  -modernize-use-trailing-return-type,
  -readability-identifier-length,
  -readability-implicit-bool-conversion,
  -readability-magic-numbers,
  -readability-redundant-access-specifiers,
  -readability-uppercase-literal-suffix,

HeaderFilterRegex:   '.*\.(h|hpp)$'

CheckOptions:
  - { key: readability-identifier-naming.ProtectedMemberPrefix, value: m_ }
  - { key: readability-identifier-naming.PrivateMemberPrefix, value: m_ }
  - { key: readability-identifier-naming.StaticConstantPrefix, value: s_ }
  - { key: readability-identifier-naming.StaticVariablePrefix, value: s_ }
  - { key: readability-identifier-naming.AbstractClassCase, value: CamelCase }
  - { key: readability-identifier-naming.ClassCase, value: CamelCase }
  - { key: readability-identifier-naming.ClassConstantCase, value: CamelCase }
  - { key: readability-identifier-naming.ClassMemberCase, value: camelBack }
  - { key: readability-identifier-naming.ClassMethodCase, value: camelBack }
  - { key: readability-identifier-naming.ConstantParameterCase, value: camelBack }
  - { key: readability-identifier-naming.ConstantPointerParameterCase, value: camelBack }
  - { key: readability-identifier-naming.ConstexprFunctionCase, value: CamelCase }
  - { key: readability-identifier-naming.ConstexprMethodCase, value: CamelCase }
  - { key: readability-identifier-naming.ConstexprVariableCase, value: UPPER_CASE }
  - { key: readability-identifier-naming.EnumCase, value: CamelCase }
  - { key: readability-identifier-naming.EnumConstantCase, value: UPPER_CASE }
  - { key: readability-identifier-naming.FunctionCase, value: camelBack }
  - { key: readability-identifier-naming.GlobalConstantCase, value: CamelCase }
  - { key: readability-identifier-naming.GlobalConstantPointerCase, value: CamelCase }
  - { key: readability-identifier-naming.GlobalFunctionCase, value: camelBack }
  - { key: readability-identifier-naming.GlobalPointerCase, value: CamelCase }
  - { key: readability-identifier-naming.GlobalVariableCase, value: CamelCase }
  - { key: readability-identifier-naming.LocalConstantCase, value: camelBack }
  - { key: readability-identifier-naming.LocalConstantPointerCase, value: camelBack }
  - { key: readability-identifier-naming.LocalPointerCase, value: camelBack }
  - { key: readability-identifier-naming.LocalVariableCase, value: camelBack }
  - { key: readability-identifier-naming.MacroDefinitionCase, value: UPPER_CASE }
  - { key: readability-identifier-naming.MemberCase, value: camelBack }
  - { key: readability-identifier-naming.MethodCase, value: camelBack }
  - { key: readability-identifier-naming.NamespaceCase, value: camelBack }
  - { key: readability-identifier-naming.ParameterCase, value: camelBack }
  - { key: readability-identifier-naming.ParameterPackCase, value: camelBack }
  - { key: readability-identifier-naming.PointerParameterCase, value: camelBack }
  - { key: readability-identifier-naming.PrivateMemberCase, value: camelBack }
  - { key: readability-identifier-naming.PrivateMethodCase, value: camelBack }
  - { key: readability-identifier-naming.ProtectedMemberCase, value: camelBack }
  - { key: readability-identifier-naming.ProtectedMethodCase, value: camelBack }
  - { key: readability-identifier-naming.PublicMemberCase, value: camelBack }
  - { key: readability-identifier-naming.PublicMethodCase, value: camelBack }
  - { key: readability-identifier-naming.ScopedEnumConstantCase, value: UPPER_CASE }
  - { key: readability-identifier-naming.StaticConstantCase, value: camelBack }
  - { key: readability-identifier-naming.StaticVariableCase, value: camelBack }
  - { key: readability-identifier-naming.StructCase, value: CamelCase }
  - { key: readability-identifier-naming.TemplateParameterCase, value: CamelCase }
  - { key: readability-identifier-naming.TemplateTemplateParameterCase, value: camelBack }
  - { key: readability-identifier-naming.TypeAliasCase, value: CamelCase }
  - { key: readability-identifier-naming.TypedefCase, value: CamelCase }
  - { key: readability-identifier-naming.TypeTemplateParameterCase, value: CamelCase }
  - { key: readability-identifier-naming.UnionCase, value: CamelCase }
  - { key: readability-identifier-naming.ValueTemplateParameterCase, value: CamelCase }
  - { key: readability-identifier-naming.VariableCase, value: camelBack }
  - { key: readability-identifier-naming.VirtualMethodCase, value: camelBack }
#
  - { key: misc-assert-side-effect.CheckFunctionCalls, value: 1 }
#
  - { key: misc-assert-side-effect.AssertMacros, value: assert }
#
  - { key: google-readability-function-size.LineThreshold, value: 300 }
#
  - { key: readability-function-cognitive-complexity.Threshold, value: 100 }
#
  - { key: readability-implicit-bool-conversion.AllowPointerConditions, value: true }