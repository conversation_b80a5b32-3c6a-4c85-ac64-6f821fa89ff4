### Added by <PERSON><PERSON>'s <PERSON><PERSON> Compile Commands Extractor: https://github.com/hedronvision/bazel-compile-commands-extractor
# The external link: Differs on Windows vs macOS/Linux, so we can't check it in. The pattern needs to not have a trailing / because it's a symlink on macOS/Linux.
/external
# Bazel output symlinks: Same reasoning as /external. You need the * because people can change the name of the directory your repository is cloned into, changing the bazel-<workspace_name> symlink.
/bazel-*
# Compiled output -> don't check in
/compile_commands.json
# Directory where clangd puts its indexing work
/.cache/
