
libtclapincludedir = $(includedir)/tclap

libtclapinclude_HEADERS = \
			 CmdLineInterface.h \
			 ArgException.h \
			 CmdLine.h \
			 XorHandler.h \
			 MultiArg.h \
			 UnlabeledMultiArg.h \
			 ValueArg.h \
			 UnlabeledValueArg.h \
			 Visitor.h Arg.h \
			 HelpVisitor.h \
			 SwitchArg.h \
			 MultiSwitchArg.h \
			 VersionVisitor.h \
			 IgnoreRestVisitor.h \
			 CmdLineOutput.h \
			 StdOutput.h \
			 DocBookOutput.h \
			 ZshCompletionOutput.h \
			 OptionalUnlabeledTracker.h \
			 Constraint.h \
			 ValuesConstraint.h \
			 ArgTraits.h \
			 StandardTraits.h \
			 sstream.h
