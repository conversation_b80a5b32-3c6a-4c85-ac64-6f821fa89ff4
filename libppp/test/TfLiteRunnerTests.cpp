#include "TestHelpers.h"
#include "TfLiteRunner.h"
#include <gtest/gtest.h>

namespace ppp {

// TEST(TfLiteRunner, FaceDectectionModelLoads)
// {
//     const auto pathToModel = resolvePath("libppp/share/face_detection_full_range_sparse.tflite");
//     auto tfLiteRunner = TfLiteRunner(pathToModel);
// }
// TEST(TfLiteRunner, FaceMeshModelLoads)
// {
//     const auto pathToModel = resolvePath("libppp/share/face_landmark_with_attention.tflite");
//     auto tfLiteRunner = TfLiteRunner(pathToModel);
// }
// TEST(TfLiteRunner, SelfieSegmentationModelLoads)
// {
//     const auto pathToModel = resolvePath("libppp/share/selfie_segmentation.tflite");
//     auto tfLiteRunner = TfLiteRunner(pathToModel);
// }

} // namespace ppp
