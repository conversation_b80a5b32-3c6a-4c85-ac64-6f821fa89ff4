BasedOnStyle: WebKit

#
# easy ones
#
BreakBeforeBraces: Mozilla
ColumnLimit: 120
FixNamespaceComments: true
AlwaysBreakTemplateDeclarations: true
IndentCaseLabels: true
ConstructorInitializerIndentWidth: 0
SortIncludes: true
SortUsingDeclarations: true
NamespaceIndentation: None

#
# argument / parameter breaks
#
PenaltyBreakAssignment: 100
PenaltyExcessCharacter: 50
PenaltyReturnTypeOnItsOwnLine: 10000
PenaltyBreakFirstLessLess: 0
PenaltyBreakBeforeFirstCallParameter: 100000
BinPackParameters: false
BinPackArguments: false
AlignAfterOpenBracket: true
ExperimentalAutoDetectBinPacking: false
AllowAllParametersOfDeclarationOnNextLine: false

#
# tricky ones
#
PointerAlignment: Middle
BreakBeforeTernaryOperators: true

AllowShortFunctionsOnASingleLine: false
AllowShortBlocksOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AllowShortIfStatementsOnASingleLine: false