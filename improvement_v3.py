"""
Improved passport photo processor with face alignment (Version 3)
"""
import os
import cv2
import json
import numpy as np
import argparse
import pickle
import math
from pathlib import Path

class PhotoStandard:
    """
    Class representing a passport photo standard
    """
    def __init__(self,
                 picture_width,
                 picture_height,
                 face_height,
                 crown_top=None,
                 bottom_eye_line=None,
                 resolution=300,
                 units="mm"):
        """
        Initialize a photo standard

        Args:
            picture_width: Width of the photo
            picture_height: Height of the photo
            face_height: Height of the face (crown to chin)
            crown_top: Distance from the top of the photo to the crown (optional)
            bottom_eye_line: Distance from the bottom of the photo to the eye line (optional)
            resolution: Resolution in DPI
            units: Units of measurement (mm, inch, pixel)
        """
        self.picture_width = picture_width
        self.picture_height = picture_height
        self.face_height = face_height
        self.resolution = resolution
        self.units = units

        # If crown_top is not provided but bottom_eye_line is, calculate crown_top
        if crown_top is None and bottom_eye_line is not None:
            # Convert eye line to bottom distance into crown top distance
            alpha = 0.477196  # Ratio of eye line to face height
            self.crown_top = picture_height - bottom_eye_line - alpha * face_height
        else:
            self.crown_top = crown_top if crown_top is not None else 0.0

    @staticmethod
    def from_json(json_data):
        """
        Create a PhotoStandard from JSON data

        Args:
            json_data: JSON data containing photo standard information

        Returns:
            PhotoStandard object
        """
        dimensions = json_data["dimensions"]

        return PhotoStandard(
            picture_width=dimensions.get("pictureWidth", 0),
            picture_height=dimensions.get("pictureHeight", 0),
            face_height=dimensions.get("faceHeight", 0),
            crown_top=dimensions.get("crownTop", None),
            bottom_eye_line=dimensions.get("bottomEyeLine", None),
            resolution=dimensions.get("dpi", 300),
            units=dimensions.get("units", "mm")
        )

    def to_pixels(self):
        """
        Convert the standard dimensions to pixels

        Returns:
            Dictionary with dimensions in pixels
        """
        if self.units == "pixel":
            return {
                "picture_width": self.picture_width,
                "picture_height": self.picture_height,
                "face_height": self.face_height,
                "crown_top": self.crown_top
            }

        # Convert to pixels based on resolution
        pixels_per_unit = self.resolution
        if self.units == "mm":
            pixels_per_unit = self.resolution / 25.4  # 1 inch = 25.4 mm

        return {
            "picture_width": int(self.picture_width * pixels_per_unit),
            "picture_height": int(self.picture_height * pixels_per_unit),
            "face_height": int(self.face_height * pixels_per_unit),
            "crown_top": int(self.crown_top * pixels_per_unit)
        }

def load_photo_standards(standards_file):
    """
    Load photo standards from a JSON file

    Args:
        standards_file: Path to the JSON file containing photo standards

    Returns:
        Dictionary of photo standards by ID
    """
    with open(standards_file, 'r') as f:
        standards_data = json.load(f)

    standards = {}
    for standard_data in standards_data:
        standard_id = standard_data["id"]
        standards[standard_id] = PhotoStandard.from_json(standard_data)

    return standards

def detect_landmarks(image, model=None, annotation_path=None):
    """
    Detect facial landmarks in an image

    Args:
        image: Input image
        model: Trained model (optional)
        annotation_path: Path to annotation file (optional)

    Returns:
        Dictionary with detected landmarks
    """
    # If annotation is provided, use it
    if annotation_path and os.path.exists(annotation_path):
        with open(annotation_path, 'r') as f:
            landmarks_data = json.load(f)

        landmarks = {}
        landmarks["crown"] = np.array([landmarks_data[0]["x"], landmarks_data[0]["y"]])
        landmarks["chin"] = np.array([landmarks_data[1]["x"], landmarks_data[1]["y"]])
        landmarks["left_eye"] = np.array([landmarks_data[2]["x"], landmarks_data[2]["y"]])
        landmarks["right_eye"] = np.array([landmarks_data[3]["x"], landmarks_data[3]["y"]])
        landmarks["left_mouth"] = np.array([landmarks_data[4]["x"], landmarks_data[4]["y"]])
        landmarks["right_mouth"] = np.array([landmarks_data[5]["x"], landmarks_data[5]["y"]])

        return landmarks

    # If model is provided, use it
    if model:
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detect face
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)

        if len(faces) == 0:
            print("Warning: No face detected")
            return None

        # Use the largest face
        face = max(faces, key=lambda rect: rect[2] * rect[3])
        x, y, w, h = face

        # Extract face region
        face_img = gray[y:y+h, x:x+w]

        # Resize to a fixed size
        face_img = cv2.resize(face_img, (100, 100))

        # Predict landmarks
        landmarks_array = model.predict([face_img.flatten()])[0]

        # Reshape landmarks
        landmarks_array = landmarks_array.reshape(6, 2)

        # Denormalize landmarks
        landmarks_array[:, 0] = landmarks_array[:, 0] * w + x
        landmarks_array[:, 1] = landmarks_array[:, 1] * h + y

        # Create landmarks dictionary
        landmarks = {}
        landmarks["crown"] = landmarks_array[0]
        landmarks["chin"] = landmarks_array[1]
        landmarks["left_eye"] = landmarks_array[2]
        landmarks["right_eye"] = landmarks_array[3]
        landmarks["left_mouth"] = landmarks_array[4]
        landmarks["right_mouth"] = landmarks_array[5]

        return landmarks

    # If neither annotation nor model is provided, use OpenCV's face detector
    # and estimate landmarks based on face proportions
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Detect face
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    faces = face_cascade.detectMultiScale(gray, 1.3, 5)

    if len(faces) == 0:
        print("Warning: No face detected")
        return None

    # Use the largest face
    face = max(faces, key=lambda rect: rect[2] * rect[3])
    x, y, w, h = face

    # Try to detect eyes
    eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
    roi_gray = gray[y:y+h, x:x+w]
    eyes = eye_cascade.detectMultiScale(roi_gray)

    # If we detect at least two eyes, use them
    if len(eyes) >= 2:
        # Sort eyes by x-coordinate
        eyes = sorted(eyes, key=lambda e: e[0])

        # Get the two leftmost and rightmost eyes
        left_eye = eyes[0]
        right_eye = eyes[-1]

        # Calculate eye centers
        left_eye_center = (x + left_eye[0] + left_eye[2]//2, y + left_eye[1] + left_eye[3]//2)
        right_eye_center = (x + right_eye[0] + right_eye[2]//2, y + right_eye[1] + right_eye[3]//2)

        # Estimate other landmarks based on eye positions
        eye_distance = right_eye_center[0] - left_eye_center[0]
        eye_y = (left_eye_center[1] + right_eye_center[1]) // 2

        landmarks = {}
        landmarks["left_eye"] = np.array(left_eye_center)
        landmarks["right_eye"] = np.array(right_eye_center)
        # The crown is typically not at the very top of the detected face rectangle
        # but about 10% down from the top of the head
        landmarks["crown"] = np.array([x + w//2, y + int(h * 0.1)])
        landmarks["chin"] = np.array([x + w//2, y + h])
        landmarks["left_mouth"] = np.array([left_eye_center[0], eye_y + eye_distance//2])
        landmarks["right_mouth"] = np.array([right_eye_center[0], eye_y + eye_distance//2])

        return landmarks

    # Estimate landmarks based on face proportions
    landmarks = {}
    # The crown is typically not at the very top of the detected face rectangle
    # but about 10% down from the top of the head
    landmarks["crown"] = np.array([x + w//2, y + int(h * 0.1)])
    landmarks["chin"] = np.array([x + w//2, y + h])
    landmarks["left_eye"] = np.array([x + w//3, y + h//3])
    landmarks["right_eye"] = np.array([x + 2*w//3, y + h//3])
    landmarks["left_mouth"] = np.array([x + w//3, y + 2*h//3])
    landmarks["right_mouth"] = np.array([x + 2*w//3, y + 2*h//3])

    return landmarks

def align_face(image, landmarks):
    """
    Align the face so that the eyes are on a horizontal line

    Args:
        image: Input image
        landmarks: Detected landmarks

    Returns:
        Aligned image and updated landmarks
    """
    # Calculate the angle between the eyes
    left_eye = landmarks["left_eye"]
    right_eye = landmarks["right_eye"]

    # Make sure right_eye is actually to the right of left_eye
    if left_eye[0] > right_eye[0]:
        left_eye, right_eye = right_eye, left_eye

    # Calculate the angle between the eyes and the horizontal
    dy = right_eye[1] - left_eye[1]
    dx = right_eye[0] - left_eye[0]

    # Calculate angle in degrees
    angle = np.degrees(np.arctan2(dy, dx))

    # Print the angle for debugging
    print(f"Eye angle before alignment: {angle:.2f} degrees")

    # Get the center of the image as the rotation center
    center = (image.shape[1] // 2, image.shape[0] // 2)

    # Get the rotation matrix
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # Rotate the image
    aligned_image = cv2.warpAffine(image, M, (image.shape[1], image.shape[0]), flags=cv2.INTER_CUBIC)

    # Update the landmarks
    aligned_landmarks = {}
    for key, point in landmarks.items():
        # Apply the rotation to the point
        aligned_point = np.array([
            M[0, 0] * point[0] + M[0, 1] * point[1] + M[0, 2],
            M[1, 0] * point[0] + M[1, 1] * point[1] + M[1, 2]
        ])
        aligned_landmarks[key] = aligned_point

    # Verify the alignment worked by calculating the new angle
    new_left_eye = aligned_landmarks["left_eye"]
    new_right_eye = aligned_landmarks["right_eye"]
    new_dy = new_right_eye[1] - new_left_eye[1]
    new_dx = new_right_eye[0] - new_left_eye[0]
    new_angle = np.degrees(np.arctan2(new_dy, new_dx))
    print(f"Eye angle after alignment: {new_angle:.2f} degrees")

    return aligned_image, aligned_landmarks

def process_passport_photo(image, landmarks, standard_id="us_passport_photo", standards_file=None):
    """
    Process an image to create a passport photo according to a specific standard

    Args:
        image: Input image
        landmarks: Detected landmarks
        standard_id: ID of the photo standard to use
        standards_file: Path to the JSON file containing photo standards

    Returns:
        Processed passport photo and final landmarks
    """
    # First, align the face
    aligned_image, aligned_landmarks = align_face(image, landmarks)

    # Load photo standards
    if standards_file and os.path.exists(standards_file):
        standards = load_photo_standards(standards_file)
        if standard_id in standards:
            standard = standards[standard_id]
        else:
            print(f"Warning: Standard {standard_id} not found. Using default US passport photo standard.")
            standard = PhotoStandard(2.0, 2.0, 1.1875, 0.25, None, 300, "inch")
    else:
        # Use default US passport photo standard
        standard = PhotoStandard(2.0, 2.0, 1.1875, 0.25, None, 300, "inch")

    # Convert standard to pixels
    standard_pixels = standard.to_pixels()

    # Print the standard dimensions in pixels
    print(f"Standard: {standard_id}")
    print(f"Picture width: {standard_pixels['picture_width']} pixels")
    print(f"Picture height: {standard_pixels['picture_height']} pixels")
    print(f"Face height: {standard_pixels['face_height']} pixels")
    print(f"Crown top: {standard_pixels['crown_top']} pixels")

    # Calculate the current face height
    face_height = np.linalg.norm(aligned_landmarks["crown"] - aligned_landmarks["chin"])

    # Calculate the scaling factor
    scale_factor = standard_pixels["face_height"] / face_height

    # Resize the image
    resized_image = cv2.resize(aligned_image, None, fx=scale_factor, fy=scale_factor)

    # Calculate the scaled landmark positions
    scaled_landmarks = {}
    for key, value in aligned_landmarks.items():
        scaled_landmarks[key] = value * scale_factor

    # Calculate the eye midpoint
    eye_midpoint = (scaled_landmarks["left_eye"] + scaled_landmarks["right_eye"]) / 2

    # Calculate the target position for the crown
    target_crown_y = standard_pixels["crown_top"]

    # Print crown position for debugging
    print(f"Crown position in scaled image: {scaled_landmarks['crown'][1]:.2f} pixels")
    print(f"Target crown position: {target_crown_y} pixels")

    # Calculate the vertical offset to position the crown correctly
    vertical_offset = int(target_crown_y - scaled_landmarks["crown"][1])
    print(f"Vertical offset: {vertical_offset} pixels")

    # For passport photos, we need to ensure the face is properly positioned
    # Let's use a simpler approach - just use the standard crown_top value
    vertical_offset = standard_pixels["crown_top"]

    # Adjust for the current crown position
    vertical_offset = vertical_offset - int(scaled_landmarks["crown"][1])

    # Ensure the offset is reasonable
    if vertical_offset < -300:
        vertical_offset = -300
    elif vertical_offset > 100:
        vertical_offset = 100

    print(f"Final vertical offset: {vertical_offset} pixels")

    # Calculate the target horizontal center
    target_center_x = standard_pixels["picture_width"] // 2

    # Calculate the horizontal offset to center the face
    horizontal_offset = int(target_center_x - eye_midpoint[0])
    print(f"Horizontal offset: {horizontal_offset} pixels")

    # Create a blank canvas with the standard dimensions
    canvas = np.ones((standard_pixels["picture_height"], standard_pixels["picture_width"], 3), dtype=np.uint8) * 255

    # Calculate the position to place the resized image on the canvas
    x_offset = max(0, horizontal_offset)
    y_offset = max(0, vertical_offset)

    # Calculate the region of the resized image to copy
    src_x = max(0, -horizontal_offset)
    src_y = max(0, -vertical_offset)
    src_width = min(resized_image.shape[1] - src_x, standard_pixels["picture_width"] - x_offset)
    src_height = min(resized_image.shape[0] - src_y, standard_pixels["picture_height"] - y_offset)

    # Copy the region of the resized image to the canvas
    canvas[y_offset:y_offset+src_height, x_offset:x_offset+src_width] = resized_image[src_y:src_y+src_height, src_x:src_x+src_width]

    # Calculate the final landmark positions on the canvas
    final_landmarks = {}
    for key, point in scaled_landmarks.items():
        # Apply the translation to the point
        final_point = np.array([
            point[0] - src_x + x_offset,
            point[1] - src_y + y_offset
        ])
        # Check if the point is within the canvas
        if (0 <= final_point[0] < standard_pixels["picture_width"] and
            0 <= final_point[1] < standard_pixels["picture_height"]):
            final_landmarks[key] = final_point

    return canvas, final_landmarks

def draw_landmarks(image, landmarks, color=(0, 255, 0), radius=3, thickness=2):
    """
    Draw landmarks on an image

    Args:
        image: Input image
        landmarks: Dictionary of landmarks
        color: Color of the landmarks (BGR)
        radius: Radius of the landmark circles
        thickness: Thickness of the landmark circles

    Returns:
        Image with landmarks drawn on it
    """
    # Make a copy of the image to avoid modifying the original
    image_with_landmarks = image.copy()

    # Draw each landmark
    for key, point in landmarks.items():
        # Convert point to integer coordinates
        point = (int(point[0]), int(point[1]))

        # Draw a circle at the landmark position
        cv2.circle(image_with_landmarks, point, radius, color, thickness)

        # Add a label
        cv2.putText(image_with_landmarks, key, (point[0] + 5, point[1] - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1, cv2.LINE_AA)

    # Draw lines connecting key landmarks
    if "left_eye" in landmarks and "right_eye" in landmarks:
        left_eye = (int(landmarks["left_eye"][0]), int(landmarks["left_eye"][1]))
        right_eye = (int(landmarks["right_eye"][0]), int(landmarks["right_eye"][1]))
        cv2.line(image_with_landmarks, left_eye, right_eye, color, thickness)

    if "crown" in landmarks and "chin" in landmarks:
        crown = (int(landmarks["crown"][0]), int(landmarks["crown"][1]))
        chin = (int(landmarks["chin"][0]), int(landmarks["chin"][1]))
        cv2.line(image_with_landmarks, crown, chin, color, thickness)

    return image_with_landmarks

def main():
    parser = argparse.ArgumentParser(description='Process passport photos according to standards')
    parser.add_argument('--image', required=True, help='Path to the input image')
    parser.add_argument('--output', required=True, help='Path to save the processed passport photo')
    parser.add_argument('--model', help='Path to the trained model (optional)')
    parser.add_argument('--annotation', help='Path to the annotation file (optional)')
    parser.add_argument('--standard', default='us_passport_photo', help='ID of the photo standard to use')
    parser.add_argument('--standards_file', help='Path to the JSON file containing photo standards')
    parser.add_argument('--show_landmarks', action='store_true', help='Show landmarks on the output image')

    args = parser.parse_args()

    # Load the model if provided
    model = None
    if args.model and os.path.exists(args.model):
        with open(args.model, 'rb') as f:
            model = pickle.load(f)

    # Read the image
    image = cv2.imread(args.image)
    if image is None:
        print(f"Error: Could not read image {args.image}")
        return

    # Detect landmarks
    landmarks = detect_landmarks(image, model, args.annotation)

    if landmarks is None:
        print("Error: Could not detect landmarks")
        return

    # Save an image with the original landmarks
    original_with_landmarks = draw_landmarks(image, landmarks)
    original_landmarks_path = os.path.splitext(args.output)[0] + "_original_landmarks.jpg"
    cv2.imwrite(original_landmarks_path, original_with_landmarks)
    print(f"Original image with landmarks saved to {original_landmarks_path}")

    # Process the passport photo
    passport_photo, final_landmarks = process_passport_photo(image, landmarks, args.standard, args.standards_file)

    # Save the processed photo
    cv2.imwrite(args.output, passport_photo)
    print(f"Passport photo saved to {args.output}")

    # If requested, create a version with landmarks
    if args.show_landmarks and final_landmarks:
        passport_with_landmarks = draw_landmarks(passport_photo, final_landmarks)
        landmarks_path = os.path.splitext(args.output)[0] + "_landmarks.jpg"
        cv2.imwrite(landmarks_path, passport_with_landmarks)
        print(f"Passport photo with landmarks saved to {landmarks_path}")

    # Print the dimensions of the output image
    output_image = cv2.imread(args.output)
    if output_image is not None:
        print(f"Output image dimensions: {output_image.shape[1]}x{output_image.shape[0]} pixels")

if __name__ == '__main__':
    main()
