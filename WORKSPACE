workspace(name = "mediapipe_app")

##################################################################################
load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
http_archive(
  name = "bazel_skylib",
  type = "tar.gz",
  urls = [
      "https://github.com/bazelbuild/bazel-skylib/releases/download/1.0.3/bazel-skylib-1.0.3.tar.gz",
      "https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/1.0.3/bazel-skylib-1.0.3.tar.gz",
  ],
  sha256 = "1c531376ac7e5a180e0237938a2536de0c54d93f5c278634818e0efc952dd56c",
)
load("@bazel_skylib//:workspace.bzl", "bazel_skylib_workspace")
bazel_skylib_workspace()
load("@bazel_skylib//lib:versions.bzl", "versions")
versions.check(minimum_bazel_version = "3.7.2")


git_repository(
  name = "org_mediapipe",
  remote = "https://github.com/google/mediapipe",
  # branch = "v0.8.11",
  commit = "6cdc6443b6a7ed662744e2a2ce2d58d9c83e6d6f",
  shallow_since = "1662687532 +0000"
)

new_local_repository(
  name = "tp_linux",
  path = "/thirdparty/install_linux_relwithdebinfo",
  build_file_content = """
cc_library(
  name = "tplibs",
  includes = [ "include" ],
  strip_include_prefix = "include",
  srcs = [],
  visibility = ["//visibility:public"],
)
""",
)

new_local_repository(
  name = "tp_wasm",
  path = "/thirdparty/install_emscripten_release",
  build_file_content = """
cc_library(
    name = "tplibs",
    includes = [ "include" ],
    strip_include_prefix = "include",
    srcs = [],
    visibility = ["//visibility:public"],
)
""",
)

# OpenCV build for MediaPipe
new_local_repository(
  name = "linux_opencv",
  path =  "/thirdparty/",
  build_file_content = """
config_setting(
  name = "wasm_build",
  values = {"cpu": "wasm"},
)
cc_library(
  name = "opencv",
  hdrs = select({
    ':wasm_build': glob([
      "install_emscripten_release/include/opencv2/**/*.h",
      "install_emscripten_release/include/opencv2/**/*.hpp",
    ]),
    '//conditions:default': glob([
      "install_linux_relwithdebinfo/include/opencv2/**/*.h",
      "install_linux_relwithdebinfo/include/opencv2/**/*.hpp",
    ])
  }),
  strip_include_prefix = select({
    ':wasm_build': "install_emscripten_release/include",
    '//conditions:default': "install_linux_relwithdebinfo/include",
  }),
  srcs = select({
    ':wasm_build': [
      'install_emscripten_release/lib/libopencv_calib3d.a',
      'install_emscripten_release/lib/libopencv_features2d.a',
      'install_emscripten_release/lib/libopencv_flann.a',
      'install_emscripten_release/lib/libopencv_highgui.a',
      'install_emscripten_release/lib/libopencv_imgcodecs.a',
      'install_emscripten_release/lib/libopencv_objdetect.a',
      'install_emscripten_release/lib/libopencv_video.a',
      'install_emscripten_release/lib/libopencv_videoio.a',
      'install_emscripten_release/lib/libopencv_ximgproc.a',
      'install_emscripten_release/lib/libopencv_imgproc.a',
      'install_emscripten_release/lib/libopencv_core.a',
      'install_emscripten_release/lib/opencv4/3rdparty/liblibjpeg-turbo.a',
      'install_emscripten_release/lib/opencv4/3rdparty/liblibopenjp2.a',
      'install_emscripten_release/lib/opencv4/3rdparty/liblibpng.a',
      'install_emscripten_release/lib/opencv4/3rdparty/libquirc.a',
      'install_emscripten_release/lib/opencv4/3rdparty/libzlib.a',
    ],
    '//conditions:default': [
      'install_linux_relwithdebinfo/lib/libopencv_calib3d.a',
      'install_linux_relwithdebinfo/lib/libopencv_features2d.a',
      'install_linux_relwithdebinfo/lib/libopencv_flann.a',
      'install_linux_relwithdebinfo/lib/libopencv_highgui.a',
      'install_linux_relwithdebinfo/lib/libopencv_imgcodecs.a',
      'install_linux_relwithdebinfo/lib/libopencv_objdetect.a',
      'install_linux_relwithdebinfo/lib/libopencv_video.a',
      'install_linux_relwithdebinfo/lib/libopencv_videoio.a',
      'install_linux_relwithdebinfo/lib/libopencv_ximgproc.a',
      'install_linux_relwithdebinfo/lib/libopencv_imgproc.a',
      'install_linux_relwithdebinfo/lib/libopencv_core.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/liblibjpeg-turbo.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/liblibopenjp2.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/liblibpng.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/libquirc.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/libzlib.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/libippiw.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/libippicv.a',
      'install_linux_relwithdebinfo/lib/opencv4/3rdparty/libittnotify.a',
    ]}
  ),
  visibility = ["//visibility:public"],
)
""",
)



#####################
# emscripten
#####################
local_repository(
  name = "emsdk",
  path = "/emsdk/bazel",
)

load("@emsdk//:deps.bzl", emsdk_deps = "deps")
emsdk_deps()

load("@emsdk//:emscripten_deps.bzl", emsdk_emscripten_deps = "emscripten_deps")
emsdk_emscripten_deps()

load("@emsdk//:toolchains.bzl", "register_emscripten_toolchains")
register_emscripten_toolchains()

###################################################################
# Hedron's Compile Commands Extractor for Bazel
# https://github.com/hedronvision/bazel-compile-commands-extractor
###################################################################
http_archive(
  name = "hedron_compile_commands",
  # Replace the commit hash in both places (below) with the latest, rather than using the stale one here.
  # Even better, set up Renovate and let it do the work for you (see "Suggestion: Updates" in the README).
  url = "https://github.com/hedronvision/bazel-compile-commands-extractor/archive/12440540f4461cac006a357257d698aed74a2423.tar.gz",
  strip_prefix = "bazel-compile-commands-extractor-12440540f4461cac006a357257d698aed74a2423",
  sha256 = "2f6c129770dbc7a0bb53057b8a225ee1eb275bf27e05504b683f8d96d2c877c9",
  # When you first run this tool, it'll recommend a sha256 hash to put here with a message like: "DEBUG: Rule 'hedron_compile_commands' indicated that a canonical reproducible form can be obtained by modifying arguments sha256 = ..."
)
load("@hedron_compile_commands//:workspace_setup.bzl", "hedron_compile_commands_setup")
hedron_compile_commands_setup()


######################################################################
######################################################################
######################################################################

# ABSL cpp library lts_2021_03_24, patch 2.
http_archive(
  name = "com_google_absl",
  urls = [
    "https://github.com/abseil/abseil-cpp/archive/refs/tags/20210324.2.tar.gz",
  ],
  # Remove after https://github.com/abseil/abseil-cpp/issues/326 is solved.
  patches = [
    "@org_mediapipe//third_party:com_google_absl_f863b622fe13612433fdf43f76547d5edda0c93001.diff"
  ],
  patch_args = [
    "-p1",
  ],
  strip_prefix = "abseil-cpp-20210324.2",
  sha256 = "59b862f50e710277f8ede96f083a5bb8d7c9595376146838b9580be90374ee1f"
)

http_archive(
  name = "rules_cc",
  strip_prefix = "rules_cc-2f8c04c04462ab83c545ab14c0da68c3b4c96191",
# The commit can be updated if the build passes. Last updated 6/23/22.
  urls = ["https://github.com/bazelbuild/rules_cc/archive/2f8c04c04462ab83c545ab14c0da68c3b4c96191.zip"],
)

http_archive(
  name = "rules_foreign_cc",
  strip_prefix = "rules_foreign_cc-0.1.0",
  url = "https://github.com/bazelbuild/rules_foreign_cc/archive/0.1.0.zip",
  sha256 = "c2cdcf55ffaf49366725639e45dedd449b8c3fe22b54e31625eb80ce3a240f1e"
)

load("@rules_foreign_cc//:workspace_definitions.bzl", "rules_foreign_cc_dependencies")

rules_foreign_cc_dependencies()

# This is used to select all contents of the archives for CMake-based packages to give CMake access to them.
all_content = """filegroup(name = "all", srcs = glob(["**"]), visibility = ["//visibility:public"])"""

# GoogleTest/GoogleMock framework. Used by most unit-tests.
# Last updated 2021-07-02.
http_archive(
  name = "com_google_googletest",
  urls = ["https://github.com/google/googletest/archive/4ec4cd23f486bf70efcc5d2caa40f24368f752e3.zip"],
  strip_prefix = "googletest-4ec4cd23f486bf70efcc5d2caa40f24368f752e3",
  sha256 = "de682ea824bfffba05b4e33b67431c247397d6175962534305136aa06f92e049",
)

# Google Benchmark library v1.6.1 released on 2022-01-10.
http_archive(
  name = "com_google_benchmark",
  urls = ["https://github.com/google/benchmark/archive/refs/tags/v1.6.1.tar.gz"],
  strip_prefix = "benchmark-1.6.1",
  sha256 = "6132883bc8c9b0df5375b16ab520fac1a85dc9e4cf5be59480448ece74b278d4",
  build_file = "@org_mediapipe//third_party:benchmark.BUILD",
)

# gflags needed by glog
http_archive(
  name = "com_github_gflags_gflags",
  strip_prefix = "gflags-2.2.2",
  sha256 = "19713a36c9f32b33df59d1c79b4958434cb005b5b47dc5400a7a4b078111d9b5",
  url = "https://github.com/gflags/gflags/archive/v2.2.2.zip",
)

# 2020-08-21
http_archive(
  name = "com_github_glog_glog",
  strip_prefix = "glog-0a2e5931bd5ff22fd3bf8999eb8ce776f159cda6",
  sha256 = "58c9b3b6aaa4dd8b836c0fd8f65d0f941441fb95e27212c5eeb9979cfd3592ab",
  urls = [
    "https://github.com/google/glog/archive/0a2e5931bd5ff22fd3bf8999eb8ce776f159cda6.zip",
  ],
)
http_archive(
  name = "com_github_glog_glog_no_gflags",
  strip_prefix = "glog-0a2e5931bd5ff22fd3bf8999eb8ce776f159cda6",
  sha256 = "58c9b3b6aaa4dd8b836c0fd8f65d0f941441fb95e27212c5eeb9979cfd3592ab",
  build_file = "@//third_party:glog_no_gflags.BUILD",
  urls = [
    "https://github.com/google/glog/archive/0a2e5931bd5ff22fd3bf8999eb8ce776f159cda6.zip",
  ],
  patches = [
    "@org_mediapipe//third_party:com_github_glog_glog_9779e5ea6ef59562b030248947f787d1256132ae.diff",
  ],
  patch_args = [
    "-p1",
  ],
)

# easyexif
http_archive(
  name = "easyexif",
  url = "https://github.com/mayanklahiri/easyexif/archive/master.zip",
  strip_prefix = "easyexif-master",
  build_file = "@org_mediapipe//third_party:easyexif.BUILD",
)

# libyuv
http_archive(
  name = "libyuv",
  # Error: operand type mismatch for `vbroadcastss' caused by commit 8a13626e42f7fdcf3a6acbb0316760ee54cda7d8.
  urls = ["https://chromium.googlesource.com/libyuv/libyuv/+archive/2525698acba9bf9b701ba6b4d9584291a1f62257.tar.gz"],
  build_file = "@org_mediapipe//third_party:libyuv.BUILD",
)

# Note: protobuf-javalite is no longer released as a separate download, it's included in the main Java download.
# ...but the Java download is currently broken, so we use the "source" download.
http_archive(
  name = "com_google_protobuf_javalite",
  sha256 = "87407cd28e7a9c95d9f61a098a53cf031109d451a7763e7dd1253abf8b4df422",
  strip_prefix = "protobuf-3.19.1",
  urls = ["https://github.com/protocolbuffers/protobuf/archive/v3.19.1.tar.gz"],
)

http_archive(
  name = "com_google_protobuf",
  sha256 = "87407cd28e7a9c95d9f61a098a53cf031109d451a7763e7dd1253abf8b4df422",
  strip_prefix = "protobuf-3.19.1",
  urls = ["https://github.com/protocolbuffers/protobuf/archive/v3.19.1.tar.gz"],
  patches = [
    "@org_mediapipe//third_party:com_google_protobuf_fixes.diff"
  ],
  patch_args = [
    "-p1",
  ],
)

load("@org_mediapipe//third_party/flatbuffers:workspace.bzl", flatbuffers = "repo")
flatbuffers()

http_archive(
  name = "com_google_audio_tools",
  strip_prefix = "multichannel-audio-tools-master",
  urls = ["https://github.com/google/multichannel-audio-tools/archive/master.zip"],
)

# sentencepiece
http_archive(
  name = "com_google_sentencepiece",
  strip_prefix = "sentencepiece-1.0.0",
  sha256 = "c05901f30a1d0ed64cbcf40eba08e48894e1b0e985777217b7c9036cac631346",
  urls = [
    "https://github.com/google/sentencepiece/archive/1.0.0.zip",
  ],
  repo_mapping = {"@com_google_glog" : "@com_github_glog_glog"},
)

http_archive(
  name = "org_tensorflow_text",
  sha256 = "f64647276f7288d1b1fe4c89581d51404d0ce4ae97f2bcc4c19bd667549adca8",
  strip_prefix = "text-2.2.0",
  urls = [
    "https://github.com/tensorflow/text/archive/v2.2.0.zip",
  ],
  patches = [
    "@org_mediapipe//third_party:tensorflow_text_remove_tf_deps.diff",
    "@org_mediapipe//third_party:tensorflow_text_a0f49e63.diff",
  ],
  patch_args = ["-p1"],
  repo_mapping = {"@com_google_re2": "@com_googlesource_code_re2"},
)

http_archive(
  name = "com_googlesource_code_re2",
  sha256 = "e06b718c129f4019d6e7aa8b7631bee38d3d450dd980246bfaf493eb7db67868",
  strip_prefix = "re2-fe4a310131c37f9a7e7f7816fa6ce2a8b27d65a8",
  urls = [
    "https://github.com/google/re2/archive/fe4a310131c37f9a7e7f7816fa6ce2a8b27d65a8.tar.gz",
  ],
)
# Point to the commit that deprecates the usage of Eigen::MappedSparseMatrix.
http_archive(
  name = "ceres_solver",
  url = "https://github.com/ceres-solver/ceres-solver/archive/123fba61cf2611a3c8bddc9d91416db26b10b558.zip",
  patches = [
    "@org_mediapipe//third_party:ceres_solver_compatibility_fixes.diff"
  ],
  patch_args = [
    "-p1",
  ],
  strip_prefix = "ceres-solver-123fba61cf2611a3c8bddc9d91416db26b10b558",
  sha256 = "8b7b16ceb363420e0fd499576daf73fa338adb0b1449f58bea7862766baa1ac7"
)

# Needed by TensorFlow
http_archive(
  name = "io_bazel_rules_closure",
  sha256 = "e0a111000aeed2051f29fcc7a3f83be3ad8c6c93c186e64beb1ad313f0c7f9f9",
  strip_prefix = "rules_closure-cf1e44edb908e9616030cc83d085989b8e6cd6df",
  urls = [
    "http://mirror.tensorflow.org/github.com/bazelbuild/rules_closure/archive/cf1e44edb908e9616030cc83d085989b8e6cd6df.tar.gz",
    "https://github.com/bazelbuild/rules_closure/archive/cf1e44edb908e9616030cc83d085989b8e6cd6df.tar.gz",  # 2019-04-04
  ],
)

# Load Zlib before initializing TensorFlow to guarantee that the target
# @zlib//:mini_zlib is available
http_archive(
  name = "zlib",
  build_file = "@org_mediapipe//third_party:zlib.BUILD",
  sha256 = "c3e5e9fdd5004dcb542feda5ee4f0ff0744628baf8ed2dd5d66f8ca1197cb1a1",
  strip_prefix = "zlib-1.2.11",
  urls = [
    "http://mirror.bazel.build/zlib.net/fossils/zlib-1.2.11.tar.gz",
    "http://zlib.net/fossils/zlib-1.2.11.tar.gz",  # 2017-01-15
  ],
  patches = [
    "@org_mediapipe//third_party:zlib.diff",
  ],
  patch_args = [
    "-p1",
  ],
)

# TensorFlow repo should always go after the other external dependencies.
# TF on 2022-08-10.
_TENSORFLOW_GIT_COMMIT = "af1d5bc4fbb66d9e6cc1cf89503014a99233583b"
_TENSORFLOW_SHA256 = "f85a5443264fc58a12d136ca6a30774b5bc25ceaf7d114d97f252351b3c3a2cb"
http_archive(
  name = "org_tensorflow",
  urls = [
    "https://github.com/tensorflow/tensorflow/archive/%s.tar.gz" % _TENSORFLOW_GIT_COMMIT,
  ],
  patches = [
    "@org_mediapipe//third_party:org_tensorflow_compatibility_fixes.diff",
    # Diff is generated with a script, don't update it manually.
    "@org_mediapipe//third_party:org_tensorflow_custom_ops.diff",
  ],
  patch_args = [
    "-p1",
  ],
  strip_prefix = "tensorflow-%s" % _TENSORFLOW_GIT_COMMIT,
  sha256 = _TENSORFLOW_SHA256,
)

load("@org_tensorflow//tensorflow:workspace3.bzl", "tf_workspace3")
tf_workspace3()
load("@org_tensorflow//tensorflow:workspace2.bzl", "tf_workspace2")
tf_workspace2()
