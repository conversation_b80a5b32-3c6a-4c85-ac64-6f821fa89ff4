# Background Removal Implementation Plan - BiRefNet v2 API

## Overview
Integrate BiRefNet v2 API for high-quality background removal in the passport photo maker application. This will allow users to automatically remove backgrounds from their photos before processing them according to passport photo standards.

## Implementation Strategy

### 1. Backend Implementation

#### 1.1 Dependencies
- [ ] Add `fal-client` to requirements.txt
- [ ] Set up environment variable for `FAL_KEY`
- [ ] Create configuration management for API settings

#### 1.2 Background Removal Service
- [ ] Create `app/services/background_removal.py`
  - Background removal API client
  - Image upload to fal.ai service
  - Result processing and error handling
  - Support for different models (Portrait model preferred)
  - Async processing with queue status checking

#### 1.3 API Endpoints
- [ ] Create new router `app/routers/background_removal.py`
  - `POST /api/background-removal/remove` - Remove background from uploaded image
  - `GET /api/background-removal/status/{request_id}` - Check processing status
  - `GET /api/background-removal/result/{request_id}` - Get processed result

#### 1.4 Integration with Existing Workflow
- [ ] Modify `app/services/landmark_detection.py` to support background-removed images
- [ ] Update `image_store` to track background removal status
- [ ] Ensure processed images maintain quality and metadata

### 2. Frontend Implementation

#### 2.1 UI Components
- [ ] Add background removal section to `app/static/index.html`
  - Toggle button for background removal
  - Progress indicator for API processing
  - Preview of original vs background-removed image
  - Option to proceed with or without background removal

#### 2.2 Workflow Integration
- [ ] Insert background removal step between upload and landmark detection
- [ ] Allow users to skip background removal if desired
- [ ] Maintain original image for comparison
- [ ] Update image preview to show background-removed version

#### 2.3 User Experience
- [ ] Loading states during API processing
- [ ] Error handling and retry mechanisms
- [ ] Clear visual feedback on processing status
- [ ] Option to revert to original image

### 3. Configuration and Environment

#### 3.1 Environment Variables
- [ ] `FAL_KEY` - API key for fal.ai service
- [ ] `BACKGROUND_REMOVAL_ENABLED` - Feature toggle
- [ ] `BACKGROUND_REMOVAL_MODEL` - Default model (Portrait)

#### 3.2 Settings
- [ ] Model selection: Portrait (recommended for passport photos)
- [ ] Operating resolution: 2048x2048 for high quality
- [ ] Output format: PNG to preserve transparency
- [ ] Refine foreground: true for better quality

### 4. Implementation Details

#### 4.1 API Integration
```python
# Example service structure
class BackgroundRemovalService:
    def __init__(self):
        self.client = fal_client
        self.model = "fal-ai/birefnet/v2"
        
    async def remove_background(self, image_id: str) -> str:
        # Upload image to fal.ai
        # Submit background removal request
        # Return processed image ID
        
    async def check_status(self, request_id: str) -> dict:
        # Check processing status
        
    async def get_result(self, request_id: str) -> bytes:
        # Download processed image
```

#### 4.2 Workflow Integration
1. User uploads image
2. Option to remove background appears
3. If selected, image is sent to BiRefNet API
4. User sees progress indicator
5. Processed image replaces original in workflow
6. Continue with landmark detection and cropping

#### 4.3 Quality Considerations
- Use Portrait model for best passport photo results
- High resolution (2048x2048) for quality preservation
- PNG output to maintain transparency
- Refine foreground for cleaner edges

### 5. Testing Strategy

#### 5.1 Unit Tests
- [ ] Test background removal service
- [ ] Test API endpoint responses
- [ ] Test error handling scenarios

#### 5.2 Integration Tests
- [ ] Test full workflow with background removal
- [ ] Test image quality preservation
- [ ] Test with various image formats and sizes

#### 5.3 Manual Testing
- [ ] Test with different portrait types
- [ ] Verify passport photo compliance after background removal
- [ ] Test performance with large images

### 6. Error Handling

#### 6.1 API Errors
- Network connectivity issues
- API rate limiting
- Invalid image formats
- Processing failures

#### 6.2 User Experience
- Clear error messages
- Fallback to original image
- Retry mechanisms
- Graceful degradation

### 7. Performance Considerations

#### 7.1 Optimization
- Async processing to avoid blocking UI
- Progress indicators for long operations
- Image compression for API upload
- Caching of processed results

#### 7.2 Resource Management
- Memory management for large images
- Cleanup of temporary files
- API rate limiting compliance

### 8. Security Considerations

#### 8.1 API Key Management
- Secure storage of FAL_KEY
- Environment-based configuration
- No exposure in client-side code

#### 8.2 Image Privacy
- Temporary storage on fal.ai servers
- Data retention policies
- User consent for cloud processing

### 9. Deployment Checklist

- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] API key obtained and set
- [ ] Feature toggle enabled
- [ ] Testing completed
- [ ] Documentation updated

### 10. Future Enhancements

- [ ] Batch processing for multiple images
- [ ] Custom background replacement
- [ ] Edge refinement options
- [ ] Local processing fallback
- [ ] Background removal quality metrics

## Implementation Priority

1. **High Priority**: Core background removal service and API integration
2. **Medium Priority**: UI integration and user experience improvements
3. **Low Priority**: Advanced features and optimizations

## Success Criteria

- Users can successfully remove backgrounds from passport photos
- Image quality is maintained throughout the process
- Integration doesn't disrupt existing workflow
- Processing time is reasonable (< 30 seconds)
- Error handling provides clear user feedback
