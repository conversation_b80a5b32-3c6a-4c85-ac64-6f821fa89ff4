{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "defaultProject": "app", "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["interactjs"], "outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", {"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}, "src/manifest.webmanifest"], "styles": [{"input": "src/theme/variables.scss", "inject": true}, {"input": "src/global.scss", "inject": true}], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "ci": {"progress": false}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "app:build", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "app:build:production"}, "ci": {"progress": false}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "styles": [], "scripts": [], "assets": [{"glob": "favicon.ico", "input": "src/", "output": "/"}, {"glob": "robots.txt", "input": "src/assets", "output": "/"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}, {"glob": "**/*", "input": "src/assets", "output": "/assets"}, "src/manifest.webmanifest"]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "app:serve"}, "configurations": {"production": {"devServerTarget": "app:serve:production"}, "ci": {"devServerTarget": "app:serve:ci"}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "app:ionic-cordova-build", "devServerTarget": "app:serve"}, "configurations": {"production": {"cordovaBuildTarget": "app:ionic-cordova-build:production", "devServerTarget": "app:serve:production"}}}}}}, "cli": {"defaultCollection": "@ionic/angular-toolkit", "analytics": "e56f4039-3202-4ece-a6e2-0f7a1a1b7fe1"}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}