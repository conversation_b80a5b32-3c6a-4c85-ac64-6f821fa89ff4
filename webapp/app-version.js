var fs = require('fs');
var util = require('util');
var exec = util.promisify(require('child_process').exec);

var MAJOR_REVISION = '1';
var MINOR_REVISION = '0';

exec('git rev-parse --short HEAD', (err, stdout) => {
  var commitId = stdout.toString().trim();
  exec('git rev-list HEAD', (err, stdout) => {
    var buildNumber = stdout.toString().split(/\r\n|\r|\n/).length;
    exec('git rev-parse --abbrev-ref HEAD', (err, stdout) => {
      var branch = stdout.toString().trim();
      console.log(
        `AppVersion: ${MAJOR_REVISION}.${MINOR_REVISION}.${buildNumber} - Branch: '${branch}' - Commit ID: ${commitId}`
      );

      const content = `
// this file is automatically generated by git.version.ts script
export class AppBuildInfo {
  branch: string;
  majorVersion: number;
  minorVersion:number;
  buildNumber: number;
  versionString: string;
  timeStamp: number;
  commitId: string;
};

export const appBuildInfo = {
  branch: '${branch}',
  majorVersion: ${MAJOR_REVISION},
  minorVersion: ${MINOR_REVISION},
  buildNumber: ${buildNumber},
  versionString: '${MAJOR_REVISION}.${MINOR_REVISION}.${buildNumber}',
  timeStamp: ${Date.now()},
  commitId: '${commitId}'
};
`;

      fs.writeFileSync('src/environments/app-build-info.ts', content, { encoding: 'utf8' });
    });
  });
});
