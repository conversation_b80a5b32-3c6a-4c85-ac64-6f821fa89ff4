# Specifies intentionally untracked files to ignore when using Git
# http://git-scm.com/docs/gitignore

*~
*.sw[mnpcod]
.tmp
*.tmp
*.tmp.*
*.sublime-project
*.sublime-workspace
.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate
$RECYCLE.BIN/

*.log
log.txt
npm-debug.log*

/.idea
/.ionic
/.sass-cache
/.sourcemaps
/.versions
/.vscode
/coverage
/node_modules
/platforms
/plugins
/www

# Server files part of LIBPPP
/uploads
*.pyc
config.bundle.json
libppp.dll
liblibppp.so
liblibppp.dylib
libpppwrapper.py
src/assets/icons/icon-*.png
.angular/

# This is the emscripten output
src/assets/*.wasm
src/assets/*-wasm.js
src/assets/*.wasm.map
src/assets/*.dat
src/assets/**/*.xml
src/assets/config.json
src/assets/*.tflite
src/assets/*.onnx

# Autogenerated version file with date time stamp and git revision
src/environments/app-build-info.ts

.firebase
