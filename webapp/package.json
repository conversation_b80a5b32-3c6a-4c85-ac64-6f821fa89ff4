{"name": "photo-id-creator", "version": "1.0.1", "description": "A Photo ID Creation Tool", "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://myphotoidapp.web.app/", "scripts": {"gen-pwa-icons": "npx ngx-pwa-icons -i src/favicon.png", "gen-app-info": "node app-version.js", "ng": "npx ionic", "start": "npx ionic serve -b", "build": "npx ionic build", "test": "npx ionic test", "test:ci": "npx ng test --browsers=ChromeHeadless --watch=false", "lint": "npx ionic lint", "e2e": "npx ionic e2e", "build:all": "npm run gen-app-info && npm run build"}, "private": true, "dependencies": {"@angular/common": "^14.0.0", "@angular/core": "^14.0.0", "@angular/forms": "^14.0.0", "@angular/platform-browser": "^14.0.0", "@angular/platform-browser-dynamic": "^14.0.0", "@angular/router": "^14.0.0", "@angular/service-worker": "^14.2.10", "@capacitor/android": "4.4.0", "@capacitor/app": "4.1.0", "@capacitor/camera": "^4.1.3", "@capacitor/core": "4.4.0", "@capacitor/haptics": "4.0.1", "@capacitor/keyboard": "4.0.1", "@capacitor/status-bar": "4.0.1", "@ionic/angular": "^6.1.9", "@ionic/pwa-elements": "^3.1.1", "flag-icons": "^6.6.6", "interactjs": "^1.10.17", "ionicons": "^6.0.3", "ngx-color-picker": "^13.0.0", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.0.0", "@angular-eslint/builder": "^14.0.0", "@angular-eslint/eslint-plugin": "^14.0.0", "@angular-eslint/eslint-plugin-template": "^14.0.0", "@angular-eslint/template-parser": "^14.0.0", "@angular/cli": "^14.0.0", "@angular/compiler": "^14.0.0", "@angular/compiler-cli": "^14.0.0", "@angular/language-service": "^14.0.0", "@capacitor/cli": "4.4.0", "@ionic/angular-toolkit": "^6.0.0", "@types/jasmine": "~4.0.0", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "eslint": "^7.6.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "firebase-tools": "^11.16.0", "@ionic/cli": "^7.1.1", "jasmine-core": "~4.3.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ngx-pwa-icons": "0.1.2", "ts-node": "~8.3.0", "typescript": "~4.7.2"}}