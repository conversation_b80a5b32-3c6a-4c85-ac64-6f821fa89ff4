if ('function' === typeof importScripts) {
  let pppEngine = null;
  let moduleInitialized = false;

  // Set up a timeout to detect if the WebAssembly module is stuck
  const initTimeout = setTimeout(() => {
    if (!moduleInitialized) {
      console.error('WebAssembly module initialization timed out');
      postMessage({
        cmd: 'onAppDataLoadingProgress',
        progress: 0.0,
        step: 'Error: WebAssembly initialization timed out',
      });

      // Even though we timed out, let's try to force initialization
      // This is a last resort attempt to make the app work
      setTimeout(() => {
        console.log('Attempting to force initialization after timeout');
        postMessage({
          cmd: 'onAppDataLoadingProgress',
          progress: 1.0,
          step: 'Application ready (forced)',
        });
        postMessage({ cmd: 'onRuntimeInitialized' });
      }, 1000);
    }
  }, 15000); // 15 seconds timeout

  // Define Module object before importing the WebAssembly script
  self.Module = {
    preRun: [],
    postRun: [],
    print: function(text) {
      console.log('WASM stdout:', text);

      // Force send the onRuntimeInitialized message if we see the engine is ready
      // This is a backup in case the normal initialization flow fails
      if (text.includes("Engine configured and ready to be used")) {
        console.log("Engine is ready, forcing initialization message");

        // Send the progress update
        postMessage({
          cmd: 'onAppDataLoadingProgress',
          progress: 1.0,
          step: 'Application ready',
        });

        // Send the initialization message with a delay
        setTimeout(() => {
          console.log("Sending forced onRuntimeInitialized message");
          postMessage({ cmd: 'onRuntimeInitialized' });
        }, 1000);
      }
    },
    printErr: function(text) {
      console.error('WASM stderr:', text);
    },
    onAbort: function(what) {
      console.error('WASM aborted:', what);
      postMessage({
        cmd: 'onAppDataLoadingProgress',
        progress: 0.0,
        step: 'Error: WebAssembly aborted: ' + what,
      });
    },
    onRuntimeInitialized: async function() {
      console.log('WebAssembly module runtime initialized');
      moduleInitialized = true;
      clearTimeout(initTimeout);

      try {
        console.log('Initializing Google Logging');
        if (typeof Module.InitGoogleLogging !== 'function') {
          console.error('InitGoogleLogging is not a function');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.0,
            step: 'Error: InitGoogleLogging is not a function',
          });
          return;
        }

        Module.InitGoogleLogging();
        console.log('Creating Engine instance');

        if (typeof Module.Engine !== 'function') {
          console.error('Engine is not a constructor');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.0,
            step: 'Error: Engine is not a constructor',
          });
          return;
        }

        pppEngine = new Module.Engine();
        postMessage({
          cmd: 'onAppDataLoadingProgress',
          progress: 0.5,
          step: 'Loading configuration ...',
        });

        console.log('Fetching configuration');
        let res = await fetch('/assets/config.json');
        if (!res.ok) {
          console.error('Failed to fetch config.json:', res.status, res.statusText);
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.0,
            step: 'Error: Failed to fetch config.json',
          });
          return;
        }

        const configJson = await res.text();
        console.log('Configuring engine with config:', configJson.substring(0, 100) + '...');

        if (typeof pppEngine.configure !== 'function') {
          console.error('configure is not a function');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.0,
            step: 'Error: configure is not a function',
          });
          return;
        }

        pppEngine.configure(configJson);

        if (typeof pppEngine.isConfigured !== 'function') {
          console.error('isConfigured is not a function');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.0,
            step: 'Error: isConfigured is not a function',
          });
          return;
        }

        const isConfigured = pppEngine.isConfigured();
        console.log('Engine configured:', isConfigured);

        if (isConfigured) {
          console.log('Engine is configured, sending onRuntimeInitialized message');

          // First update the progress
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 1.0,
            step: 'Application ready',
          });

          // Then send the runtime initialized message
          setTimeout(() => {
            console.log('Sending delayed onRuntimeInitialized message');
            postMessage({ cmd: 'onRuntimeInitialized' });
          }, 500); // Add a small delay to ensure the progress message is processed first
        } else {
          console.error('Engine configuration failed');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.0,
            step: 'Error: Engine configuration failed',
          });
        }
      } catch (error) {
        console.error('Error during WebAssembly initialization:', error);
        postMessage({
          cmd: 'onAppDataLoadingProgress',
          progress: 0.0,
          step: 'Error: ' + (error.message || 'Unknown error during initialization'),
        });
      }
    },
    setStatus: function(text) {
      console.log('WASM status:', text);
    },
    totalDependencies: 0,
    monitorRunDependencies: function(left) {
      console.log('WASM dependencies remaining:', left);
      this.totalDependencies = Math.max(this.totalDependencies, left);
      if (left) {
        postMessage({
          cmd: 'onAppDataLoadingProgress',
          progress: 0.2 + (1 - left / this.totalDependencies) * 0.3,
          step: 'Loading dependencies: ' + (this.totalDependencies - left) + '/' + this.totalDependencies,
        });
      }
    }
  };

  postMessage({
    cmd: 'onAppDataLoadingProgress',
    progress: 0.1,
    step: 'Warming up ...',
  });

  try {
    console.log('Importing WebAssembly script');
    importScripts('ppp-wasm.js');
    console.log('WebAssembly script imported successfully');

    // If the script is imported but Module is not defined properly
    if (typeof Module !== 'object' || Module === null) {
      console.error('Module object not properly defined after importing WebAssembly script');
      postMessage({
        cmd: 'onAppDataLoadingProgress',
        progress: 0.0,
        step: 'Error: Module object not properly defined',
      });
    }
  } catch (error) {
    console.error('Error importing WebAssembly script:', error);
    postMessage({
      cmd: 'onAppDataLoadingProgress',
      progress: 0.0,
      step: 'Error: ' + (error.message || 'Failed to import WebAssembly script'),
    });
  }

  postMessage({
    cmd: 'onAppDataLoadingProgress',
    progress: 0.2,
    step: 'Compiling wasm code ...',
  });

  addEventListener('message', (e) => {
    switch (e.data.cmd) {
      case 'start':
        // Add a check after a delay to see if the engine is ready
        setTimeout(() => {
          console.log('Checking if engine is ready...');
          if (moduleInitialized && typeof pppEngine !== 'undefined' && pppEngine) {
            console.log('Engine is ready, sending initialization message');
            postMessage({
              cmd: 'onAppDataLoadingProgress',
              progress: 1.0,
              step: 'Application ready',
            });
            postMessage({ cmd: 'onRuntimeInitialized' });
          } else {
            console.log('Engine is not ready yet, current state:', {
              moduleInitialized,
              pppEngine: typeof pppEngine !== 'undefined'
            });
          }
        }, 3000); // Check after 3 seconds
        break;
      case 'checkStatus':
        // Add a direct way to check the status
        if (moduleInitialized && typeof pppEngine !== 'undefined' && pppEngine) {
          console.log('Engine is ready (status check)');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 1.0,
            step: 'Application ready',
          });
          postMessage({ cmd: 'onRuntimeInitialized' });
        } else {
          console.log('Engine is not ready (status check)');
          postMessage({
            cmd: 'onAppDataLoadingProgress',
            progress: 0.5,
            step: 'Still initializing...',
          });
        }
        break;
      case 'setImage':
        setImage(e.data.imageData);
        break;
      case 'detectLandmarks':
        detectLandmarks(e.data.imgKey);
        break;
      case 'createTiledPrint':
        createTilePrint(e.data.request);
        break;
    }
  });

  function _stringToPtr(str) {
    if (typeof lengthBytesUTF8 === 'function' && typeof Module._malloc === 'function' && typeof stringToUTF8 === 'function') {
      const str_len = lengthBytesUTF8(str);
      let ptr = Module._malloc(str_len + 1);
      stringToUTF8(str, ptr, str_len + 1);
      return ptr;
    }
    console.error('WebAssembly helper functions not available');
    return 0;
  }

  function _arrayToHeap(typedArray) {
    const numBytes = typedArray.length * typedArray.BYTES_PER_ELEMENT;
    const ptr = Module._malloc(numBytes);
    let heapBytes = Module.HEAPU8.subarray(ptr, ptr + numBytes);
    heapBytes.set(typedArray);
    return [ptr, numBytes];
  }

  async function setImage(imageDataArrayBuf) {
    const imageData = new Uint8Array(imageDataArrayBuf);
    const metadata = pppEngine.setImage(imageData);
    const imageMetadata = JSON.parse(metadata);
    let cvMat = pppEngine.getImage(imageMetadata.imgKey);
    const imgBuf = cvMat.data.slice(0);
    postMessage(
      {
        cmd: 'onImageSet',
        imgKey: imageMetadata.imgKey,
        EXIFInfo: imageMetadata.EXIFInfo,
        imageBuffer: imgBuf.buffer,
        width: cvMat.cols,
        height: cvMat.rows,
      },
      [imgBuf.buffer]
    );
    cvMat.delete();
  }

  function detectLandmarks(imgKey) {
    const landMarksStr = pppEngine.detectLandmarks(imgKey);
    postMessage({ cmd: 'onLandmarksDetected', landmarks: JSON.parse(landMarksStr) });
  }
}
