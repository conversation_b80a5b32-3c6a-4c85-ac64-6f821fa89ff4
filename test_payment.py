#!/usr/bin/env python3
"""
Test script for payment integration.
This script tests the payment endpoints and database functionality.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.database import db_service
from app.services.payment_service import payment_service
from app.models.payment import PaymentInitiateRequest, Currency, DownloadType

# Load environment variables
load_dotenv()

async def test_database_connection():
    """Test database connection and basic operations"""
    print("🔍 Testing database connection...")
    
    try:
        # Test user creation
        user_data = {
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User",
            "phone_number": "+1234567890",
            "country": "US"
        }
        
        user = await db_service.create_user(user_data)
        if user:
            print(f"✅ User created successfully: {user.email}")
            
            # Test user retrieval
            retrieved_user = await db_service.get_user_by_email("<EMAIL>")
            if retrieved_user:
                print(f"✅ User retrieved successfully: {retrieved_user.id}")
            else:
                print("❌ Failed to retrieve user")
                return False
        else:
            print("❌ Failed to create user")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

async def test_payment_initiation():
    """Test payment initiation"""
    print("\n🔍 Testing payment initiation...")
    
    try:
        # Create a test payment request
        payment_request = PaymentInitiateRequest(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            phone_number="+1234567890",
            country="US",
            image_id="test_image_123",
            standard_id="us_passport",
            download_type=DownloadType.DIGITAL,
            currency=Currency.USD
        )
        
        # Initiate payment
        response = await payment_service.initiate_payment(payment_request)
        
        if response.success:
            print(f"✅ Payment initiated successfully")
            print(f"   Order ID: {response.order_id}")
            print(f"   Amount: ${response.amount}")
            print(f"   Currency: {response.currency}")
            if response.checkout_url:
                print(f"   Checkout URL: {response.checkout_url[:50]}...")
            return True
        else:
            print(f"❌ Payment initiation failed: {response.error}")
            return False
        
    except Exception as e:
        print(f"❌ Payment test failed: {str(e)}")
        return False

async def test_pricing():
    """Test pricing configuration"""
    print("\n🔍 Testing pricing configuration...")
    
    try:
        from app.models.payment import PricingConfig
        
        # Test USD pricing
        usd_digital = PricingConfig.get_price(DownloadType.DIGITAL, Currency.USD)
        usd_print = PricingConfig.get_price(DownloadType.PRINT, Currency.USD)
        
        print(f"✅ USD Pricing:")
        print(f"   Digital: ${usd_digital}")
        print(f"   Print: ${usd_print}")
        
        # Test KES pricing
        kes_digital = PricingConfig.get_price(DownloadType.DIGITAL, Currency.KES)
        kes_print = PricingConfig.get_price(DownloadType.PRINT, Currency.KES)
        
        print(f"✅ KES Pricing:")
        print(f"   Digital: KES {kes_digital}")
        print(f"   Print: KES {kes_print}")
        
        # Test currency detection
        us_currency = PricingConfig.get_currency_for_country("US")
        ke_currency = PricingConfig.get_currency_for_country("KE")
        
        print(f"✅ Currency Detection:")
        print(f"   US -> {us_currency}")
        print(f"   KE -> {ke_currency}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pricing test failed: {str(e)}")
        return False

def check_environment():
    """Check if all required environment variables are set"""
    print("🔍 Checking environment configuration...")
    
    required_vars = [
        "SUPABASE_URL",
        "SUPABASE_KEY",
        "INTASEND_PUBLISHABLE_KEY",
        "INTASEND_SECRET_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease add these to your .env file")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

async def main():
    """Main test function"""
    print("🧪 Testing Passport Photo Maker Payment Integration")
    print("=" * 55)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed!")
        return
    
    # Test database
    db_success = await test_database_connection()
    
    # Test pricing
    pricing_success = test_pricing()
    
    # Test payment (only if IntaSend keys are available)
    payment_success = True
    if os.getenv("INTASEND_PUBLISHABLE_KEY") and os.getenv("INTASEND_SECRET_KEY"):
        payment_success = await test_payment_initiation()
    else:
        print("\n⚠️  Skipping payment test (IntaSend keys not configured)")
    
    # Summary
    print("\n" + "=" * 55)
    print("📊 Test Summary:")
    print(f"   Database: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"   Pricing: {'✅ PASS' if pricing_success else '❌ FAIL'}")
    print(f"   Payment: {'✅ PASS' if payment_success else '❌ FAIL'}")
    
    if db_success and pricing_success and payment_success:
        print("\n🎉 All tests passed! Payment integration is ready.")
        print("\nNext steps:")
        print("1. Start the application: python -m uvicorn app.main:app --reload")
        print("2. Open http://localhost:8000 in your browser")
        print("3. Upload an image and test the payment flow")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
