#!/usr/bin/env python3
"""
Quick test to verify background removal is working.
"""

import requests
import json

def test_status():
    """Test the status endpoint."""
    print("🔍 Testing background removal status...")
    try:
        response = requests.get('http://localhost:8000/api/background-removal/status')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {data}")
            return data.get('enabled', False)
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_frontend():
    """Test if the main page loads."""
    print("🌐 Testing frontend...")
    try:
        response = requests.get('http://localhost:8000/')
        if response.status_code == 200:
            print("✅ Frontend loads successfully")
            # Check if background removal section is in the HTML
            if 'background-removal-section' in response.text:
                print("✅ Background removal section found in HTML")
                return True
            else:
                print("❌ Background removal section not found in HTML")
                return False
        else:
            print(f"❌ Frontend failed to load: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Quick Background Removal Test")
    print("=" * 40)
    
    status_ok = test_status()
    frontend_ok = test_frontend()
    
    print("\n📊 Results:")
    print(f"   - API Status: {'✅' if status_ok else '❌'}")
    print(f"   - Frontend: {'✅' if frontend_ok else '❌'}")
    
    if status_ok and frontend_ok:
        print("\n🎉 Background removal is working!")
        print("📝 Instructions:")
        print("   1. Open http://localhost:8000 in your browser")
        print("   2. Upload an image")
        print("   3. You should see the 'Background Removal' section appear")
        print("   4. Click 'Remove Background' to test the functionality")
    else:
        print("\n❌ Issues detected. Check the server logs.")
