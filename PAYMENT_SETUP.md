# Payment Integration Setup Guide

This guide will help you set up the IntaSend payment integration for the Passport Photo Maker application.

## 🚀 Quick Start

### 1. Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# IntaSend Payment Configuration
INTASEND_PUBLISHABLE_KEY=your_publishable_key_here
INTASEND_SECRET_KEY=your_secret_key_here
INTASEND_TEST_MODE=true

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_anon_key_here

# Application Configuration
BASE_URL=http://localhost:8000
```

### 2. Get IntaSend API Keys

1. **For Testing (Sandbox):**
   - Visit: https://sandbox.intasend.com/
   - No sign-up needed for testing
   - Get your test API keys

2. **For Production:**
   - Visit: https://intasend.com/
   - Create an account
   - Complete verification
   - Get your live API keys

### 3. Set Up Supabase Database

1. **Create a Supabase Project:**
   - Visit: https://supabase.com/
   - Create a new project
   - Get your project URL and anon key

2. **Run Database Setup:**
   ```bash
   python setup_database.py
   ```

### 4. Install Dependencies

```bash
pip install intasend-python supabase asyncpg
```

### 5. Test the Integration

```bash
python test_payment.py
```

## 💰 Pricing Configuration

Current pricing is set to:
- **Digital Photo**: $13.99 USD / KES 1000
- **Print Layout**: $13.99 USD / KES 1000
- **Bundle**: $13.99 USD / KES 1000

To modify pricing, edit `app/models/payment.py`:

```python
PRICING = {
    Currency.USD: {
        DownloadType.DIGITAL: 13.99,
        DownloadType.PRINT: 13.99,
        DownloadType.BUNDLE: 13.99
    },
    Currency.KES: {
        DownloadType.DIGITAL: 1000.0,
        DownloadType.PRINT: 1000.0,
        DownloadType.BUNDLE: 1000.0
    }
}
```

## 🔧 How It Works

### Payment Flow

1. **User uploads and processes image** (FREE)
2. **User sees watermarked preview** (FREE)
3. **User clicks download** → Payment modal opens
4. **User enters details** → Payment form
5. **User pays via IntaSend** → Checkout window
6. **Payment confirmed** → Download access granted
7. **User downloads** → Secure, time-limited link

### Technical Architecture

```
Frontend (HTML/JS) → FastAPI Backend → IntaSend API
                                   ↓
                              Supabase Database
                                   ↓
                              Download Service
```

### Database Schema

- **users**: Session-based user information
- **orders**: Payment tracking and order management
- **download_permissions**: Access control for downloads

## 🛡️ Security Features

- **Secure API Keys**: Stored server-side only
- **Time-limited Downloads**: Links expire after 15 minutes
- **Download Limits**: 5 downloads per purchase
- **Payment Verification**: All downloads require payment confirmation
- **Webhook Validation**: Secure payment status updates

## 🧪 Testing

### Test Payment Flow

1. Start the application:
   ```bash
   python -m uvicorn app.main:app --reload
   ```

2. Open http://localhost:8000

3. Upload a test image

4. Process the image

5. Click "Download" to test payment flow

### Test Cards (Sandbox)

Use these test card numbers in sandbox mode:

- **Visa**: 4242 4242 4242 4242
- **Mastercard**: 5555 5555 5555 4444
- **Expiry**: Any future date
- **CVC**: Any 3 digits

### Test M-Pesa (Sandbox)

- **Phone**: +254700000000
- **Amount**: Any amount
- **PIN**: 1234

## 🔄 Webhook Configuration

IntaSend will send payment notifications to:
```
POST /api/payment/webhook
```

Make sure your application is accessible from the internet for webhooks to work in production.

## 📊 Monitoring

### Check Payment Status

```bash
curl http://localhost:8000/api/payment/status/{order_id}
```

### View User Orders

```bash
curl http://localhost:8000/api/payment/orders/{user_email}
```

### Get Pricing Information

```bash
curl http://localhost:8000/api/payment/pricing
```

## 🚨 Troubleshooting

### Common Issues

1. **"Payment system not available"**
   - Check if payment.js is loaded
   - Verify environment variables
   - Check browser console for errors

2. **"Database connection failed"**
   - Verify Supabase credentials
   - Check if tables exist
   - Run setup_database.py again

3. **"IntaSend API error"**
   - Verify API keys are correct
   - Check if in test mode for sandbox
   - Ensure internet connectivity

4. **"Popup blocked"**
   - Allow popups for your domain
   - Check browser popup settings

### Debug Mode

Enable debug logging by setting:
```bash
export LOG_LEVEL=DEBUG
```

## 🔄 Going Live

### Production Checklist

- [ ] Get live IntaSend API keys
- [ ] Set `INTASEND_TEST_MODE=false`
- [ ] Configure production Supabase project
- [ ] Set up SSL certificate (HTTPS required)
- [ ] Configure webhook URL in IntaSend dashboard
- [ ] Test with real payment methods
- [ ] Set up monitoring and alerts

### Environment Variables for Production

```bash
INTASEND_PUBLISHABLE_KEY=live_pub_key_here
INTASEND_SECRET_KEY=live_secret_key_here
INTASEND_TEST_MODE=false
BASE_URL=https://yourdomain.com
```

## 📞 Support

- **IntaSend Documentation**: https://developers.intasend.com/
- **Supabase Documentation**: https://supabase.com/docs
- **Application Issues**: Check the GitHub repository

## 🎉 Success!

If everything is set up correctly, you should see:
- ✅ Payment modal opens when clicking download
- ✅ IntaSend checkout window opens
- ✅ Payment confirmation works
- ✅ Download links are generated
- ✅ Files download successfully

Your passport photo maker is now monetized! 🚀
