# Passport Photo Maker

A web application for creating passport photos that meet international standards. This application uses facial landmark detection to properly position and crop photos according to various country-specific requirements.

## Features

- **Advanced Facial Landmark Detection**: Uses a combination of MediaPipe Face Mesh, face_recognition, and OpenCV for robust facial landmark detection
- **Face Alignment**: Automatically aligns faces so that eyes are horizontal
- **Crown Point Detection**: Accurately estimates the top of the head for proper photo framing
- **Multiple Fallback Mechanisms**: Ensures reliable detection even in challenging cases
- **Visualization Tools**: Provides visual feedback of detected landmarks for verification
- **API Endpoints**: RESTful API for integration with other applications
- **Photo Standards**: Support for multiple countries and document types
- **Photo Processing**: Automatic cropping and resizing according to standards
- **Tiled Printing**: Create printable sheets with multiple copies
- **User Location Detection**: Automatically detect user location for relevant standards

## Setup

1. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the application:
   ```
   python run.py
   ```

4. Access the application at http://localhost:8000

## API Endpoints

### Landmarks
- `POST /api/upload`: Upload an image for processing
- `GET /api/landmarks/{image_id}`: Get detected landmarks for an image
- `GET /api/aligned-image/{image_id}`: Get the aligned face image
- `GET /api/visualization/{image_id}`: Get a visualization of detected landmarks
- `POST /api/update-crown-chin`: Update crown and chin points manually

### Photo Standards
- `GET /api/photostandards`: Get all photo standards
- `GET /api/photostandards/{standard_id}`: Get a specific photo standard
- `GET /api/photostandards/countries`: Get a list of all countries
- `GET /api/photostandards/doctypes`: Get a list of all document types

### Photo Processing
- `POST /api/photo/process`: Process a photo according to a standard
- `POST /api/photo/tiled-print`: Create a tiled print with multiple copies
- `GET /api/photo/image/{image_id}`: Get a processed image

### Location
- `GET /api/location`: Get the user's location based on IP address

## Technologies Used

- FastAPI
- MediaPipe
- OpenCV
- face_recognition
- Python 3.10+
- NumPy
- Pillow
- JSON for photo standards

## License

MIT
