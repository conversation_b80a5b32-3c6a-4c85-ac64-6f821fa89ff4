-- Passport Photo Maker Database Schema for Supabase
-- This schema supports the payment integration with IntaSend

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for session-based users
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone_number VARCHAR(20),
    country VARCHAR(2), -- ISO country code
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on email for faster lookups
CREATE INDEX idx_users_email ON users(email);

-- Orders table for tracking payments
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    image_id VARCHAR(255) NOT NULL, -- Reference to processed image
    standard_id VARCHAR(255) NOT NULL, -- Photo standard used
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL CHECK (currency IN ('USD', 'KES')),
    download_type VARCHAR(20) NOT NULL CHECK (download_type IN ('digital', 'print', 'bundle')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'failed', 'expired', 'refunded')),
    intasend_ref VARCHAR(255), -- IntaSend invoice/reference ID
    checkout_url TEXT, -- IntaSend checkout URL
    payment_method VARCHAR(50), -- M-PESA, CARD-PAYMENT, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    paid_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for orders
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_intasend_ref ON orders(intasend_ref);
CREATE INDEX idx_orders_image_id ON orders(image_id);
CREATE INDEX idx_orders_created_at ON orders(created_at DESC);

-- Download permissions table
CREATE TABLE download_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    image_id VARCHAR(255) NOT NULL,
    download_type VARCHAR(20) NOT NULL CHECK (download_type IN ('digital', 'print', 'bundle')),
    download_count INTEGER NOT NULL DEFAULT 0,
    max_downloads INTEGER NOT NULL DEFAULT 5,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for download permissions
CREATE INDEX idx_download_permissions_order_id ON download_permissions(order_id);
CREATE INDEX idx_download_permissions_user_id ON download_permissions(user_id);
CREATE INDEX idx_download_permissions_image_id ON download_permissions(image_id);

-- Create unique constraint to prevent duplicate permissions per order
CREATE UNIQUE INDEX idx_download_permissions_unique_order ON download_permissions(order_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically expire old orders
CREATE OR REPLACE FUNCTION expire_old_orders()
RETURNS void AS $$
BEGIN
    UPDATE orders 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending' 
    AND expires_at < NOW();
END;
$$ language 'plpgsql';

-- Function to clean up expired download permissions
CREATE OR REPLACE FUNCTION cleanup_expired_permissions()
RETURNS void AS $$
BEGIN
    DELETE FROM download_permissions 
    WHERE expires_at < NOW();
END;
$$ language 'plpgsql';

-- Row Level Security (RLS) policies for Supabase
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE download_permissions ENABLE ROW LEVEL SECURITY;

-- RLS policies for users table
CREATE POLICY "Users can view their own data" ON users
    FOR SELECT USING (true); -- Allow read access for service

CREATE POLICY "Users can insert their own data" ON users
    FOR INSERT WITH CHECK (true); -- Allow insert for service

CREATE POLICY "Users can update their own data" ON users
    FOR UPDATE USING (true); -- Allow update for service

-- RLS policies for orders table
CREATE POLICY "Orders can be viewed by service" ON orders
    FOR SELECT USING (true);

CREATE POLICY "Orders can be inserted by service" ON orders
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Orders can be updated by service" ON orders
    FOR UPDATE USING (true);

-- RLS policies for download_permissions table
CREATE POLICY "Download permissions can be viewed by service" ON download_permissions
    FOR SELECT USING (true);

CREATE POLICY "Download permissions can be inserted by service" ON download_permissions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Download permissions can be updated by service" ON download_permissions
    FOR UPDATE USING (true);

-- Views for analytics and reporting

-- View for order statistics
CREATE VIEW order_stats AS
SELECT 
    DATE(created_at) as order_date,
    currency,
    download_type,
    status,
    COUNT(*) as order_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount
FROM orders
GROUP BY DATE(created_at), currency, download_type, status
ORDER BY order_date DESC;

-- View for user activity
CREATE VIEW user_activity AS
SELECT 
    u.id,
    u.email,
    u.first_name,
    u.last_name,
    u.country,
    COUNT(o.id) as total_orders,
    COUNT(CASE WHEN o.status = 'paid' THEN 1 END) as paid_orders,
    SUM(CASE WHEN o.status = 'paid' THEN o.amount ELSE 0 END) as total_spent,
    MAX(o.created_at) as last_order_date
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.email, u.first_name, u.last_name, u.country
ORDER BY total_spent DESC;

-- View for download activity
CREATE VIEW download_activity AS
SELECT 
    dp.id,
    dp.order_id,
    u.email,
    dp.download_type,
    dp.download_count,
    dp.max_downloads,
    (dp.max_downloads - dp.download_count) as downloads_remaining,
    dp.created_at,
    dp.expires_at
FROM download_permissions dp
JOIN users u ON dp.user_id = u.id
ORDER BY dp.created_at DESC;

-- Comments for documentation
COMMENT ON TABLE users IS 'Session-based users who purchase passport photos';
COMMENT ON TABLE orders IS 'Payment orders tracking IntaSend transactions';
COMMENT ON TABLE download_permissions IS 'Download permissions granted after successful payment';

COMMENT ON COLUMN orders.intasend_ref IS 'IntaSend invoice/reference ID for tracking payments';
COMMENT ON COLUMN orders.expires_at IS 'When the payment link expires (24 hours from creation)';
COMMENT ON COLUMN download_permissions.max_downloads IS 'Maximum number of downloads allowed (default: 5)';
COMMENT ON COLUMN download_permissions.expires_at IS 'When download permission expires (30 days from creation)';

-- Sample data for testing (optional - remove in production)
-- INSERT INTO users (email, first_name, last_name, phone_number, country) VALUES
-- ('<EMAIL>', 'Test', 'User', '+254700000000', 'KE'),
-- ('<EMAIL>', 'John', 'Doe', '+1234567890', 'US');

-- Grant necessary permissions for the service role
-- Note: In Supabase, you'll need to configure these through the dashboard
-- GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
-- GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
