from typing import Optional, List
from pydantic import BaseModel, Field


class PrintDefinition(BaseModel):
    """
    Definition of a print layout for passport photos.

    Attributes:
        id: Unique identifier for the print definition
        title: Display name for the print layout
        height: Height of the print in specified units
        width: Width of the print in specified units
        resolution: Resolution in DPI (dots per inch)
        units: Unit of measurement (inch, cm, mm)
        padding: Padding around the print in specified units
        gutter: Space between photos in the print in specified units
        custom: Whether this is a custom user-defined layout
        paper_color: Background color of the paper (default: white)
        show_borders: Whether to show borders around individual photos
        border_width: Width of the border around individual photos in specified units
        border_color: Color of the border around individual photos
        maximize_photos: Whether to automatically maximize the number of photos on the page
        custom_photo_count: Whether to use a custom number of photos instead of maximizing
        num_rows: Number of rows for custom photo count
        num_cols: Number of columns for custom photo count
    """
    id: Optional[str] = None
    title: str
    height: float
    width: float
    resolution: float
    units: str
    padding: float = 0.0
    gutter: float = 0.0
    custom: bool = False
    paper_color: str = "#ffffff"
    show_borders: bool = False
    border_width: float = 0.02
    border_color: str = "#000000"
    maximize_photos: bool = False
    custom_photo_count: bool = False
    num_rows: int = 2
    num_cols: int = 2

    class Config:
        schema_extra = {
            "example": {
                "title": "6″ x 4″",
                "height": 4,
                "width": 6,
                "resolution": 300,
                "units": "inch",
                "padding": 0.06,
                "gutter": 0.1,
                "custom": False,
                "paper_color": "#ffffff",
                "show_borders": False,
                "border_width": 0.02,
                "border_color": "#000000",
                "maximize_photos": False,
                "custom_photo_count": False,
                "num_rows": 2,
                "num_cols": 2
            }
        }


class PrintDefinitionCreate(BaseModel):
    """
    Model for creating a new print definition.
    """
    title: str
    height: float
    width: float
    resolution: float
    units: str
    padding: float = 0.0
    gutter: float = 0.0
    paper_color: str = "#ffffff"
    show_borders: bool = False
    border_width: float = 0.02
    border_color: str = "#000000"
    maximize_photos: bool = False
    custom_photo_count: bool = False
    num_rows: int = 2
    num_cols: int = 2


class PrintDefinitionUpdate(BaseModel):
    """
    Model for updating an existing print definition.
    """
    title: Optional[str] = None
    height: Optional[float] = None
    width: Optional[float] = None
    resolution: Optional[float] = None
    units: Optional[str] = None
    padding: Optional[float] = None
    gutter: Optional[float] = None
    paper_color: Optional[str] = None
    show_borders: Optional[bool] = None
    border_width: Optional[float] = None
    border_color: Optional[str] = None
    maximize_photos: Optional[bool] = None
    custom_photo_count: Optional[bool] = None
    num_rows: Optional[int] = None
    num_cols: Optional[int] = None


class PrintDefinitionResponse(BaseModel):
    """
    Response model for print definition operations.
    """
    print_definition: Optional[PrintDefinition] = None
    print_definitions: Optional[List[PrintDefinition]] = []
    error: Optional[str] = None

    class Config:
        """Configuration for the model."""
        # Allow arbitrary types for better compatibility
        arbitrary_types_allowed = True

        # Example for documentation
        schema_extra = {
            "example": {
                "print_definition": {
                    "id": "6x4-inch",
                    "title": "6″ x 4″",
                    "height": 4,
                    "width": 6,
                    "resolution": 300,
                    "units": "inch",
                    "padding": 0.06,
                    "gutter": 0.1,
                    "custom": False,
                    "paper_color": "#ffffff",
                    "show_borders": False,
                    "border_width": 0.02,
                    "border_color": "#000000",
                    "maximize_photos": False,
                    "custom_photo_count": False,
                    "num_rows": 2,
                    "num_cols": 2
                },
                "print_definitions": [
                    {
                        "id": "6x4-inch",
                        "title": "6″ x 4″",
                        "height": 4,
                        "width": 6,
                        "resolution": 300,
                        "units": "inch",
                        "padding": 0.06,
                        "gutter": 0.1,
                        "custom": False,
                        "paper_color": "#ffffff",
                        "show_borders": False,
                        "border_width": 0.02,
                        "border_color": "#000000",
                        "maximize_photos": False,
                        "custom_photo_count": False,
                        "num_rows": 2,
                        "num_cols": 2
                    }
                ],
                "error": None
            }
        }
