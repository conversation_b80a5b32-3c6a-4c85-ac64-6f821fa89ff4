from typing import List, Optional, Dict, Any
from pydantic import BaseModel

class Point(BaseModel):
    """
    A 2D point representing a facial landmark.
    """
    x: float
    y: float

class CrownChinPointPair(BaseModel):
    """
    A pair of points representing the crown and chin points.
    """
    crownPoint: Point
    chinPoint: Point

class KeyFacialPoints(BaseModel):
    """
    Key facial points as shown in the reference image (A through Q).
    These are the essential points for passport photo alignment.
    """
    # Eye points
    A: Optional[Point] = None  # Left eye pupil
    B: Optional[Point] = None  # Right eye pupil

    # Center line points
    P: Optional[Point] = None  # Top of head (crown)
    M: Optional[Point] = None  # Between eyes
    N: Optional[Point] = None  # Nose tip
    Q: Optional[Point] = None  # Bottom of chin

    # Mouth points
    C: Optional[Point] = None  # Left mouth corner
    D: Optional[Point] = None  # Right mouth corner

class FaceLandmarks(BaseModel):
    """
    Facial landmarks detected in an image.
    """
    # Face rectangle
    top: int
    right: int
    bottom: int
    left: int

    # Facial landmarks
    chin: List[Point]
    left_eyebrow: List[Point]
    right_eyebrow: List[Point]
    nose_bridge: List[Point]
    nose_tip: List[Point]
    left_eye: List[Point]
    right_eye: List[Point]
    top_lip: List[Point]
    bottom_lip: List[Point]

    # Crown-chin points
    crown_chin: Optional[CrownChinPointPair] = None

    # Key facial points (A-Q) for simplified display
    key_points: Optional[KeyFacialPoints] = None

class ImageUploadResponse(BaseModel):
    """
    Response model for image upload.
    """
    image_id: str
    width: int
    height: int
    format: str

class LandmarkDetectionResponse(BaseModel):
    """
    Response model for landmark detection.
    """
    image_id: str
    landmarks: Optional[FaceLandmarks] = None
    error: Optional[str] = None

class LandmarkUpdateRequest(BaseModel):
    """
    Request model for updating landmarks.
    """
    image_id: str
    crown_point: Point
    chin_point: Point
