from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class PaymentStatus(str, Enum):
    """Payment status enumeration"""
    PENDING = "pending"
    PAID = "paid"
    FAILED = "failed"
    EXPIRED = "expired"
    REFUNDED = "refunded"


class DownloadType(str, Enum):
    """Download type enumeration"""
    DIGITAL = "digital"
    PRINT = "print"
    BUNDLE = "bundle"


class Currency(str, Enum):
    """Supported currencies"""
    USD = "USD"
    KES = "KES"


class User(BaseModel):
    """User model for session-based users"""
    id: Optional[str] = None
    email: str
    full_name: Optional[str] = None
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # Additional fields for payment processing (not stored in DB)
    phone_number: Optional[str] = None
    country: Optional[str] = None

    # Computed properties for compatibility
    @property
    def first_name(self) -> str:
        if self.full_name:
            return self.full_name.split(" ")[0]
        return ""

    @property
    def last_name(self) -> str:
        if self.full_name and " " in self.full_name:
            return self.full_name.split(" ", 1)[1]
        return ""


class Order(BaseModel):
    """Order model for tracking payments"""
    id: Optional[str] = None
    user_id: str
    image_id: str
    standard_id: str
    amount: float
    currency: Currency
    download_type: DownloadType
    status: PaymentStatus = PaymentStatus.PENDING
    intasend_ref: Optional[str] = None
    checkout_url: Optional[str] = None
    payment_method: Optional[str] = None
    created_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class DownloadPermission(BaseModel):
    """Download permission model"""
    id: Optional[str] = None
    order_id: str
    user_id: str
    image_id: str
    download_type: DownloadType
    download_count: int = 0
    max_downloads: int = 5
    expires_at: Optional[datetime] = None
    created_at: Optional[datetime] = None


# Request/Response Models

class UserCreateRequest(BaseModel):
    """Request model for creating a user"""
    email: str = Field(..., description="User email address")
    first_name: str = Field(..., description="User first name")
    last_name: str = Field(..., description="User last name")
    phone_number: Optional[str] = Field(None, description="User phone number (optional)")
    country: Optional[str] = Field(None, description="User country code")


class OrderCreateRequest(BaseModel):
    """Request model for creating an order"""
    user_id: str = Field(..., description="User ID")
    image_id: str = Field(..., description="Processed image ID")
    standard_id: str = Field(..., description="Photo standard ID")
    download_type: DownloadType = Field(..., description="Type of download")
    currency: Currency = Field(Currency.USD, description="Payment currency")


class PaymentInitiateRequest(BaseModel):
    """Request model for initiating payment"""
    email: str = Field(..., description="User email address")
    first_name: str = Field(..., description="User first name")
    last_name: str = Field(..., description="User last name")
    phone_number: Optional[str] = Field(None, description="User phone number")
    country: Optional[str] = Field(None, description="User country")
    image_id: str = Field(..., description="Processed image ID")
    standard_id: str = Field(..., description="Photo standard ID")
    download_type: DownloadType = Field(..., description="Type of download")
    currency: Currency = Field(Currency.USD, description="Payment currency")


class PaymentResponse(BaseModel):
    """Response model for payment operations"""
    success: bool
    order_id: Optional[str] = None
    checkout_url: Optional[str] = None
    amount: Optional[float] = None
    currency: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None


class OrderStatusResponse(BaseModel):
    """Response model for order status"""
    order_id: str
    status: PaymentStatus
    amount: float
    currency: Currency
    download_type: DownloadType
    downloads_remaining: int
    paid_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class DownloadLinkResponse(BaseModel):
    """Response model for download links"""
    success: bool
    download_url: Optional[str] = None
    downloads_remaining: Optional[int] = None
    expires_at: Optional[datetime] = None
    error: Optional[str] = None


class WebhookPayload(BaseModel):
    """IntaSend webhook payload model"""
    invoice_id: str
    state: str
    provider: str
    charges: float
    net_amount: float
    currency: str
    value: float
    account: str
    api_ref: Optional[str] = None
    mpesa_reference: Optional[str] = None
    card_reference: Optional[str] = None
    created_at: str
    updated_at: str


# Pricing Configuration

class PricingConfig:
    """Pricing configuration for different markets"""

    PRICING = {
        Currency.USD: {
            DownloadType.DIGITAL: 13.99,
            DownloadType.PRINT: 13.99,
            DownloadType.BUNDLE: 13.99
        },
        Currency.KES: {
            DownloadType.DIGITAL: 1000.0,
            DownloadType.PRINT: 1000.0,
            DownloadType.BUNDLE: 1000.0
        }
    }

    @classmethod
    def get_price(cls, download_type: DownloadType, currency: Currency) -> float:
        """Get price for a specific download type and currency"""
        return cls.PRICING[currency][download_type]

    @classmethod
    def get_currency_for_country(cls, country_code: Optional[str]) -> Currency:
        """Determine currency based on country code"""
        if country_code and country_code.upper() == "KE":
            return Currency.KES
        return Currency.USD
