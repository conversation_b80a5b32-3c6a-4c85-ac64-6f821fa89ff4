from typing import Optional
from pydantic import BaseModel

class LocationResponse(BaseModel):
    """
    Response model for the location endpoint.
    """
    error: Optional[int] = None
    alpha2: Optional[str] = None
    alpha3: Optional[str] = None
    name: Optional[str] = None
    continent: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    message: Optional[str] = None

    # Allow additional fields
    class Config:
        extra = "allow"
