from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class PhotoDimensions(BaseModel):
    """
    Dimensions of a photo standard.
    """
    pictureWidth: float
    pictureHeight: float
    units: str
    dpi: float
    faceHeight: float
    crownTop: Optional[float] = None
    bottomEyeLine: Optional[float] = None


class PhotoStandard(BaseModel):
    """
    A photo standard for a specific country and document type.
    """
    id: str
    text: str
    country: str
    docType: str
    dimensions: PhotoDimensions
    backgroundColor: str
    printable: bool
    officialLinks: List[str]
    comments: Optional[str] = None
    thumbnail: Optional[str] = None
    visible: Optional[bool] = True


class PhotoStandardResponse(BaseModel):
    """
    Response model for photo standards.
    """
    standards: List[PhotoStandard]
    count: int


class PhotoStandardDetailResponse(BaseModel):
    """
    Response model for a single photo standard.
    """
    standard: PhotoStandard


class PhotoProcessingRequest(BaseModel):
    """
    Request model for photo processing.
    """
    image_id: str
    standard_id: str
    crown_point: Optional[Dict[str, float]] = None
    chin_point: Optional[Dict[str, float]] = None


class PhotoProcessingResponse(BaseModel):
    """
    Response model for photo processing.
    """
    image_id: str
    processed_image_id: str
    standard_id: str
    width: int
    height: int
    format: str
    error: Optional[str] = None


class PhotoPreviewRequest(BaseModel):
    """
    Request model for generating a preview SVG with compliance markers.
    """
    image_id: str
    standard_id: str
    crown_point: Optional[Dict[str, float]] = None
    chin_point: Optional[Dict[str, float]] = None
