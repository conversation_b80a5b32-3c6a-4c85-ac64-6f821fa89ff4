import os
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import uuid
from supabase import create_client, Client
from app.models.payment import (
    User, Order, DownloadPermission, PaymentStatus, 
    DownloadType, Currency
)

logger = logging.getLogger(__name__)


class DatabaseService:
    """Database service for Supabase integration"""
    
    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        
        if not self.supabase_url or not self.supabase_key:
            logger.error("Supabase credentials not found in environment variables")
            raise ValueError("Supabase credentials not configured")
        
        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        logger.info("Database service initialized with Supabase")
    
    # User operations
    
    async def create_user(self, user_data: Dict[str, Any]) -> Optional[User]:
        """Create a new user"""
        try:
            user_data["id"] = str(uuid.uuid4())
            user_data["created_at"] = datetime.utcnow().isoformat()
            user_data["updated_at"] = datetime.utcnow().isoformat()
            
            result = self.client.table("users").insert(user_data).execute()
            
            if result.data:
                return User(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        try:
            result = self.client.table("users").select("*").eq("email", email).execute()
            
            if result.data:
                return User(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by email: {str(e)}")
            return None
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            result = self.client.table("users").select("*").eq("id", user_id).execute()
            
            if result.data:
                return User(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by ID: {str(e)}")
            return None
    
    # Order operations
    
    async def create_order(self, order_data: Dict[str, Any]) -> Optional[Order]:
        """Create a new order"""
        try:
            order_data["id"] = str(uuid.uuid4())
            order_data["created_at"] = datetime.utcnow().isoformat()
            order_data["expires_at"] = (datetime.utcnow() + timedelta(hours=24)).isoformat()
            
            result = self.client.table("orders").insert(order_data).execute()
            
            if result.data:
                return Order(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error creating order: {str(e)}")
            return None
    
    async def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """Get order by ID"""
        try:
            result = self.client.table("orders").select("*").eq("id", order_id).execute()
            
            if result.data:
                return Order(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting order by ID: {str(e)}")
            return None
    
    async def get_order_by_intasend_ref(self, intasend_ref: str) -> Optional[Order]:
        """Get order by IntaSend reference"""
        try:
            result = self.client.table("orders").select("*").eq("intasend_ref", intasend_ref).execute()
            
            if result.data:
                return Order(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting order by IntaSend ref: {str(e)}")
            return None
    
    async def update_order_status(self, order_id: str, status: PaymentStatus, 
                                 payment_method: Optional[str] = None) -> bool:
        """Update order status"""
        try:
            update_data = {
                "status": status.value,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            if status == PaymentStatus.PAID:
                update_data["paid_at"] = datetime.utcnow().isoformat()
            
            if payment_method:
                update_data["payment_method"] = payment_method
            
            result = self.client.table("orders").update(update_data).eq("id", order_id).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Error updating order status: {str(e)}")
            return False
    
    async def update_order_intasend_ref(self, order_id: str, intasend_ref: str, 
                                       checkout_url: str) -> bool:
        """Update order with IntaSend reference and checkout URL"""
        try:
            update_data = {
                "intasend_ref": intasend_ref,
                "checkout_url": checkout_url,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = self.client.table("orders").update(update_data).eq("id", order_id).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Error updating order IntaSend ref: {str(e)}")
            return False
    
    # Download permission operations
    
    async def create_download_permission(self, permission_data: Dict[str, Any]) -> Optional[DownloadPermission]:
        """Create download permission"""
        try:
            permission_data["id"] = str(uuid.uuid4())
            permission_data["created_at"] = datetime.utcnow().isoformat()
            permission_data["expires_at"] = (datetime.utcnow() + timedelta(days=30)).isoformat()
            
            result = self.client.table("download_permissions").insert(permission_data).execute()
            
            if result.data:
                return DownloadPermission(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error creating download permission: {str(e)}")
            return None
    
    async def get_download_permission(self, order_id: str) -> Optional[DownloadPermission]:
        """Get download permission by order ID"""
        try:
            result = self.client.table("download_permissions").select("*").eq("order_id", order_id).execute()
            
            if result.data:
                return DownloadPermission(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting download permission: {str(e)}")
            return None
    
    async def increment_download_count(self, permission_id: str) -> bool:
        """Increment download count"""
        try:
            # First get current count
            result = self.client.table("download_permissions").select("download_count").eq("id", permission_id).execute()
            
            if not result.data:
                return False
            
            current_count = result.data[0]["download_count"]
            new_count = current_count + 1
            
            # Update count
            update_result = self.client.table("download_permissions").update({
                "download_count": new_count
            }).eq("id", permission_id).execute()
            
            return len(update_result.data) > 0
            
        except Exception as e:
            logger.error(f"Error incrementing download count: {str(e)}")
            return False
    
    async def get_user_orders(self, user_id: str) -> List[Order]:
        """Get all orders for a user"""
        try:
            result = self.client.table("orders").select("*").eq("user_id", user_id).order("created_at", desc=True).execute()
            
            return [Order(**order) for order in result.data]
            
        except Exception as e:
            logger.error(f"Error getting user orders: {str(e)}")
            return []


# Global database service instance
db_service = DatabaseService()
