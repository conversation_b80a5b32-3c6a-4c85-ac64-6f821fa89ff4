import os
import logging
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from intasend import APIService
from app.models.payment import (
    User, Order, PaymentStatus, DownloadType, Currency, PricingConfig,
    PaymentInitiateRequest, PaymentResponse, OrderStatusResponse
)
from app.services.database import db_service

logger = logging.getLogger(__name__)


class PaymentService:
    """Payment service for IntaSend integration"""

    def __init__(self):
        self.publishable_key = os.getenv("INTASEND_PUBLISHABLE_KEY")
        self.secret_key = os.getenv("INTASEND_SECRET_KEY")
        self.test_mode = os.getenv("INTASEND_TEST_MODE", "true").lower() == "true"

        if not self.publishable_key or not self.secret_key:
            logger.error("IntaSend credentials not found in environment variables")
            raise ValueError("IntaSend credentials not configured")

        self.api_service = APIService(
            token=self.secret_key,
            publishable_key=self.publishable_key,
            test=self.test_mode
        )

        logger.info(f"Payment service initialized (test_mode: {self.test_mode})")

    async def initiate_payment(self, request: PaymentInitiateRequest) -> PaymentResponse:
        """Initiate payment process"""
        try:
            # 1. Get or create user
            user = await self._get_or_create_user(request)
            if not user:
                return PaymentResponse(
                    success=False,
                    error="Failed to create user"
                )

            # 2. Determine currency and amount
            currency = self._determine_currency(request.currency, request.country)
            amount = PricingConfig.get_price(request.download_type, currency)

            # 3. Create order
            order = await self._create_order(user.id, request, currency, amount)
            if not order:
                return PaymentResponse(
                    success=False,
                    error="Failed to create order"
                )

            # 4. Generate IntaSend checkout link
            checkout_url, intasend_ref = await self._create_checkout_link(order, user)
            if not checkout_url:
                return PaymentResponse(
                    success=False,
                    error="Failed to create checkout link"
                )

            # 5. Update order with IntaSend reference
            await db_service.update_order_intasend_ref(order.id, intasend_ref, checkout_url)

            return PaymentResponse(
                success=True,
                order_id=order.id,
                checkout_url=checkout_url,
                amount=amount,
                currency=currency.value,
                message="Payment initiated successfully"
            )

        except Exception as e:
            logger.error(f"Error initiating payment: {str(e)}")
            return PaymentResponse(
                success=False,
                error=f"Payment initiation failed: {str(e)}"
            )

    async def check_payment_status(self, order_id: str) -> OrderStatusResponse:
        """Check payment status for an order"""
        try:
            order = await db_service.get_order_by_id(order_id)
            if not order:
                raise ValueError("Order not found")

            # If already paid, return current status
            if order.status == PaymentStatus.PAID:
                permission = await db_service.get_download_permission(order_id)
                downloads_remaining = 0
                if permission:
                    downloads_remaining = permission.max_downloads - permission.download_count

                return OrderStatusResponse(
                    order_id=order_id,
                    status=order.status,
                    amount=order.amount,
                    currency=order.currency,
                    download_type=order.download_type,
                    downloads_remaining=downloads_remaining,
                    paid_at=order.paid_at,
                    expires_at=order.expires_at
                )

            # Check with IntaSend if payment is pending
            if order.intasend_invoice_id and order.status == PaymentStatus.PENDING:
                intasend_status = await self._check_intasend_status(order.intasend_invoice_id)
                if intasend_status and intasend_status.get("state") == "COMPLETE":
                    # Update order status
                    await db_service.update_order_status(
                        order_id,
                        PaymentStatus.PAID,
                        intasend_status.get("provider")
                    )

                    # Create download permission
                    await self._create_download_permission(order)

                    order.status = PaymentStatus.PAID
                    order.paid_at = datetime.utcnow()

            permission = await db_service.get_download_permission(order_id)
            downloads_remaining = 0
            if permission:
                downloads_remaining = permission.max_downloads - permission.download_count

            return OrderStatusResponse(
                order_id=order_id,
                status=order.status,
                amount=order.amount,
                currency=order.currency,
                download_type=order.download_type,
                downloads_remaining=downloads_remaining,
                paid_at=order.paid_at,
                expires_at=order.expires_at
            )

        except Exception as e:
            logger.error(f"Error checking payment status: {str(e)}")
            raise

    async def handle_webhook(self, webhook_data: Dict[str, Any]) -> bool:
        """Handle IntaSend webhook notification"""
        try:
            invoice_id = webhook_data.get("invoice_id")
            state = webhook_data.get("state")

            if not invoice_id:
                logger.error("No invoice_id in webhook data")
                return False

            # Find order by IntaSend reference
            order = await db_service.get_order_by_intasend_ref(invoice_id)
            if not order:
                logger.error(f"Order not found for IntaSend ref: {invoice_id}")
                return False

            # Update order status based on webhook state
            if state == "COMPLETE":
                await db_service.update_order_status(
                    order.id,
                    PaymentStatus.PAID,
                    webhook_data.get("provider")
                )

                # Create download permission
                await self._create_download_permission(order)

                logger.info(f"Payment completed for order {order.id}")

            elif state in ["FAILED", "CANCELLED"]:
                await db_service.update_order_status(order.id, PaymentStatus.FAILED)
                logger.info(f"Payment failed for order {order.id}")

            return True

        except Exception as e:
            logger.error(f"Error handling webhook: {str(e)}")
            return False

    # Private helper methods

    async def _get_or_create_user(self, request: PaymentInitiateRequest) -> Optional[User]:
        """Get existing user or create new one"""
        user = await db_service.get_user_by_email(request.email)
        if user:
            return user

        user_data = {
            "email": request.email,
            "first_name": request.first_name,
            "last_name": request.last_name,
            "phone_number": request.phone_number,
            "country": request.country
        }

        return await db_service.create_user(user_data)

    def _determine_currency(self, requested_currency: Currency, country: Optional[str]) -> Currency:
        """Determine the appropriate currency"""
        if country and country.upper() == "KE":
            return Currency.KES
        return requested_currency

    async def _create_order(self, user_id: str, request: PaymentInitiateRequest,
                           currency: Currency, amount: float) -> Optional[Order]:
        """Create a new order"""
        order_data = {
            "user_id": user_id,
            "image_id": request.image_id,
            "standard_id": request.standard_id,
            "amount": amount,
            "currency": currency.value,
            "download_type": request.download_type.value,
            "status": PaymentStatus.PENDING.value
        }

        return await db_service.create_order(order_data)

    async def _create_checkout_link(self, order: Order, user: User) -> Tuple[Optional[str], Optional[str]]:
        """Create IntaSend checkout link"""
        try:
            # Prepare checkout data
            checkout_data = {
                "amount": order.amount,
                "currency": order.currency.value,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "api_ref": order.id,
                "comment": f"Passport Photo - {order.download_type.value.title()}",
                "redirect_url": f"{os.getenv('BASE_URL', 'http://localhost:8000')}/payment/success?order_id={order.id}"
            }

            if user.phone_number:
                checkout_data["phone_number"] = user.phone_number

            if user.country:
                checkout_data["country"] = user.country

            # Create checkout link
            response = self.api_service.collect.checkout(**checkout_data)

            checkout_url = response.get("url")
            invoice_id = response.get("invoice_id")

            return checkout_url, invoice_id

        except Exception as e:
            logger.error(f"Error creating checkout link: {str(e)}")
            return None, None

    async def _check_intasend_status(self, intasend_ref: str) -> Optional[Dict[str, Any]]:
        """Check payment status with IntaSend"""
        try:
            # Note: This would require IntaSend API method to check status
            # For now, we'll rely on webhooks
            return None

        except Exception as e:
            logger.error(f"Error checking IntaSend status: {str(e)}")
            return None

    async def _create_download_permission(self, order: Order) -> bool:
        """Create download permission after successful payment"""
        try:
            permission_data = {
                "order_id": order.id,
                "user_id": order.user_id,
                "image_id": order.image_id,
                "download_type": order.download_type.value,
                "download_count": 0,
                "max_downloads": 5
            }

            permission = await db_service.create_download_permission(permission_data)
            return permission is not None

        except Exception as e:
            logger.error(f"Error creating download permission: {str(e)}")
            return False


# Global payment service instance
payment_service = PaymentService()
