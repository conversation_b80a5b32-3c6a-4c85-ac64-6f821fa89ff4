import os
import json
import logging
import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import math

from app.models.photostandard import PhotoStandard, PhotoDimensions
from app.models.landmarks import Point, CrownChinPointPair
from app.services.landmark_detection import image_store
from app.services.dpi_service import DpiService

logger = logging.getLogger(__name__)

# Path to the photo standards JSON file
STANDARDS_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "photo-standards.json")

# Cache for photo standards
_photo_standards_cache = None


def load_photo_standards() -> List[PhotoStandard]:
    """
    Load photo standards from the JSON file.

    Returns:
        List of PhotoStandard objects
    """
    global _photo_standards_cache

    if _photo_standards_cache is not None:
        return _photo_standards_cache

    try:
        with open(STANDARDS_FILE, 'r') as f:
            standards_data = json.load(f)

        standards = [PhotoStandard.model_validate(standard) for standard in standards_data]
        _photo_standards_cache = standards
        return standards
    except Exception as e:
        logger.error(f"Error loading photo standards: {str(e)}")
        return []


def get_all_photo_standards() -> List[PhotoStandard]:
    """
    Get all photo standards.

    Returns:
        List of all PhotoStandard objects
    """
    return load_photo_standards()


def get_photo_standard(standard_id: str) -> Optional[PhotoStandard]:
    """
    Get a photo standard by ID.

    Args:
        standard_id: ID of the photo standard

    Returns:
        PhotoStandard object or None if not found
    """
    standards = load_photo_standards()
    for standard in standards:
        if standard.id == standard_id:
            return standard
    return None


def get_photo_standards_by_country(country: str) -> List[PhotoStandard]:
    """
    Get photo standards for a specific country.

    Args:
        country: Country name

    Returns:
        List of PhotoStandard objects
    """
    standards = load_photo_standards()
    return [standard for standard in standards if standard.country.lower() == country.lower()]


def get_photo_standards_by_doc_type(doc_type: str) -> List[PhotoStandard]:
    """
    Get photo standards for a specific document type.

    Args:
        doc_type: Document type

    Returns:
        List of PhotoStandard objects
    """
    standards = load_photo_standards()
    return [standard for standard in standards if standard.docType.lower() == doc_type.lower()]


def convert_units(value: float, from_unit: str, to_unit: str, dpi: float = 300.0) -> float:
    """
    Convert a value from one unit to another.

    Args:
        value: Value to convert
        from_unit: Source unit (mm, inch, pixel)
        to_unit: Target unit (mm, inch, pixel)
        dpi: Dots per inch (for pixel conversions)

    Returns:
        Converted value
    """
    # Use DpiService for unit conversion
    return DpiService.convert_units(value, from_unit, to_unit, dpi)


def process_photo(image_id: str, standard_id: str, crown_point: Optional[Point] = None,
                 chin_point: Optional[Point] = None) -> Tuple[Optional[np.ndarray], Optional[str]]:
    """
    Process a photo according to a specific standard.

    Args:
        image_id: ID of the image to process
        standard_id: ID of the photo standard to use
        crown_point: Optional crown point (if not provided, will use detected landmarks)
        chin_point: Optional chin point (if not provided, will use detected landmarks)

    Returns:
        Tuple of (processed image as numpy array, error message or None)
    """
    # Check if image exists
    if image_id not in image_store:
        return None, f"Image with ID {image_id} not found"

    # Get the image and landmarks
    img = image_store[image_id]['image']
    landmarks = image_store[image_id].get('landmarks')

    if landmarks is None:
        return None, "No landmarks detected for this image"

    # Get the photo standard
    standard = get_photo_standard(standard_id)
    if standard is None:
        return None, f"Photo standard with ID {standard_id} not found"

    # Use provided crown and chin points or get them from landmarks
    if crown_point is None or chin_point is None:
        if landmarks.crown_chin is None:
            return None, "No crown-chin points detected and none provided"
        crown_pt = landmarks.crown_chin.crownPoint
        chin_pt = landmarks.crown_chin.chinPoint
    else:
        crown_pt = crown_point
        chin_pt = chin_point

    # Process the photo
    try:
        processed_img = crop_photo(img, crown_pt, chin_pt, standard, landmarks)
        return processed_img, None
    except Exception as e:
        logger.error(f"Error processing photo: {str(e)}")
        return None, f"Error processing photo: {str(e)}"


def crop_photo(img: np.ndarray, crown_point: Point, chin_point: Point,
              standard: PhotoStandard, landmarks=None) -> np.ndarray:
    """
    Crop a photo according to a specific standard.

    Args:
        img: Input image as numpy array
        crown_point: Crown point
        chin_point: Chin point
        standard: Photo standard to use

    Returns:
        Cropped image as numpy array
    """
    # Get image dimensions
    height, width = img.shape[:2]

    # Calculate face height in pixels
    face_height_px = math.sqrt((crown_point.x - chin_point.x)**2 +
                              (crown_point.y - chin_point.y)**2)

    # Calculate required dimensions in pixels using DpiService
    dimensions = standard.dimensions
    std_width_px = DpiService.convert_to_pixels(dimensions.pictureWidth, dimensions.units, dimensions.dpi)
    std_height_px = DpiService.convert_to_pixels(dimensions.pictureHeight, dimensions.units, dimensions.dpi)
    std_face_height_px = DpiService.convert_to_pixels(dimensions.faceHeight, dimensions.units, dimensions.dpi)

    # Calculate scale factor
    scale_factor = std_face_height_px / face_height_px

    # Calculate center point between crown and chin
    center_x = (crown_point.x + chin_point.x) / 2
    center_y = (crown_point.y + chin_point.y) / 2

    # Calculate crop dimensions
    crop_width = std_width_px / scale_factor
    crop_height = std_height_px / scale_factor

    # Calculate crop coordinates
    crop_left = center_x - crop_width / 2
    crop_top = center_y - crop_height / 2

    # Adjust for crown position if specified
    if dimensions.crownTop is not None:
        crown_top_px = DpiService.convert_to_pixels(dimensions.crownTop, dimensions.units, dimensions.dpi)
        crown_top_adjusted = crown_top_px / scale_factor
        crop_top = crown_point.y - crown_top_adjusted

    # Adjust for eye line position if specified
    elif dimensions.bottomEyeLine is not None and landmarks.key_points and landmarks.key_points.A and landmarks.key_points.B:
        eye_line_bottom_px = DpiService.convert_to_pixels(dimensions.bottomEyeLine, dimensions.units, dimensions.dpi)
        eye_y = (landmarks.key_points.A.y + landmarks.key_points.B.y) / 2
        crop_top = eye_y - (std_height_px - eye_line_bottom_px) / scale_factor

    # Ensure crop is within image bounds
    crop_left = max(0, min(width - crop_width, crop_left))
    crop_top = max(0, min(height - crop_height, crop_top))

    # Crop the image
    crop = img[int(crop_top):int(crop_top + crop_height),
              int(crop_left):int(crop_left + crop_width)]

    # Resize to standard dimensions
    resized = cv2.resize(crop, (int(std_width_px), int(std_height_px)))

    return resized
