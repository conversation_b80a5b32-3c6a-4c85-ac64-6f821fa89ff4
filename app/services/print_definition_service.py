import json
import os
import uuid
import logging
from typing import List, Optional, Dict, Any, Tuple

from app.models.print_definition import PrintDefinition

logger = logging.getLogger(__name__)

# Path to the print definitions JSON file
PRINT_DEFINITIONS_FILE = os.path.join(os.path.dirname(__file__), "../data/print-definitions.json")

# In-memory cache of print definitions
_print_definitions: List[PrintDefinition] = []
_custom_print_definitions: Dict[str, PrintDefinition] = {}


def load_print_definitions() -> List[PrintDefinition]:
    """
    Load print definitions from the JSON file.
    
    Returns:
        List of PrintDefinition objects
    """
    global _print_definitions
    
    if _print_definitions:
        return _print_definitions
    
    try:
        with open(PRINT_DEFINITIONS_FILE, 'r') as f:
            data = json.load(f)
            
        _print_definitions = []
        for item in data:
            # Generate an ID if not present
            if 'id' not in item:
                item['id'] = str(uuid.uuid4())
                
            # Convert to PrintDefinition model
            pd = PrintDefinition(**item)
            _print_definitions.append(pd)
            
        return _print_definitions
    except Exception as e:
        logger.error(f"Error loading print definitions: {str(e)}")
        return []


def get_print_definition(print_definition_id: str) -> Optional[PrintDefinition]:
    """
    Get a print definition by ID.
    
    Args:
        print_definition_id: ID of the print definition
        
    Returns:
        PrintDefinition object or None if not found
    """
    # Check custom print definitions first
    if print_definition_id in _custom_print_definitions:
        return _custom_print_definitions[print_definition_id]
    
    # Then check predefined print definitions
    for pd in load_print_definitions():
        if pd.id == print_definition_id:
            return pd
    
    return None


def get_all_print_definitions() -> List[PrintDefinition]:
    """
    Get all available print definitions.
    
    Returns:
        List of PrintDefinition objects
    """
    # Combine predefined and custom print definitions
    return list(_custom_print_definitions.values()) + load_print_definitions()


def create_print_definition(print_definition: Dict[str, Any]) -> Tuple[Optional[PrintDefinition], Optional[str]]:
    """
    Create a new custom print definition.
    
    Args:
        print_definition: Dictionary with print definition data
        
    Returns:
        Tuple of (PrintDefinition object or None, error message or None)
    """
    try:
        # Generate an ID for the new print definition
        print_definition_id = str(uuid.uuid4())
        print_definition['id'] = print_definition_id
        print_definition['custom'] = True
        
        # Create a PrintDefinition object
        pd = PrintDefinition(**print_definition)
        
        # Store in the custom print definitions dictionary
        _custom_print_definitions[print_definition_id] = pd
        
        return pd, None
    except Exception as e:
        logger.error(f"Error creating print definition: {str(e)}")
        return None, f"Error creating print definition: {str(e)}"


def update_print_definition(print_definition_id: str, print_definition: Dict[str, Any]) -> Tuple[Optional[PrintDefinition], Optional[str]]:
    """
    Update an existing custom print definition.
    
    Args:
        print_definition_id: ID of the print definition to update
        print_definition: Dictionary with updated print definition data
        
    Returns:
        Tuple of (PrintDefinition object or None, error message or None)
    """
    # Only custom print definitions can be updated
    if print_definition_id not in _custom_print_definitions:
        return None, f"Print definition with ID {print_definition_id} not found or is not a custom definition"
    
    try:
        # Get the existing print definition
        existing_pd = _custom_print_definitions[print_definition_id]
        
        # Update the fields
        for key, value in print_definition.items():
            if hasattr(existing_pd, key) and value is not None:
                setattr(existing_pd, key, value)
        
        # Store the updated print definition
        _custom_print_definitions[print_definition_id] = existing_pd
        
        return existing_pd, None
    except Exception as e:
        logger.error(f"Error updating print definition: {str(e)}")
        return None, f"Error updating print definition: {str(e)}"


def delete_print_definition(print_definition_id: str) -> Tuple[bool, Optional[str]]:
    """
    Delete a custom print definition.
    
    Args:
        print_definition_id: ID of the print definition to delete
        
    Returns:
        Tuple of (success boolean, error message or None)
    """
    # Only custom print definitions can be deleted
    if print_definition_id not in _custom_print_definitions:
        return False, f"Print definition with ID {print_definition_id} not found or is not a custom definition"
    
    try:
        # Remove the print definition from the dictionary
        del _custom_print_definitions[print_definition_id]
        
        return True, None
    except Exception as e:
        logger.error(f"Error deleting print definition: {str(e)}")
        return False, f"Error deleting print definition: {str(e)}"
