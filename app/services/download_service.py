import os
import logging
import uuid
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException
from fastapi.responses import Response
import cv2
from app.models.payment import DownloadLinkResponse, PaymentStatus
from app.services.database import db_service
from app.services.landmark_detection import image_store
from app.services import photo_processing

logger = logging.getLogger(__name__)


class DownloadService:
    """Service for handling secure downloads after payment"""

    def __init__(self):
        self.temp_links = {}  # In-memory storage for temporary download links
        self.link_expiry_minutes = 15  # Download links expire after 15 minutes

    async def generate_download_link(self, order_id: str) -> DownloadLinkResponse:
        """Generate a secure, time-limited download link"""
        try:
            # Get order details
            order = await db_service.get_order_by_id(order_id)
            if not order:
                return DownloadLinkResponse(
                    success=False,
                    error="Order not found"
                )

            # Verify payment status
            if order.status != PaymentStatus.PAID:
                return DownloadLinkResponse(
                    success=False,
                    error="Payment not completed"
                )

            # Get download permission
            permission = await db_service.get_download_permission(order_id)
            if not permission:
                return DownloadLinkResponse(
                    success=False,
                    error="Download permission not found"
                )

            # Check download limits
            if permission.download_count >= permission.max_downloads:
                return DownloadLinkResponse(
                    success=False,
                    error="Download limit exceeded"
                )

            # Check expiry
            if permission.expires_at and datetime.utcnow() > permission.expires_at:
                return DownloadLinkResponse(
                    success=False,
                    error="Download permission expired"
                )

            # Generate temporary download token
            download_token = str(uuid.uuid4())
            expires_at = datetime.utcnow() + timedelta(minutes=self.link_expiry_minutes)

            # Store temporary link info
            self.temp_links[download_token] = {
                "order_id": order_id,
                "permission_id": permission.id,
                "image_id": order.image_id,
                "download_type": order.download_type,
                "expires_at": expires_at
            }

            # Generate download URL
            base_url = os.getenv("BASE_URL", "http://localhost:8000")
            download_url = f"{base_url}/api/payment/secure-download/{download_token}"

            return DownloadLinkResponse(
                success=True,
                download_url=download_url,
                downloads_remaining=permission.max_downloads - permission.download_count,
                expires_at=expires_at
            )

        except Exception as e:
            logger.error(f"Error generating download link: {str(e)}")
            return DownloadLinkResponse(
                success=False,
                error=f"Failed to generate download link: {str(e)}"
            )

    async def generate_download_token(self, image_id: str, standard_id: str,
                                    download_type: str, order_id: str) -> Optional[str]:
        """Generate a temporary download token"""
        try:
            # Generate temporary download token
            download_token = str(uuid.uuid4())
            expires_at = datetime.utcnow() + timedelta(minutes=self.link_expiry_minutes)

            # Store temporary link info
            self.temp_links[download_token] = {
                "order_id": order_id,
                "image_id": image_id,
                "download_type": download_type,
                "standard_id": standard_id,
                "expires_at": expires_at
            }

            return download_token

        except Exception as e:
            logger.error(f"Error generating download token: {str(e)}")
            return None

    async def process_secure_download(self, download_token: str) -> Response:
        """Process a secure download using the temporary token"""
        try:
            # Check if token exists and is valid
            if download_token not in self.temp_links:
                raise HTTPException(status_code=404, detail="Download link not found or expired")

            link_info = self.temp_links[download_token]

            # Check if link has expired
            if datetime.utcnow() > link_info["expires_at"]:
                # Clean up expired link
                del self.temp_links[download_token]
                raise HTTPException(status_code=410, detail="Download link expired")

            # Get order and permission details
            order = await db_service.get_order_by_id(link_info["order_id"])
            permission = await db_service.get_download_permission(link_info["order_id"])

            if not order or not permission:
                raise HTTPException(status_code=404, detail="Order or permission not found")

            # Double-check download limits
            if permission.download_count >= permission.max_downloads:
                raise HTTPException(status_code=403, detail="Download limit exceeded")

            # Generate the file based on download type
            standard_id = link_info.get("standard_id", order.standard_id if order else "")
            file_content, filename, content_type = await self._generate_download_file(
                link_info["image_id"],
                link_info["download_type"],
                standard_id
            )

            if not file_content:
                raise HTTPException(status_code=500, detail="Failed to generate download file")

            # Increment download count
            await db_service.increment_download_count(permission.id)

            # Clean up the temporary link (one-time use)
            del self.temp_links[download_token]

            # Return the file
            headers = {
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": content_type
            }

            return Response(
                content=file_content,
                headers=headers,
                media_type=content_type
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing secure download: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

    async def _generate_download_file(self, image_id: str, download_type: str,
                                    standard_id: str) -> tuple[Optional[bytes], str, str]:
        """Generate the actual file for download"""
        try:
            if download_type == "digital":
                return await self._generate_digital_photo(image_id, standard_id)
            elif download_type == "print":
                return await self._generate_print_layout(image_id, standard_id)
            elif download_type == "bundle":
                # For bundle, we'll return the digital version
                # In a real implementation, you might create a ZIP file with both
                return await self._generate_digital_photo(image_id, standard_id)
            else:
                logger.error(f"Unknown download type: {download_type}")
                return None, "", ""

        except Exception as e:
            logger.error(f"Error generating download file: {str(e)}")
            return None, "", ""

    async def _generate_digital_photo(self, image_id: str, standard_id: str) -> tuple[Optional[bytes], str, str]:
        """Generate high-quality digital photo"""
        try:
            # Get the processed image from image store
            if image_id not in image_store:
                logger.error(f"Image {image_id} not found in image store")
                return None, "", ""

            img_data = image_store[image_id]
            img = img_data["image"]

            # Ensure high quality encoding
            encode_params = [
                cv2.IMWRITE_JPEG_QUALITY, 100,
                cv2.IMWRITE_PNG_COMPRESSION, 0
            ]

            # Encode as high-quality JPEG
            success, buffer = cv2.imencode('.jpg', img, encode_params)
            if not success:
                logger.error("Failed to encode image as JPEG")
                return None, "", ""

            filename = f"passport_photo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            content_type = "image/jpeg"

            return buffer.tobytes(), filename, content_type

        except Exception as e:
            logger.error(f"Error generating digital photo: {str(e)}")
            return None, "", ""

    async def _generate_print_layout(self, image_id: str, standard_id: str) -> tuple[Optional[bytes], str, str]:
        """Generate print layout with multiple photos"""
        try:
            # Create a default print layout (2x2 on standard paper)
            tiled_image_id, error = photo_processing.create_tiled_print(
                image_id=image_id,
                num_rows=2,
                num_cols=2,
                spacing_mm=2.0,
                print_definition_id="6x4_inch"  # Default print size
            )

            if error or not tiled_image_id:
                logger.error(f"Failed to create print layout: {error}")
                return None, "", ""

            # Get the tiled image
            if tiled_image_id not in image_store:
                logger.error(f"Tiled image {tiled_image_id} not found")
                return None, "", ""

            tiled_img_data = image_store[tiled_image_id]
            tiled_img = tiled_img_data["image"]

            # Encode as high-quality JPEG
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 100]
            success, buffer = cv2.imencode('.jpg', tiled_img, encode_params)

            if not success:
                logger.error("Failed to encode tiled image as JPEG")
                return None, "", ""

            filename = f"passport_print_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            content_type = "image/jpeg"

            return buffer.tobytes(), filename, content_type

        except Exception as e:
            logger.error(f"Error generating print layout: {str(e)}")
            return None, "", ""

    def cleanup_expired_links(self):
        """Clean up expired temporary download links"""
        try:
            current_time = datetime.utcnow()
            expired_tokens = [
                token for token, info in self.temp_links.items()
                if current_time > info["expires_at"]
            ]

            for token in expired_tokens:
                del self.temp_links[token]

            if expired_tokens:
                logger.info(f"Cleaned up {len(expired_tokens)} expired download links")

        except Exception as e:
            logger.error(f"Error cleaning up expired links: {str(e)}")


# Global download service instance
download_service = DownloadService()
