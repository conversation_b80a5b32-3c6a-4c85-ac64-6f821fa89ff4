"""
DPI Service for managing DPI-related operations across the application.
"""
import struct
import binascii
import numpy as np
import cv2
from typing import Tuple, Optional, Union, Literal
from enum import Enum


class PrinterType(str, Enum):
    """Enum for different printer types with their recommended DPI values."""
    INKJET = "inkjet"
    LASER = "laser"
    PHOTO = "photo"
    PROFESSIONAL = "professional"


class DpiService:
    """Service for managing DPI-related operations across the application."""
    
    @staticmethod
    def get_optimal_dpi(standard_dpi: float, print_def_dpi: float) -> float:
        """
        Return the higher DPI value between standard and print definition.
        
        Args:
            standard_dpi: DPI value from the photo standard
            print_def_dpi: DPI value from the print definition
            
        Returns:
            The higher DPI value
        """
        return max(standard_dpi, print_def_dpi)
    
    @staticmethod
    def get_recommended_dpi(printer_type: PrinterType) -> int:
        """
        Get the recommended DPI for a specific printer type.
        
        Args:
            printer_type: Type of printer
            
        Returns:
            Recommended DPI value
        """
        if printer_type == PrinterType.INKJET:
            return 300
        elif printer_type == PrinterType.LASER:
            return 600
        elif printer_type == PrinterType.PHOTO:
            return 300
        elif printer_type == PrinterType.PROFESSIONAL:
            return 1200
        else:
            return 300  # Default
    
    @staticmethod
    def convert_to_pixels(value: float, unit: str, dpi: float) -> float:
        """
        Convert a value from a physical unit to pixels based on DPI.
        
        Args:
            value: Value to convert
            unit: Unit of measurement (pixel, inch, mm, cm)
            dpi: DPI value
            
        Returns:
            Value in pixels
        """
        if unit == "pixel":
            return value
        elif unit == "inch":
            return value * dpi
        elif unit == "mm":
            return value * dpi / 25.4
        elif unit == "cm":
            return value * dpi / 2.54
        else:
            raise ValueError(f"Unknown unit: {unit}")
    
    @staticmethod
    def convert_from_pixels(value: float, unit: str, dpi: float) -> float:
        """
        Convert a value from pixels to a physical unit based on DPI.
        
        Args:
            value: Value in pixels
            unit: Target unit of measurement (pixel, inch, mm, cm)
            dpi: DPI value
            
        Returns:
            Value in the specified unit
        """
        if unit == "pixel":
            return value
        elif unit == "inch":
            return value / dpi
        elif unit == "mm":
            return value / dpi * 25.4
        elif unit == "cm":
            return value / dpi * 2.54
        else:
            raise ValueError(f"Unknown unit: {unit}")
    
    @staticmethod
    def convert_units(value: float, from_unit: str, to_unit: str, dpi: float) -> float:
        """
        Convert a value from one unit to another based on DPI.
        
        Args:
            value: Value to convert
            from_unit: Source unit of measurement
            to_unit: Target unit of measurement
            dpi: DPI value
            
        Returns:
            Converted value
        """
        if from_unit == to_unit:
            return value
        
        # Convert to pixels first
        pixels = DpiService.convert_to_pixels(value, from_unit, dpi)
        
        # Convert from pixels to target unit
        return DpiService.convert_from_pixels(pixels, to_unit, dpi)
    
    @staticmethod
    def embed_dpi_in_image(image_data: Union[bytes, np.ndarray], 
                          dpi: float, 
                          format: Literal["JPEG", "PNG"]) -> bytes:
        """
        Embed DPI information in image data.
        
        Args:
            image_data: Image data as bytes or numpy array
            dpi: DPI value to embed
            format: Image format (JPEG or PNG)
            
        Returns:
            Image data with embedded DPI information
        """
        # Convert numpy array to bytes if needed
        if isinstance(image_data, np.ndarray):
            if format == "JPEG":
                _, buffer = cv2.imencode('.jpg', image_data, [cv2.IMWRITE_JPEG_QUALITY, 100])
                image_data = buffer.tobytes()
            elif format == "PNG":
                _, buffer = cv2.imencode('.png', image_data, [cv2.IMWRITE_PNG_COMPRESSION, 9])
                image_data = buffer.tobytes()
            else:
                raise ValueError(f"Unsupported format: {format}")
        
        # Embed DPI based on format
        if format == "PNG":
            return DpiService._embed_dpi_in_png(image_data, dpi)
        elif format == "JPEG":
            return DpiService._embed_dpi_in_jpeg(image_data, dpi)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    @staticmethod
    def _embed_dpi_in_png(png_data: bytes, dpi: float) -> bytes:
        """
        Embed DPI information in a PNG image.
        
        Args:
            png_data: PNG image data
            dpi: DPI value to embed
            
        Returns:
            PNG data with embedded DPI information
        """
        # Convert DPI to pixels per meter (PNG standard)
        ppm = int(dpi * 39.3701)
        
        # Create pHYs chunk
        phys_chunk = bytearray()
        
        # Chunk length (9 bytes)
        phys_chunk.extend((0, 0, 0, 9))
        
        # Chunk type (pHYs)
        phys_chunk.extend(b'pHYs')
        
        # X resolution (pixels per meter)
        phys_chunk.extend(ppm.to_bytes(4, byteorder='big'))
        
        # Y resolution (pixels per meter)
        phys_chunk.extend(ppm.to_bytes(4, byteorder='big'))
        
        # Unit specifier (1 = meter)
        phys_chunk.append(1)
        
        # Calculate CRC
        crc = DpiService._calculate_png_crc(phys_chunk[4:])
        phys_chunk.extend(crc.to_bytes(4, byteorder='big'))
        
        # Find where to insert the pHYs chunk (after IHDR, before IDAT)
        ihdr_end = png_data.find(b'IHDR') + 4 + 4 + 13 + 4
        
        # Check if pHYs chunk already exists
        phys_pos = png_data.find(b'pHYs')
        if phys_pos > 0:
            # Replace existing pHYs chunk
            phys_start = phys_pos - 4
            chunk_length = struct.unpack('>I', png_data[phys_start:phys_start+4])[0]
            phys_end = phys_start + 4 + 4 + chunk_length + 4
            return png_data[:phys_start] + phys_chunk + png_data[phys_end:]
        else:
            # Insert new pHYs chunk
            return png_data[:ihdr_end] + phys_chunk + png_data[ihdr_end:]
    
    @staticmethod
    def _embed_dpi_in_jpeg(jpeg_data: bytes, dpi: float) -> bytes:
        """
        Embed DPI information in a JPEG image.
        
        Args:
            jpeg_data: JPEG image data
            dpi: DPI value to embed
            
        Returns:
            JPEG data with embedded DPI information
        """
        # JPEG DPI is set in the JFIF APP0 marker
        # Find the APP0 marker
        app0_pos = jpeg_data.find(b'\xFF\xE0')
        if app0_pos < 0:
            # No APP0 marker found, can't embed DPI
            return jpeg_data
        
        # Check if it's a JFIF segment
        jfif_pos = jpeg_data.find(b'JFIF\0', app0_pos, app0_pos + 20)
        if jfif_pos < 0:
            # Not a JFIF segment, can't embed DPI
            return jpeg_data
        
        # Create a new JPEG with updated DPI
        result = bytearray(jpeg_data)
        
        # Set units to dots per inch (1)
        result[jfif_pos + 7] = 1
        
        # Set X density (2 bytes, big endian)
        result[jfif_pos + 8] = (int(dpi) >> 8) & 0xFF
        result[jfif_pos + 9] = int(dpi) & 0xFF
        
        # Set Y density (2 bytes, big endian)
        result[jfif_pos + 10] = (int(dpi) >> 8) & 0xFF
        result[jfif_pos + 11] = int(dpi) & 0xFF
        
        return bytes(result)
    
    @staticmethod
    def _calculate_png_crc(data: bytes) -> int:
        """
        Calculate CRC for PNG chunk.
        
        Args:
            data: Chunk data (type and data fields)
            
        Returns:
            CRC value
        """
        return binascii.crc32(data) & 0xFFFFFFFF
