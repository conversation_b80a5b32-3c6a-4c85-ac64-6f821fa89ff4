import os
import uuid
import logging
import numpy as np
import cv2
import face_recognition
import mediapipe as mp
import math
from PIL import Image, ExifTags
from io import BytesIO
from typing import Dict, Tuple, List, Optional, Any, Union

from app.models.landmarks import Point, FaceLandmarks, CrownChinPointPair, KeyFacialPoints

logger = logging.getLogger(__name__)

# Initialize MediaPipe Face Mesh
mp_face_mesh = mp.solutions.face_mesh
face_mesh = mp_face_mesh.FaceMesh(
    static_image_mode=True,
    max_num_faces=1,
    refine_landmarks=True,
    min_detection_confidence=0.5
)

# In-memory storage for uploaded images and their landmarks
# In a production environment, this would be replaced with a database
image_store: Dict[str, Dict[str, Any]] = {}

def save_uploaded_image(image_data: bytes) -> Tuple[str, int, int, str]:
    """
    Save an uploaded image to memory and return its ID and dimensions.
    Handles EXIF orientation to ensure the image is correctly oriented.

    Args:
        image_data: The binary image data

    Returns:
        Tuple containing image_id, width, height, and format
    """
    try:
        # Open the image using PIL
        img = Image.open(BytesIO(image_data))

        # Generate a unique ID for the image
        image_id = str(uuid.uuid4())

        # Get EXIF data and correct orientation if needed
        try:
            # Get EXIF orientation tag (if it exists)
            for orientation in ExifTags.TAGS.keys():
                if ExifTags.TAGS[orientation] == 'Orientation':
                    break

            exif = img._getexif()
            if exif is not None and orientation in exif:
                orientation_value = exif[orientation]
                logger.debug(f"EXIF Orientation: {orientation_value}")

                # Apply orientation correction
                if orientation_value == 2:
                    # Mirrored horizontally
                    img = img.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
                elif orientation_value == 3:
                    # Rotated 180 degrees
                    img = img.transpose(Image.Transpose.ROTATE_180)
                elif orientation_value == 4:
                    # Mirrored vertically
                    img = img.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
                elif orientation_value == 5:
                    # Mirrored horizontally, then rotated 90 degrees counter-clockwise
                    img = img.transpose(Image.Transpose.FLIP_LEFT_RIGHT).transpose(Image.Transpose.ROTATE_90)
                elif orientation_value == 6:
                    # Rotated 90 degrees counter-clockwise
                    img = img.transpose(Image.Transpose.ROTATE_270)
                elif orientation_value == 7:
                    # Mirrored horizontally, then rotated 90 degrees clockwise
                    img = img.transpose(Image.Transpose.FLIP_LEFT_RIGHT).transpose(Image.Transpose.ROTATE_270)
                elif orientation_value == 8:
                    # Rotated 90 degrees clockwise
                    img = img.transpose(Image.Transpose.ROTATE_90)
        except (AttributeError, KeyError, IndexError) as e:
            # No EXIF data or no orientation tag
            logger.debug(f"No EXIF orientation data: {str(e)}")
            pass

        # Convert image to RGB if it's not already
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Get image dimensions and format
        width, height = img.size
        format = img.format or 'JPEG'

        # Convert PIL image (RGB) to numpy array
        img_array = np.array(img)

        # Convert from RGB to BGR for OpenCV compatibility
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # Save the image to memory
        image_store[image_id] = {
            'image': img_bgr,
            'width': width,
            'height': height,
            'format': format,
            'landmarks': None
        }

        return image_id, width, height, format

    except Exception as e:
        logger.error(f"Error saving image: {str(e)}")
        raise

def align_face(image: np.ndarray, landmarks: Union[FaceLandmarks, dict]) -> Tuple[np.ndarray, dict]:
    """
    This function no longer rotates the image but simply returns the original image
    and the original landmarks to maintain the original photo orientation.

    Args:
        image: Input image
        landmarks: Detected landmarks (either FaceLandmarks object or dictionary with landmark points)

    Returns:
        Tuple containing the original image and the original landmarks
    """
    # Simply return the original image and landmarks without any rotation
    if isinstance(landmarks, FaceLandmarks):
        # Log the eye angle for reference but don't rotate
        if landmarks.left_eye and landmarks.right_eye:
            left_eye_center = (
                sum(p.x for p in landmarks.left_eye) / len(landmarks.left_eye),
                sum(p.y for p in landmarks.left_eye) / len(landmarks.left_eye)
            )
            right_eye_center = (
                sum(p.x for p in landmarks.right_eye) / len(landmarks.right_eye),
                sum(p.y for p in landmarks.right_eye) / len(landmarks.right_eye)
            )

            # Calculate the angle between the eyes and the horizontal
            dy = right_eye_center[1] - left_eye_center[1]
            dx = right_eye_center[0] - left_eye_center[0]

            # Calculate angle in degrees
            angle = np.degrees(np.arctan2(dy, dx))
            logger.debug(f"Eye angle (no rotation): {angle:.2f} degrees")

    # Return the original image and landmarks
    return image, landmarks

def detect_landmarks(image_id: str) -> Optional[FaceLandmarks]:
    """
    Detect facial landmarks in an image using a combination of face_recognition and MediaPipe.
    Keeps the original image orientation and adjusts landmarks to work with it.

    Args:
        image_id: The ID of the image to process

    Returns:
        FaceLandmarks object or None if no face is detected
    """
    if image_id not in image_store:
        logger.error(f"Image with ID {image_id} not found")
        return None

    try:
        # Get the image from the store (already in BGR format)
        img = image_store[image_id]['image']
        height, width = img.shape[:2]

        # Convert image to RGB for MediaPipe processing
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # First try MediaPipe Face Mesh for more accurate landmarks
        mp_results = face_mesh.process(img_rgb)

        if mp_results.multi_face_landmarks and len(mp_results.multi_face_landmarks) > 0:
            # Use MediaPipe landmarks
            landmarks = process_mediapipe_landmarks(mp_results.multi_face_landmarks[0], img, image_id)

            # Calculate eye angle for reference but don't rotate the image
            if landmarks.left_eye and landmarks.right_eye:
                left_eye_center = (
                    sum(p.x for p in landmarks.left_eye) / len(landmarks.left_eye),
                    sum(p.y for p in landmarks.left_eye) / len(landmarks.left_eye)
                )
                right_eye_center = (
                    sum(p.x for p in landmarks.right_eye) / len(landmarks.right_eye),
                    sum(p.y for p in landmarks.right_eye) / len(landmarks.right_eye)
                )

                dy = right_eye_center[1] - left_eye_center[1]
                dx = right_eye_center[0] - left_eye_center[0]
                eye_angle = np.degrees(np.arctan2(dy, dx))
                logger.debug(f"Eye angle (no rotation): {eye_angle:.2f} degrees")

            # Store the original image as the "aligned" image for consistency with frontend
            image_store[image_id]['aligned_image'] = img.copy()

            # Store the landmarks
            image_store[image_id]['original_landmarks'] = landmarks
            image_store[image_id]['landmarks'] = landmarks

            return landmarks
        else:
            # Fall back to face_recognition if MediaPipe fails
            landmarks = process_face_recognition_landmarks(img, image_id)

            if landmarks:
                # Calculate eye angle for reference but don't rotate the image
                if landmarks.left_eye and landmarks.right_eye:
                    left_eye_center = (
                        sum(p.x for p in landmarks.left_eye) / len(landmarks.left_eye),
                        sum(p.y for p in landmarks.left_eye) / len(landmarks.left_eye)
                    )
                    right_eye_center = (
                        sum(p.x for p in landmarks.right_eye) / len(landmarks.right_eye),
                        sum(p.y for p in landmarks.right_eye) / len(landmarks.right_eye)
                    )

                    dy = right_eye_center[1] - left_eye_center[1]
                    dx = right_eye_center[0] - left_eye_center[0]
                    eye_angle = np.degrees(np.arctan2(dy, dx))
                    logger.debug(f"Eye angle (face_recognition, no rotation): {eye_angle:.2f} degrees")

                # Store the original image as the "aligned" image for consistency with frontend
                image_store[image_id]['aligned_image'] = img.copy()

                # Store the landmarks
                image_store[image_id]['original_landmarks'] = landmarks
                image_store[image_id]['landmarks'] = landmarks

                return landmarks

            # If face_recognition also fails, try OpenCV's Haar cascades
            return process_opencv_fallback(img, image_id)

    except Exception as e:
        logger.error(f"Error detecting landmarks: {str(e)}")
        return None

def extract_key_facial_points(landmarks: FaceLandmarks) -> KeyFacialPoints:
    """
    Extract key facial points (A-Q) from the full set of landmarks.
    These are the essential points for passport photo alignment.

    Args:
        landmarks: Full facial landmarks

    Returns:
        KeyFacialPoints object with the essential points
    """
    key_points = KeyFacialPoints()

    # Extract eye pupil points (A, B)
    if landmarks.left_eye and len(landmarks.left_eye) > 0:
        # Calculate center of left eye (pupil)
        left_eye_center = Point(
            x=sum(p.x for p in landmarks.left_eye) / len(landmarks.left_eye),
            y=sum(p.y for p in landmarks.left_eye) / len(landmarks.left_eye)
        )
        key_points.A = left_eye_center  # Left eye pupil

    if landmarks.right_eye and len(landmarks.right_eye) > 0:
        # Calculate center of right eye (pupil)
        right_eye_center = Point(
            x=sum(p.x for p in landmarks.right_eye) / len(landmarks.right_eye),
            y=sum(p.y for p in landmarks.right_eye) / len(landmarks.right_eye)
        )
        key_points.B = right_eye_center  # Right eye pupil

    # Extract mouth corner points (C, D)
    if landmarks.top_lip and len(landmarks.top_lip) > 0 and landmarks.bottom_lip and len(landmarks.bottom_lip) > 0:
        # Combine all lip points
        all_lip_points = landmarks.top_lip + landmarks.bottom_lip

        # Find leftmost and rightmost points
        lip_points_sorted_x = sorted(all_lip_points, key=lambda p: p.x)
        key_points.C = lip_points_sorted_x[0]  # Leftmost point (left corner)
        key_points.D = lip_points_sorted_x[-1]  # Rightmost point (right corner)

    # Extract center line points (P, M, N, Q)
    if landmarks.crown_chin:
        # P is the crown point
        key_points.P = landmarks.crown_chin.crownPoint

        # Q is the chin point
        key_points.Q = landmarks.crown_chin.chinPoint

    # M is between the eyes
    if landmarks.left_eye and landmarks.right_eye:
        left_eye_center = Point(
            x=sum(p.x for p in landmarks.left_eye) / len(landmarks.left_eye),
            y=sum(p.y for p in landmarks.left_eye) / len(landmarks.left_eye)
        )
        right_eye_center = Point(
            x=sum(p.x for p in landmarks.right_eye) / len(landmarks.right_eye),
            y=sum(p.y for p in landmarks.right_eye) / len(landmarks.right_eye)
        )

        # M is the midpoint between the eyes
        key_points.M = Point(
            x=(left_eye_center.x + right_eye_center.x) / 2,
            y=(left_eye_center.y + right_eye_center.y) / 2
        )

    # N is the nose tip
    if landmarks.nose_tip and len(landmarks.nose_tip) > 0:
        # Use the lowest point of the nose tip as N
        nose_tip_points = sorted(landmarks.nose_tip, key=lambda p: p.y, reverse=True)
        key_points.N = nose_tip_points[0]

    return key_points

def process_mediapipe_landmarks(face_landmarks, img, image_id):
    """Process MediaPipe face landmarks and convert to our format"""
    height, width = img.shape[:2]

    # Extract face bounding box
    x_coordinates = [landmark.x * width for landmark in face_landmarks.landmark]
    y_coordinates = [landmark.y * height for landmark in face_landmarks.landmark]

    left = int(min(x_coordinates))
    right = int(max(x_coordinates))
    top = int(min(y_coordinates))
    bottom = int(max(y_coordinates))

    # MediaPipe face mesh indices for different facial features
    # These indices are based on the MediaPipe Face Mesh model
    chin_indices = [152, 377, 400, 378, 379, 365, 397, 288, 361, 323, 356, 389, 174, 175, 176, 177, 148]
    left_eyebrow_indices = [336, 296, 334, 293, 300, 276, 283, 282, 295, 285]
    right_eyebrow_indices = [107, 66, 105, 63, 70, 46, 53, 52, 65, 55]
    nose_bridge_indices = [168, 6, 197, 195, 5]
    nose_tip_indices = [1, 2, 3, 4, 5, 6, 19, 94, 19]
    left_eye_indices = [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398]
    right_eye_indices = [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246]
    top_lip_indices = [61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291, 375, 321, 405, 314, 17, 84, 181, 91, 146]
    bottom_lip_indices = [146, 91, 181, 84, 17, 314, 405, 321, 375, 291, 409, 270, 269, 267, 0, 37, 39, 40, 185, 61]

    # Convert landmarks to our format
    landmarks = FaceLandmarks(
        top=top,
        right=right,
        bottom=bottom,
        left=left,
        chin=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in chin_indices],
        left_eyebrow=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in left_eyebrow_indices],
        right_eyebrow=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in right_eyebrow_indices],
        nose_bridge=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in nose_bridge_indices],
        nose_tip=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in nose_tip_indices],
        left_eye=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in left_eye_indices],
        right_eye=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in right_eye_indices],
        top_lip=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in top_lip_indices],
        bottom_lip=[Point(x=face_landmarks.landmark[i].x * width, y=face_landmarks.landmark[i].y * height) for i in bottom_lip_indices]
    )

    # Estimate crown and chin points using a more accurate method
    crown_chin = estimate_crown_chin_points_mediapipe(face_landmarks, width, height)
    landmarks.crown_chin = crown_chin

    # Extract key facial points
    landmarks.key_points = extract_key_facial_points(landmarks)

    # Store the landmarks in the image store
    image_store[image_id]['landmarks'] = landmarks

    return landmarks

def process_face_recognition_landmarks(img, image_id):
    """Process face_recognition landmarks as a fallback"""
    # Convert BGR to RGB for face_recognition
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # Detect face locations
    face_locations = face_recognition.face_locations(img_rgb)

    if not face_locations:
        logger.warning(f"No faces detected in image {image_id}")
        return None

    # Use the first face found (assuming there's only one face in passport photos)
    top, right, bottom, left = face_locations[0]

    # Detect facial landmarks
    face_landmarks_list = face_recognition.face_landmarks(img_rgb, face_locations)

    if not face_landmarks_list:
        logger.warning(f"No landmarks detected in image {image_id}")
        return None

    face_landmarks_dict = face_landmarks_list[0]

    # Convert landmarks to Point objects
    landmarks = FaceLandmarks(
        top=top,
        right=right,
        bottom=bottom,
        left=left,
        chin=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['chin']],
        left_eyebrow=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['left_eyebrow']],
        right_eyebrow=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['right_eyebrow']],
        nose_bridge=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['nose_bridge']],
        nose_tip=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['nose_tip']],
        left_eye=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['left_eye']],
        right_eye=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['right_eye']],
        top_lip=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['top_lip']],
        bottom_lip=[Point(x=float(p[0]), y=float(p[1])) for p in face_landmarks_dict['bottom_lip']]
    )

    # Estimate crown and chin points
    crown_chin = estimate_crown_chin_points(landmarks)
    landmarks.crown_chin = crown_chin

    # Extract key facial points
    landmarks.key_points = extract_key_facial_points(landmarks)

    # Store the landmarks in the image store
    image_store[image_id]['landmarks'] = landmarks

    return landmarks

def process_opencv_fallback(img, image_id):
    """
    Process image using OpenCV's Haar cascades as a last resort fallback.
    This function works with the original image orientation without rotation.

    Args:
        img: Input image
        image_id: The ID of the image

    Returns:
        FaceLandmarks object or None if no face is detected
    """
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        height, width = img.shape[:2]

        # Detect face
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)

        if len(faces) == 0:
            logger.warning(f"No faces detected in image {image_id} using OpenCV")
            return None

        # Use the largest face
        face = max(faces, key=lambda rect: rect[2] * rect[3])
        x, y, w, h = face

        # Try to detect eyes
        eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        roi_gray = gray[y:y+h, x:x+w]
        eyes = eye_cascade.detectMultiScale(roi_gray)

        # Estimate landmarks based on face proportions
        landmarks_dict = {}

        # If we detect at least two eyes, use them
        if len(eyes) >= 2:
            # Sort eyes by x-coordinate
            eyes = sorted(eyes, key=lambda e: e[0])

            # Get the two leftmost and rightmost eyes
            left_eye = eyes[0]
            right_eye = eyes[-1]

            # Calculate eye centers
            left_eye_center = (x + left_eye[0] + left_eye[2]//2, y + left_eye[1] + left_eye[3]//2)
            right_eye_center = (x + right_eye[0] + right_eye[2]//2, y + right_eye[1] + right_eye[3]//2)

            landmarks_dict["left_eye"] = np.array(left_eye_center)
            landmarks_dict["right_eye"] = np.array(right_eye_center)

            # Calculate eye angle to determine if the face is rotated
            eye_angle_rad = math.atan2(
                right_eye_center[1] - left_eye_center[1],
                right_eye_center[0] - left_eye_center[0]
            )
            eye_angle_deg = math.degrees(eye_angle_rad)

            logger.debug(f"Eye angle for OpenCV fallback: {eye_angle_deg:.2f} degrees")

            # Estimate other landmarks based on eye positions
            eye_distance = right_eye_center[0] - left_eye_center[0]
            eye_y = (left_eye_center[1] + right_eye_center[1]) // 2

            landmarks_dict["left_mouth"] = np.array([left_eye_center[0], eye_y + eye_distance//2])
            landmarks_dict["right_mouth"] = np.array([right_eye_center[0], eye_y + eye_distance//2])

            # Calculate chin point
            chin_y = y + h
            chin_x = (left_eye_center[0] + right_eye_center[0]) // 2
            landmarks_dict["chin"] = np.array([chin_x, chin_y])

            # Calculate crown point considering the face angle
            # Always use the angled approach to better handle tilted faces
            # Calculate perpendicular direction to eye line for upward vector
            perp_angle_rad = eye_angle_rad - (math.pi / 2)  # 90 degrees counter-clockwise

            # Calculate upward vector in the face's coordinate system
            upward_vector = (
                math.cos(perp_angle_rad),
                math.sin(perp_angle_rad)
            )

            # Estimate crown position using the face height and upward vector
            crown_distance = h * 1.1  # Extend slightly beyond the face height
            eye_midpoint = ((left_eye_center[0] + right_eye_center[0]) // 2,
                           (left_eye_center[1] + right_eye_center[1]) // 2)

            crown_x = int(eye_midpoint[0] - upward_vector[0] * crown_distance)
            crown_y = int(eye_midpoint[1] - upward_vector[1] * crown_distance)
            landmarks_dict["crown"] = np.array([crown_x, crown_y])
        else:
            # Estimate eye positions based on face proportions
            landmarks_dict["left_eye"] = np.array([x + w//3, y + h//3])
            landmarks_dict["right_eye"] = np.array([x + 2*w//3, y + h//3])
            landmarks_dict["left_mouth"] = np.array([x + w//3, y + 2*h//3])
            landmarks_dict["right_mouth"] = np.array([x + 2*w//3, y + 2*h//3])
            landmarks_dict["chin"] = np.array([x + w//2, y + h])
            landmarks_dict["crown"] = np.array([x + w//2, y - int(h * 0.1)])

        # Create a simplified FaceLandmarks object
        # We need to create placeholder lists for the required fields
        placeholder_points = [Point(x=float(landmarks_dict["left_eye"][0]), y=float(landmarks_dict["left_eye"][1]))]

        landmarks = FaceLandmarks(
            top=y,
            right=x+w,
            bottom=y+h,
            left=x,
            chin=[Point(x=float(landmarks_dict["chin"][0]), y=float(landmarks_dict["chin"][1]))],
            left_eyebrow=placeholder_points,
            right_eyebrow=placeholder_points,
            nose_bridge=placeholder_points,
            nose_tip=placeholder_points,
            left_eye=[Point(x=float(landmarks_dict["left_eye"][0]), y=float(landmarks_dict["left_eye"][1]))],
            right_eye=[Point(x=float(landmarks_dict["right_eye"][0]), y=float(landmarks_dict["right_eye"][1]))],
            top_lip=[Point(x=float(landmarks_dict["left_mouth"][0]), y=float(landmarks_dict["left_mouth"][1]))],
            bottom_lip=[Point(x=float(landmarks_dict["right_mouth"][0]), y=float(landmarks_dict["right_mouth"][1]))]
        )

        # Create crown-chin points
        crown_chin = CrownChinPointPair(
            crownPoint=Point(x=float(landmarks_dict["crown"][0]), y=float(landmarks_dict["crown"][1])),
            chinPoint=Point(x=float(landmarks_dict["chin"][0]), y=float(landmarks_dict["chin"][1]))
        )
        landmarks.crown_chin = crown_chin

        # Create key points
        key_points = KeyFacialPoints()

        # Set crown and chin points
        key_points.P = landmarks.crown_chin.crownPoint
        key_points.Q = landmarks.crown_chin.chinPoint

        # Set eye pupil points if available
        if len(landmarks.left_eye) > 0:
            key_points.A = landmarks.left_eye[0]  # Use center point as pupil
        if len(landmarks.right_eye) > 0:
            key_points.B = landmarks.right_eye[0]  # Use center point as pupil

        # Set mouth points if available
        if len(landmarks.top_lip) > 0:
            key_points.C = landmarks.top_lip[0]
        if len(landmarks.bottom_lip) > 0:
            key_points.D = landmarks.bottom_lip[0]

        # Set nose tip if available
        if len(landmarks.nose_tip) > 0:
            key_points.N = landmarks.nose_tip[0]

        # Set midpoint between eyes
        if len(landmarks.left_eye) > 0 and len(landmarks.right_eye) > 0:
            key_points.M = Point(
                x=(landmarks.left_eye[0].x + landmarks.right_eye[0].x) / 2,
                y=(landmarks.left_eye[0].y + landmarks.right_eye[0].y) / 2
            )

        landmarks.key_points = key_points

        # Store the landmarks in the image store
        image_store[image_id]['landmarks'] = landmarks

        # Store the original image as the "aligned" image for consistency with frontend
        image_store[image_id]['aligned_image'] = img.copy()

        # Store the original landmarks
        image_store[image_id]['original_landmarks'] = landmarks

        return landmarks

    except Exception as e:
        logger.error(f"Error in OpenCV fallback: {str(e)}")
        return None

def estimate_crown_chin_points_mediapipe(face_landmarks, width, height) -> CrownChinPointPair:
    """
    Estimate the crown and chin points based on MediaPipe facial landmarks.
    This uses a more accurate approach based on the face geometry and handles
    image rotation properly.

    Args:
        face_landmarks: MediaPipe face landmarks
        width: Image width
        height: Image height

    Returns:
        CrownChinPointPair object
    """
    # Chin point - use the bottom-most point of the chin (index 152)
    chin_point = Point(
        x=face_landmarks.landmark[152].x * width,
        y=face_landmarks.landmark[152].y * height
    )

    # For crown point, we need to estimate where the top of the head would be
    # First, get the midpoint between the eyes
    left_eye_center = Point(
        x=face_landmarks.landmark[468].x * width,  # Left eye center
        y=face_landmarks.landmark[468].y * height
    )
    right_eye_center = Point(
        x=face_landmarks.landmark[473].x * width,  # Right eye center
        y=face_landmarks.landmark[473].y * height
    )

    # Midpoint between eyes
    eye_midpoint = Point(
        x=(left_eye_center.x + right_eye_center.x) / 2,
        y=(left_eye_center.y + right_eye_center.y) / 2
    )

    # Get the top of the forehead (highest visible point)
    forehead_top = Point(
        x=face_landmarks.landmark[10].x * width,  # Top of forehead
        y=face_landmarks.landmark[10].y * height
    )

    # Calculate the eye angle to determine if the face is rotated
    eye_angle_rad = math.atan2(
        right_eye_center.y - left_eye_center.y,
        right_eye_center.x - left_eye_center.x
    )
    eye_angle_deg = math.degrees(eye_angle_rad)

    logger.debug(f"Eye angle for crown-chin estimation: {eye_angle_deg:.2f} degrees")

    # Calculate the distance from eye midpoint to forehead top
    eye_to_forehead_distance = abs(eye_midpoint.y - forehead_top.y)

    # Calculate the normalized distance (sum of eye distance and eye-to-chin distance)
    eye_distance = math.sqrt((right_eye_center.x - left_eye_center.x)**2 +
                            (right_eye_center.y - left_eye_center.y)**2)

    eye_to_chin_distance = math.sqrt((eye_midpoint.x - chin_point.x)**2 +
                                    (eye_midpoint.y - chin_point.y)**2)

    normalized_distance = eye_distance + eye_to_chin_distance

    # Use coefficients similar to those in CrownChinEstimator.cpp
    # These coefficients are based on facial proportions
    chin_crown_coeff = 1.24481  # From libppp/share/config.json

    # Calculate the distance from chin to crown
    chin_to_crown_distance = chin_crown_coeff * normalized_distance

    # Calculate the vector from chin to eye midpoint
    chin_to_eye_vector = (
        eye_midpoint.x - chin_point.x,
        eye_midpoint.y - chin_point.y
    )

    # Normalize the vector
    vector_length = math.sqrt(chin_to_eye_vector[0]**2 + chin_to_eye_vector[1]**2)
    normalized_vector = (
        chin_to_eye_vector[0] / vector_length,
        chin_to_eye_vector[1] / vector_length
    )

    # Calculate the crown point by extending from chin along the normalized vector
    crown_point = Point(
        x=chin_point.x + normalized_vector[0] * chin_to_crown_distance,
        y=chin_point.y + normalized_vector[1] * chin_to_crown_distance
    )

    # As a fallback, if the calculated crown point seems unreasonable,
    # use the simpler method based on forehead extension
    if crown_point.y > forehead_top.y:  # If crown is below forehead (which is wrong)
        # If the face is significantly rotated, adjust the crown point calculation
        if abs(eye_angle_deg) > 5:
            # Calculate perpendicular direction to eye line for upward vector
            perp_angle_rad = eye_angle_rad - (math.pi / 2)  # 90 degrees counter-clockwise

            # Calculate upward vector in the rotated face's coordinate system
            upward_vector = (
                math.cos(perp_angle_rad),
                math.sin(perp_angle_rad)
            )

            # Extend from forehead top along the upward vector
            extension_distance = eye_to_forehead_distance * 1.5
            crown_point = Point(
                x=forehead_top.x + upward_vector[0] * extension_distance,
                y=forehead_top.y + upward_vector[1] * extension_distance
            )
        else:
            # For minimal rotation, use the simpler vertical extension
            crown_point = Point(
                x=eye_midpoint.x,
                y=forehead_top.y - eye_to_forehead_distance * 1.5  # Extend upward by 1.5x the eye-to-forehead distance
            )

    # Ensure the crown point is above the chin point (in image coordinates, y decreases as you go up)
    if crown_point.y >= chin_point.y:
        # If crown is not above chin, force it to be directly above
        crown_point = Point(
            x=chin_point.x,
            y=chin_point.y - chin_to_crown_distance
        )

    return CrownChinPointPair(crownPoint=crown_point, chinPoint=chin_point)

def estimate_crown_chin_points(landmarks: FaceLandmarks) -> CrownChinPointPair:
    """
    Estimate the crown and chin points based on facial landmarks from face_recognition.
    Uses a similar approach to the MediaPipe version, with coefficients based on facial proportions.
    This function handles image rotation properly.

    Args:
        landmarks: The facial landmarks

    Returns:
        CrownChinPointPair object
    """
    # Get the chin point (lowest point of the chin)
    chin_points = landmarks.chin
    chin_point = max(chin_points, key=lambda p: p.y)

    # Get the midpoint between the eyes
    left_eye_points = landmarks.left_eye
    right_eye_points = landmarks.right_eye

    left_eye_center = Point(
        x=sum(p.x for p in left_eye_points) / len(left_eye_points),
        y=sum(p.y for p in left_eye_points) / len(left_eye_points)
    )

    right_eye_center = Point(
        x=sum(p.x for p in right_eye_points) / len(right_eye_points),
        y=sum(p.y for p in right_eye_points) / len(right_eye_points)
    )

    # Calculate the eye angle to determine if the face is rotated
    eye_angle_rad = math.atan2(
        right_eye_center.y - left_eye_center.y,
        right_eye_center.x - left_eye_center.x
    )
    eye_angle_deg = math.degrees(eye_angle_rad)

    logger.debug(f"Eye angle for crown-chin estimation (face_recognition): {eye_angle_deg:.2f} degrees")

    eye_midpoint = Point(
        x=(left_eye_center.x + right_eye_center.x) / 2,
        y=(left_eye_center.y + right_eye_center.y) / 2
    )

    # Get the top of the eyebrows
    left_eyebrow_top = min(landmarks.left_eyebrow, key=lambda p: p.y)
    right_eyebrow_top = min(landmarks.right_eyebrow, key=lambda p: p.y)
    eyebrow_top = Point(
        x=(left_eyebrow_top.x + right_eyebrow_top.x) / 2,
        y=min(left_eyebrow_top.y, right_eyebrow_top.y)
    )

    # Calculate the distance from eye midpoint to eyebrow top
    eye_to_eyebrow_distance = abs(eye_midpoint.y - eyebrow_top.y)

    # Calculate the normalized distance (sum of eye distance and eye-to-chin distance)
    eye_distance = math.sqrt((right_eye_center.x - left_eye_center.x)**2 +
                            (right_eye_center.y - left_eye_center.y)**2)

    eye_to_chin_distance = math.sqrt((eye_midpoint.x - chin_point.x)**2 +
                                    (eye_midpoint.y - chin_point.y)**2)

    normalized_distance = eye_distance + eye_to_chin_distance

    # Use coefficients similar to those in CrownChinEstimator.cpp
    chin_crown_coeff = 1.24481  # From libppp/share/config.json

    # Calculate the distance from chin to crown
    chin_to_crown_distance = chin_crown_coeff * normalized_distance

    # Calculate the vector from chin to eye midpoint
    chin_to_eye_vector = (
        eye_midpoint.x - chin_point.x,
        eye_midpoint.y - chin_point.y
    )

    # Normalize the vector
    vector_length = math.sqrt(chin_to_eye_vector[0]**2 + chin_to_eye_vector[1]**2)
    normalized_vector = (
        chin_to_eye_vector[0] / vector_length,
        chin_to_eye_vector[1] / vector_length
    )

    # Calculate the crown point by extending from chin along the normalized vector
    crown_point = Point(
        x=chin_point.x + normalized_vector[0] * chin_to_crown_distance,
        y=chin_point.y + normalized_vector[1] * chin_to_crown_distance
    )

    # As a fallback, if the calculated crown point seems unreasonable,
    # use a method that accounts for face rotation
    if crown_point.y > eyebrow_top.y:  # If crown is below eyebrow (which is wrong)
        # If the face is significantly rotated, adjust the crown point calculation
        if abs(eye_angle_deg) > 5:
            # Calculate perpendicular direction to eye line for upward vector
            perp_angle_rad = eye_angle_rad - (math.pi / 2)  # 90 degrees counter-clockwise

            # Calculate upward vector in the rotated face's coordinate system
            upward_vector = (
                math.cos(perp_angle_rad),
                math.sin(perp_angle_rad)
            )

            # Estimate the distance from eyebrow to crown (top of head)
            eyebrow_to_crown_distance = eye_to_eyebrow_distance * 3.0

            # Extend from eyebrow top along the upward vector
            crown_point = Point(
                x=eyebrow_top.x + upward_vector[0] * eyebrow_to_crown_distance,
                y=eyebrow_top.y + upward_vector[1] * eyebrow_to_crown_distance
            )
        else:
            # For minimal rotation, use the simpler vertical extension
            eyebrow_to_crown_distance = eye_to_eyebrow_distance * 3.0
            crown_point = Point(
                x=eye_midpoint.x,
                y=eyebrow_top.y - eyebrow_to_crown_distance
            )

    # Ensure the crown point is above the chin point (in image coordinates, y decreases as you go up)
    if crown_point.y >= chin_point.y:
        # If crown is not above chin, force it to be directly above
        crown_point = Point(
            x=chin_point.x,
            y=chin_point.y - chin_to_crown_distance
        )

    return CrownChinPointPair(crownPoint=crown_point, chinPoint=chin_point)

def get_aligned_image(image_id: str) -> Optional[np.ndarray]:
    """
    Get the aligned image for an image ID.

    Args:
        image_id: The ID of the image

    Returns:
        Aligned image as numpy array or None if not available
    """
    if image_id not in image_store or 'aligned_image' not in image_store[image_id]:
        logger.error(f"Aligned image with ID {image_id} not found")
        return None

    return image_store[image_id]['aligned_image']

def draw_landmarks_on_image(image: np.ndarray, landmarks: FaceLandmarks,
                           color: Tuple[int, int, int] = (0, 255, 0),
                           radius: int = 3,
                           thickness: int = 2,
                           show_simplified: bool = True) -> np.ndarray:
    """
    Draw landmarks on an image for visualization.
    This function highlights the crown-chin points and shows the face orientation.

    When show_simplified is True, only the key points (A-Q) are shown.

    Args:
        image: Input image
        landmarks: Facial landmarks
        color: Color of the landmarks (BGR)
        radius: Radius of the landmark circles
        thickness: Thickness of the landmark circles
        show_simplified: Whether to show only the key points (A-Q)

    Returns:
        Image with landmarks drawn on it
    """
    # Make a copy of the image to avoid modifying the original
    image_with_landmarks = image.copy()

    # Don't draw face rectangle
    # cv2.rectangle(image_with_landmarks,
    #              (landmarks.left, landmarks.top),
    #              (landmarks.right, landmarks.bottom),
    #              color, 2)

    if show_simplified and landmarks.key_points:
        # Draw only the key points (A-Q)
        key_points = landmarks.key_points

        # Define colors for different point types
        eye_color = (255, 165, 0)  # Orange
        mouth_color = (255, 0, 255)  # Magenta
        center_color = (0, 255, 255)  # Yellow
        crown_color = (0, 215, 255)  # Gold (BGR format)
        chin_color = (0, 215, 255)  # Gold (BGR format)

        # Draw eye points (A, B)
        if key_points.A:
            pt_A = (int(key_points.A.x), int(key_points.A.y))
            cv2.circle(image_with_landmarks, pt_A, radius+2, eye_color, thickness)
            cv2.putText(image_with_landmarks, "A", (pt_A[0] - 15, pt_A[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, eye_color, 2, cv2.LINE_AA)

        if key_points.B:
            pt_B = (int(key_points.B.x), int(key_points.B.y))
            cv2.circle(image_with_landmarks, pt_B, radius+2, eye_color, thickness)
            cv2.putText(image_with_landmarks, "B", (pt_B[0] + 10, pt_B[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, eye_color, 2, cv2.LINE_AA)

        # Draw mouth points (C, D)
        if key_points.C:
            pt_C = (int(key_points.C.x), int(key_points.C.y))
            cv2.circle(image_with_landmarks, pt_C, radius+2, mouth_color, thickness)
            cv2.putText(image_with_landmarks, "C", (pt_C[0] - 15, pt_C[1] + 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, mouth_color, 2, cv2.LINE_AA)

        if key_points.D:
            pt_D = (int(key_points.D.x), int(key_points.D.y))
            cv2.circle(image_with_landmarks, pt_D, radius+2, mouth_color, thickness)
            cv2.putText(image_with_landmarks, "D", (pt_D[0] + 10, pt_D[1] + 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, mouth_color, 2, cv2.LINE_AA)

        # Draw center line points (P, M, N, Q)
        if key_points.P:
            pt_P = (int(key_points.P.x), int(key_points.P.y))
            cv2.circle(image_with_landmarks, pt_P, radius+3, crown_color, thickness+1)
            cv2.putText(image_with_landmarks, "P", (pt_P[0] + 10, pt_P[1]),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, crown_color, 2, cv2.LINE_AA)

        if key_points.M:
            pt_M = (int(key_points.M.x), int(key_points.M.y))
            cv2.circle(image_with_landmarks, pt_M, radius+2, center_color, thickness)
            cv2.putText(image_with_landmarks, "M", (pt_M[0] + 10, pt_M[1]),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, center_color, 2, cv2.LINE_AA)

        if key_points.N:
            pt_N = (int(key_points.N.x), int(key_points.N.y))
            cv2.circle(image_with_landmarks, pt_N, radius+2, center_color, thickness)
            cv2.putText(image_with_landmarks, "N", (pt_N[0] + 10, pt_N[1]),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, center_color, 2, cv2.LINE_AA)

        if key_points.Q:
            pt_Q = (int(key_points.Q.x), int(key_points.Q.y))
            cv2.circle(image_with_landmarks, pt_Q, radius+3, chin_color, thickness+1)
            cv2.putText(image_with_landmarks, "Q", (pt_Q[0] + 10, pt_Q[1]),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, chin_color, 2, cv2.LINE_AA)

        # Draw vertical line connecting P and Q (crown and chin)
        if key_points.P and key_points.Q:
            pt_P = (int(key_points.P.x), int(key_points.P.y))
            pt_Q = (int(key_points.Q.x), int(key_points.Q.y))
            cv2.line(image_with_landmarks, pt_P, pt_Q, (255, 255, 0), thickness)

            # Calculate and display the crown-chin distance
            distance = math.sqrt((pt_Q[0] - pt_P[0])**2 + (pt_Q[1] - pt_P[1])**2)
            midpoint = (
                (pt_P[0] + pt_Q[0]) // 2 + 15,
                (pt_P[1] + pt_Q[1]) // 2
            )
            cv2.putText(image_with_landmarks, f"Distance: {distance:.1f}px",
                       midpoint, cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2, cv2.LINE_AA)

        # Draw horizontal line connecting A and B (eye corners)
        if key_points.A and key_points.B:
            pt_A = (int(key_points.A.x), int(key_points.A.y))
            pt_B = (int(key_points.B.x), int(key_points.B.y))
            cv2.line(image_with_landmarks, pt_A, pt_B, eye_color, thickness)

        # Draw horizontal line connecting C and D (mouth corners)
        if key_points.C and key_points.D:
            pt_C = (int(key_points.C.x), int(key_points.C.y))
            pt_D = (int(key_points.D.x), int(key_points.D.y))
            cv2.line(image_with_landmarks, pt_C, pt_D, mouth_color, thickness)

    else:
        # Function to draw points
        def draw_points(points, label_prefix=""):
            for i, point in enumerate(points):
                pt = (int(point.x), int(point.y))
                cv2.circle(image_with_landmarks, pt, radius, color, thickness)
                if label_prefix:
                    cv2.putText(image_with_landmarks, f"{label_prefix}{i}",
                               (pt[0] + 5, pt[1] - 5),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1, cv2.LINE_AA)

        # Draw all landmark points
        draw_points(landmarks.chin, "chin")
        draw_points(landmarks.left_eyebrow, "l_brow")
        draw_points(landmarks.right_eyebrow, "r_brow")
        draw_points(landmarks.nose_bridge, "nose_b")
        draw_points(landmarks.nose_tip, "nose_t")
        draw_points(landmarks.left_eye, "l_eye")
        draw_points(landmarks.right_eye, "r_eye")
        draw_points(landmarks.top_lip, "t_lip")
        draw_points(landmarks.bottom_lip, "b_lip")

        # Draw lines connecting eyes and show eye angle
        if landmarks.left_eye and landmarks.right_eye:
            left_eye_center = (
                int(sum(p.x for p in landmarks.left_eye) / len(landmarks.left_eye)),
                int(sum(p.y for p in landmarks.left_eye) / len(landmarks.left_eye))
            )
            right_eye_center = (
                int(sum(p.x for p in landmarks.right_eye) / len(landmarks.right_eye)),
                int(sum(p.y for p in landmarks.right_eye) / len(landmarks.right_eye))
            )

            # Draw eye centers with a different color
            eye_color = (255, 165, 0)  # Orange
            cv2.circle(image_with_landmarks, left_eye_center, radius+1, eye_color, thickness)
            cv2.circle(image_with_landmarks, right_eye_center, radius+1, eye_color, thickness)

            # Draw line connecting eyes
            cv2.line(image_with_landmarks, left_eye_center, right_eye_center, eye_color, thickness)

            # Calculate eye angle
            dy = right_eye_center[1] - left_eye_center[1]
            dx = right_eye_center[0] - left_eye_center[0]
            eye_angle = np.degrees(np.arctan2(dy, dx))

            # Add eye angle text
            midpoint = (
                (left_eye_center[0] + right_eye_center[0]) // 2,
                (left_eye_center[1] + right_eye_center[1]) // 2 - 20
            )
            cv2.putText(image_with_landmarks, f"Eye angle: {eye_angle:.1f}°",
                       midpoint, cv2.FONT_HERSHEY_SIMPLEX, 0.5, eye_color, 1, cv2.LINE_AA)

        # Draw crown-chin points if available
        if landmarks.crown_chin:
            crown_pt = (int(landmarks.crown_chin.crownPoint.x), int(landmarks.crown_chin.crownPoint.y))
            chin_pt = (int(landmarks.crown_chin.chinPoint.x), int(landmarks.crown_chin.chinPoint.y))

            # Draw crown and chin points with gold color
            crown_color = (0, 215, 255)  # Gold for crown (BGR format)
            chin_color = (0, 215, 255)   # Gold for chin (BGR format)

            # Draw larger circles for crown and chin points to make them stand out
            cv2.circle(image_with_landmarks, crown_pt, radius+3, crown_color, thickness+1)
            cv2.circle(image_with_landmarks, chin_pt, radius+3, chin_color, thickness+1)

            # Draw line connecting crown and chin
            cv2.line(image_with_landmarks, crown_pt, chin_pt, (0, 215, 255), thickness)

            # Add labels
            cv2.putText(image_with_landmarks, "Crown", (crown_pt[0] + 10, crown_pt[1]),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, crown_color, 2, cv2.LINE_AA)
            cv2.putText(image_with_landmarks, "Chin", (chin_pt[0] + 10, chin_pt[1]),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, chin_color, 2, cv2.LINE_AA)

            # Calculate and display the crown-chin distance
            distance = math.sqrt((chin_pt[0] - crown_pt[0])**2 + (chin_pt[1] - crown_pt[1])**2)
            midpoint = (
                (crown_pt[0] + chin_pt[0]) // 2 + 15,
                (crown_pt[1] + chin_pt[1]) // 2
            )
            cv2.putText(image_with_landmarks, f"Distance: {distance:.1f}px",
                       midpoint, cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 215, 255), 2, cv2.LINE_AA)

            # Calculate and display the crown-chin angle
            dy = chin_pt[1] - crown_pt[1]
            dx = chin_pt[0] - crown_pt[0]
            cc_angle = np.degrees(np.arctan2(dy, dx))

            # Add angle text below the distance
            angle_pos = (midpoint[0], midpoint[1] + 25)
            cv2.putText(image_with_landmarks, f"C-C Angle: {cc_angle:.1f}°",
                       angle_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2, cv2.LINE_AA)

            # If we have eye angle, calculate and show the difference between eye angle and crown-chin angle
            if landmarks.left_eye and landmarks.right_eye:
                # The ideal crown-chin angle should be perpendicular to the eye angle
                ideal_cc_angle = eye_angle - 90
                if ideal_cc_angle < -180:
                    ideal_cc_angle += 360

                # Calculate the difference between actual and ideal crown-chin angle
                angle_diff = cc_angle - ideal_cc_angle
                if angle_diff > 180:
                    angle_diff -= 360
                elif angle_diff < -180:
                    angle_diff += 360

                # Display the angle difference
                diff_pos = (midpoint[0], midpoint[1] + 50)
                diff_color = (0, 255, 255)  # Yellow
                if abs(angle_diff) < 5:
                    diff_color = (0, 255, 0)  # Green if close to ideal
                elif abs(angle_diff) > 15:
                    diff_color = (0, 0, 255)  # Red if far from ideal

                cv2.putText(image_with_landmarks, f"Angle diff: {angle_diff:.1f}°",
                           diff_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.6, diff_color, 2, cv2.LINE_AA)

    return image_with_landmarks

def get_landmarks_visualization(image_id: str, use_aligned: bool = True) -> Optional[np.ndarray]:
    """
    Get an image with landmarks visualized for debugging and verification.
    This function provides detailed visualization of the landmarks on the original image.

    Args:
        image_id: The ID of the image
        use_aligned: Parameter kept for API compatibility but not used since we're
                    not rotating the image anymore.

    Returns:
        Image with landmarks drawn on it or None if not available
    """
    if image_id not in image_store:
        logger.error(f"Image with ID {image_id} not found")
        return None

    # Get the original image and landmarks
    img = image_store[image_id]['image'].copy()  # Make a copy to avoid modifying the original
    landmarks = image_store[image_id].get('landmarks')

    if landmarks is None:
        logger.error(f"No landmarks found for image {image_id}")
        return None

    # Draw landmarks on the image
    vis_img = draw_landmarks_on_image(img, landmarks)

    # Add a title for the image
    title = "ORIGINAL IMAGE WITH LANDMARKS"
    cv2.putText(vis_img, title,
               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 128, 255), 2, cv2.LINE_AA)

    # Add instructions for the user
    instructions = "Crown and chin points follow the face orientation"
    cv2.putText(vis_img, instructions,
               (10, vis_img.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 128, 255), 1, cv2.LINE_AA)

    return vis_img

def update_crown_chin_points(image_id: str, crown_point: Point, chin_point: Point) -> Optional[CrownChinPointPair]:
    """
    Update the crown and chin points for an image.
    This function works with the original image orientation without rotation.

    Args:
        image_id: The ID of the image
        crown_point: The new crown point
        chin_point: The new chin point

    Returns:
        Updated CrownChinPointPair or None if the image is not found
    """
    if image_id not in image_store or image_store[image_id]['landmarks'] is None:
        logger.error(f"Image with ID {image_id} not found or landmarks not detected")
        return None

    try:
        # Get the landmarks from the store
        landmarks = image_store[image_id]['landmarks']

        # Update the crown and chin points
        crown_chin = CrownChinPointPair(crownPoint=crown_point, chinPoint=chin_point)
        landmarks.crown_chin = crown_chin

        # Store the updated landmarks
        image_store[image_id]['landmarks'] = landmarks

        # Also update the original landmarks if they exist
        if 'original_landmarks' in image_store[image_id]:
            original_landmarks = image_store[image_id]['original_landmarks']
            original_landmarks.crown_chin = crown_chin
            image_store[image_id]['original_landmarks'] = original_landmarks

        return crown_chin

    except Exception as e:
        logger.error(f"Error updating crown-chin points: {str(e)}")
        return None
