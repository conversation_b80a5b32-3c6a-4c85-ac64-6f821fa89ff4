import uuid
import logging
import cv2
import numpy as np
import base64
import io
from typing import Tuple, Optional, Any
import math

# Import CairoSVG for SVG to JPG conversion
try:
    import cairosvg
    from PIL import Image
    CAIROSVG_AVAILABLE = True
except ImportError:
    CAIROSVG_AVAILABLE = False
    logging.warning("CairoSVG or Pillow not available. SVG to JPG conversion will use fallback method.")

from app.models.landmarks import Point
from app.models.photostandard import PhotoStandard
from app.services.landmark_detection import image_store
from app.services.photostandard_service import get_photo_standard, convert_units
from app.services.dpi_service import DpiService

logger = logging.getLogger(__name__)


def crop_photo_to_standard(
    image_id: str,
    standard_id: str,
    crown_point: Optional[Point] = None,
    chin_point: Optional[Point] = None
) -> Tuple[Optional[str], Optional[str]]:
    """
    Crop a photo according to a specific standard.

    Args:
        image_id: ID of the image to process
        standard_id: ID of the photo standard to use
        crown_point: Optional crown point (if not provided, will use detected landmarks)
        chin_point: Optional chin point (if not provided, will use detected landmarks)

    Returns:
        Tuple of (processed image ID or None, error message or None)
    """
    # Check if image exists
    if image_id not in image_store:
        return None, f"Image with ID {image_id} not found"

    # Get the image and landmarks
    img = image_store[image_id]['image']
    landmarks = image_store[image_id].get('landmarks')

    if landmarks is None:
        return None, "No landmarks detected for this image"

    # Get the photo standard
    standard = get_photo_standard(standard_id)
    if standard is None:
        return None, f"Photo standard with ID {standard_id} not found"

    # Use provided crown and chin points or get them from landmarks
    if crown_point is None or chin_point is None:
        if landmarks.crown_chin is None:
            return None, "No crown-chin points detected and none provided"
        crown_pt = landmarks.crown_chin.crownPoint
        chin_pt = landmarks.crown_chin.chinPoint
    else:
        crown_pt = crown_point
        chin_pt = chin_point

    # Process the photo
    try:
        processed_img = crop_and_resize_photo(img, crown_pt, chin_pt, standard, landmarks)

        # Generate a new image ID for the processed image
        processed_image_id = str(uuid.uuid4())

        # Store the processed image
        height, width = processed_img.shape[:2]
        image_store[processed_image_id] = {
            "image": processed_img,
            "width": width,
            "height": height,
            "format": "JPEG",
            "landmarks": None,
            "original_image_id": image_id,
            "standard_id": standard_id
        }

        return processed_image_id, None
    except Exception as e:
        logger.error(f"Error processing photo: {str(e)}")
        return None, f"Error processing photo: {str(e)}"


def point_in_line_at_distance(p0, p1, dist):
    """
    Calculate a point in the line defined by two points at a specified distance from the first point.

    Args:
        p0: First point (starting point)
        p1: Second point (direction point)
        dist: Distance from p0 towards p1

    Returns:
        Point at the specified distance
    """
    # Calculate vector from p0 to p1
    dx = p1.x - p0.x
    dy = p1.y - p0.y

    # Calculate distance between p0 and p1
    length = math.sqrt(dx*dx + dy*dy)

    if length == 0:
        logger.warning("Points are identical, returning p0")
        return p0

    # Calculate ratio
    ratio = dist / length

    # Calculate new point
    new_point = Point(
        x=p0.x + dx * ratio,
        y=p0.y + dy * ratio
    )

    # Log the calculation for debugging
    logger.debug(f"Point at distance calculation: p0=({p0.x:.2f}, {p0.y:.2f}), p1=({p1.x:.2f}, {p1.y:.2f}), "
                f"dist={dist:.2f}, length={length:.2f}, ratio={ratio:.2f}, "
                f"result=({new_point.x:.2f}, {new_point.y:.2f})")

    return new_point


def crop_and_resize_photo(
    img: np.ndarray,
    crown_point: Point,
    chin_point: Point,
    standard: PhotoStandard,
    landmarks: Any = None  # noqa: Kept for future use with eye line positioning
) -> np.ndarray:
    """
    Crop and resize a photo according to a specific standard.

    Note: The landmarks parameter is currently not used but kept for future implementation
    of eye line positioning and other advanced features.

    Args:
        img: Input image as numpy array
        crown_point: Crown point
        chin_point: Chin point
        standard: Photo standard to use
        landmarks: Optional landmarks for eye line positioning (reserved for future use)

    Returns:
        Processed image as numpy array
    """
    # Calculate face height in pixels
    face_height_px = math.sqrt((crown_point.x - chin_point.x)**2 +
                              (crown_point.y - chin_point.y)**2)

    # Calculate required dimensions in pixels
    dimensions = standard.dimensions
    # Use DpiService for unit conversion
    std_width_px = DpiService.convert_to_pixels(dimensions.pictureWidth, dimensions.units, dimensions.dpi)
    std_height_px = DpiService.convert_to_pixels(dimensions.pictureHeight, dimensions.units, dimensions.dpi)
    # We don't need face height in pixels anymore since we're not using circular masks

    # Calculate the center of the crop based on the photo standard
    # This is the key part that determines the correct positioning
    center_crop = None

    # Determine the crown top value to use
    effective_crown_top = None

    if dimensions.bottomEyeLine is not None and dimensions.bottomEyeLine > 0:
        # Convert eye line to bottom distance into crown top distance
        # The coefficient represents the ratio of chin-to-eye distance relative to total face height
        chin_frown_coefficient = 0.8945 / 1.7699
        effective_crown_top = (dimensions.pictureHeight - dimensions.bottomEyeLine -
                              dimensions.faceHeight * (1 - chin_frown_coefficient))
        logger.info(
            f"Using bottomEyeLine: {dimensions.bottomEyeLine} {dimensions.units}, calculated crownTop: {effective_crown_top:.2f} {dimensions.units}"
        )
    elif dimensions.crownTop is not None and dimensions.crownTop > 0:
        effective_crown_top = dimensions.crownTop
        logger.info(
            f"Using crownTop: {effective_crown_top} {dimensions.units}"
        )

    if effective_crown_top is None or effective_crown_top <= 0:
        # If no crown top is specified, use the middle point between crown and chin
        center_crop = Point(
            x=(crown_point.x + chin_point.x) / 2,
            y=(crown_point.y + chin_point.y) / 2
        )
        logger.info(f"Using middle point as center: ({center_crop.x:.2f}, {center_crop.y:.2f})")
    else:
        # Calculate the center based on the crown top specification
        pixels_per_unit = face_height_px / dimensions.faceHeight
        crown_top_px = effective_crown_top * pixels_per_unit
        pic_height_px = dimensions.pictureHeight * pixels_per_unit
        crown_to_center_px = pic_height_px / 2 - crown_top_px

        # Log the calculation details
        logger.info(f"Center calculation: face_height_px={face_height_px:.2f}, pixels_per_unit={pixels_per_unit:.2f}, "
                   f"crown_top_px={crown_top_px:.2f}, pic_height_px={pic_height_px:.2f}, "
                   f"crown_to_center_px={crown_to_center_px:.2f}")

        # Calculate the point at this distance from crown towards chin
        center_crop = point_in_line_at_distance(crown_point, chin_point, crown_to_center_px)
        logger.info(f"Calculated center: ({center_crop.x:.2f}, {center_crop.y:.2f})")

    # Calculate the crop dimensions based on the face height
    crop_height_px = dimensions.pictureHeight * face_height_px / dimensions.faceHeight
    crop_width_px = dimensions.pictureWidth * face_height_px / dimensions.faceHeight

    # Log the crop dimensions
    logger.info(f"Crop dimensions: width={crop_width_px:.2f}px, height={crop_height_px:.2f}px")

    # Calculate the vector from chin to crown
    chin_crown_vec = Point(
        x=crown_point.x - chin_point.x,
        y=crown_point.y - chin_point.y
    )

    # Calculate the center top point (in the direction of the face orientation)
    center_top = Point(
        x=center_crop.x + chin_crown_vec.x * (crop_height_px / face_height_px / 2.0),
        y=center_crop.y + chin_crown_vec.y * (crop_height_px / face_height_px / 2.0)
    )

    # Calculate the 90-degree rotated vector (perpendicular to face orientation)
    chin_crown_90deg_rotated = Point(
        x=chin_crown_vec.y,
        y=-chin_crown_vec.x
    )

    # Calculate the center left point (perpendicular to face orientation)
    center_left = Point(
        x=center_crop.x + chin_crown_90deg_rotated.x * (crop_width_px / face_height_px / 2.0),
        y=center_crop.y + chin_crown_90deg_rotated.y * (crop_width_px / face_height_px / 2.0)
    )

    # Define the source points for the affine transformation
    # These three points define the orientation and position of the crop
    src_points = np.array([
        [center_crop.x, center_crop.y],  # Center point
        [center_left.x, center_left.y],   # Left point (perpendicular to face orientation)
        [center_top.x, center_top.y]      # Top point (in the direction of face orientation)
    ], dtype=np.float32)

    # Define the destination points in the output image
    dst_points = np.array([
        [crop_width_px / 2, crop_height_px / 2],  # Center of the output image
        [0, crop_height_px / 2],                  # Middle of the left edge
        [crop_width_px / 2, 0]                    # Middle of the top edge
    ], dtype=np.float32)

    # Get the affine transformation matrix
    transform_matrix = cv2.getAffineTransform(src_points, dst_points)

    # Log the transformation matrix for debugging
    logger.info(f"Affine transformation matrix: {transform_matrix}")

    # Apply the affine transformation to get the properly aligned crop
    # Use a white background (255) for the transformation
    crop_image = cv2.warpAffine(
        img,
        transform_matrix,
        (int(crop_width_px), int(crop_height_px)),
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(255, 255, 255)  # White background in BGR format
    )

    # Resize to the standard dimensions
    resized = cv2.resize(crop_image, (int(std_width_px), int(std_height_px)))

    # Set the background color based on the standard
    bg_color = (255, 255, 255)  # Default white background in BGR format

    # If the standard specifies a non-white background
    if standard.backgroundColor != "#ffffff":
        # Convert hex color to BGR for the background
        bg_color_face = standard.backgroundColor.lstrip('#')
        bg_color_face = tuple(int(bg_color_face[i:i+2], 16) for i in (4, 2, 0))  # BGR format
        bg_color = bg_color_face

    # Create a new image with the specified background color
    final_img = np.ones_like(resized) * np.array(bg_color, dtype=np.uint8)

    # Copy the resized image onto the background
    # This preserves the original image content while ensuring the background has the right color
    final_img = resized.copy()

    # Create a pure white border around the image to ensure the entire image has a white background
    # This adds an extra layer of certainty that the background is white
    border_size = 2  # 2 pixel border
    final_img_with_border = cv2.copyMakeBorder(
        final_img,
        border_size, border_size, border_size, border_size,
        cv2.BORDER_CONSTANT,
        value=bg_color
    )

    # Resize back to the standard dimensions to remove the border from the final output
    final_img = cv2.resize(final_img_with_border, (int(std_width_px), int(std_height_px)))

    return final_img


def create_tiled_print(
    image_id: str,
    num_rows: int = 2,
    num_cols: int = 2,
    spacing_mm: float = 2.0,
    print_definition_id: Optional[str] = None
) -> Tuple[Optional[str], Optional[str]]:
    """
    Create a tiled print with multiple copies of the processed photo.

    Args:
        image_id: ID of the processed image
        num_rows: Number of rows in the tiled print (used if print_definition_id is None)
        num_cols: Number of columns in the tiled print (used if print_definition_id is None)
        spacing_mm: Spacing between photos in mm (used if print_definition_id is None)
        print_definition_id: Optional ID of a print definition to use

    Returns:
        Tuple of (tiled image ID or None, error message or None)
    """
    # Check if image exists
    if image_id not in image_store:
        return None, f"Image with ID {image_id} not found"

    # Get the image
    img = image_store[image_id]['image']

    # Get the standard ID if available
    standard_id = image_store[image_id].get('standard_id')

    # If using a print definition
    if print_definition_id:
        try:
            from app.services.print_definition_service import get_print_definition

            # Get the print definition
            print_def = get_print_definition(print_definition_id)
            if not print_def:
                return None, f"Print definition with ID {print_definition_id} not found"

            # If we have a standard ID, get the standard
            if standard_id:
                standard = get_photo_standard(standard_id)
                if not standard:
                    return None, f"Photo standard with ID {standard_id} not found"

                # Use the print definition to create the tiled print
                return create_tiled_print_from_definition(img, print_def, standard)
            else:
                # Create a tiled print without a specific standard
                return create_tiled_print_from_definition(img, print_def)
        except Exception as e:
            logger.error(f"Error creating tiled print from definition: {str(e)}")
            return None, f"Error creating tiled print from definition: {str(e)}"

    # Get the DPI from the standard or use a default value
    if standard_id:
        standard = get_photo_standard(standard_id)
        if standard:
            dpi = standard.dimensions.dpi
        else:
            dpi = 300.0
    else:
        dpi = 300.0

    # Calculate spacing in pixels using DpiService
    spacing_px = int(DpiService.convert_to_pixels(spacing_mm, "mm", dpi))

    # Get image dimensions
    height, width = img.shape[:2]

    # Calculate tiled image dimensions
    tiled_width = width * num_cols + spacing_px * (num_cols - 1)
    tiled_height = height * num_rows + spacing_px * (num_rows - 1)

    # Create tiled image with pure white background (255 in BGR is white)
    tiled_img = np.ones((tiled_height, tiled_width, 3), dtype=np.uint8) * 255  # White background

    # Ensure the input image has a white background by creating a white background image
    # and only copying the non-white pixels from the original image
    bg_color = (255, 255, 255)  # White in BGR format

    # Place copies of the image in the tiled image
    for row in range(num_rows):
        for col in range(num_cols):
            x = col * (width + spacing_px)
            y = row * (height + spacing_px)

            # Copy the image to the tiled image
            tiled_img[y:y+height, x:x+width] = img

            # Add a white border around each image to ensure clean separation
            if spacing_px > 0:
                # Draw white borders around the image (1px border)
                border_thickness = 1
                cv2.rectangle(
                    tiled_img,
                    (x-border_thickness, y-border_thickness),
                    (x+width+border_thickness, y+height+border_thickness),
                    bg_color,
                    border_thickness
                )

            # Add a light grey border around each photo for easier cutting
            border_thickness = 1
            border_color = (204, 204, 204)  # Light grey in BGR format
            cv2.rectangle(
                tiled_img,
                (x, y),
                (x+width-1, y+height-1),
                border_color,
                border_thickness
            )

    # Generate a new image ID for the tiled image
    tiled_image_id = str(uuid.uuid4())

    # Store the tiled image
    image_store[tiled_image_id] = {
        "image": tiled_img,
        "width": tiled_width,
        "height": tiled_height,
        "format": "JPEG",
        "landmarks": None,
        "original_image_id": image_id
    }

    return tiled_image_id, None


def create_tiled_print_from_definition(
    img: np.ndarray,
    print_def: Any,
    standard: Optional[Any] = None
) -> Tuple[Optional[str], Optional[str]]:
    """
    Create a tiled print using a print definition.

    Args:
        img: The image to tile
        print_def: The print definition to use
        standard: Optional photo standard

    Returns:
        Tuple of (tiled image ID or None, error message or None)
    """
    try:
        # Get image dimensions
        height, width = img.shape[:2]

        # If this is a digital size (no dimensions), just return the original image
        if print_def.width <= 0 or print_def.height <= 0 or not print_def.units:
            # Generate a new image ID
            tiled_image_id = str(uuid.uuid4())

            # Store the image
            image_store[tiled_image_id] = {
                "image": img.copy(),
                "width": width,
                "height": height,
                "format": "JPEG",
                "landmarks": None
            }

            return tiled_image_id, None

        # Get the optimal resolution (DPI) using DpiService
        standard_dpi = standard.dimensions.dpi if standard and hasattr(standard, 'dimensions') and hasattr(standard.dimensions, 'dpi') else 300.0
        dpi = DpiService.get_optimal_dpi(standard_dpi, print_def.resolution)

        # Convert dimensions to pixels using DpiService
        canvas_width_px = int(DpiService.convert_to_pixels(print_def.width, print_def.units, dpi))
        canvas_height_px = int(DpiService.convert_to_pixels(print_def.height, print_def.units, dpi))
        padding_px = int(DpiService.convert_to_pixels(print_def.padding, print_def.units, dpi))
        gutter_px = int(DpiService.convert_to_pixels(print_def.gutter, print_def.units, dpi)) if hasattr(print_def, 'gutter') and print_def.gutter else 0

        # Check if we should use custom photo count
        if hasattr(print_def, 'custom_photo_count') and print_def.custom_photo_count and hasattr(print_def, 'num_rows') and hasattr(print_def, 'num_cols'):
            # Use the specified number of rows and columns
            num_rows = print_def.num_rows
            num_cols = print_def.num_cols
            logger.info(f"Using custom photo count: {num_cols}x{num_rows} = {num_cols * num_rows} photos")

            # Calculate if this fits on the page
            required_width = (num_cols * width) + ((num_cols - 1) * gutter_px) + (2 * padding_px)
            required_height = (num_rows * height) + ((num_rows - 1) * gutter_px) + (2 * padding_px)

            if required_width > canvas_width_px or required_height > canvas_height_px:
                logger.warning(f"Custom photo count {num_cols}x{num_rows} doesn't fit on the page. Required: {required_width}x{required_height}px, Available: {canvas_width_px}x{canvas_height_px}px")
                # Fall back to standard calculation
                num_cols = max(1, int((canvas_width_px - 2 * padding_px) / (width + gutter_px)))
                num_rows = max(1, int((canvas_height_px - 2 * padding_px) / (height + gutter_px)))
                logger.info(f"Falling back to standard layout: {num_cols}x{num_rows} = {num_cols * num_rows} photos")
        # Check if we should maximize the number of photos
        elif hasattr(print_def, 'maximize_photos') and print_def.maximize_photos:
            # Try with minimal padding and gutter to maximize photo count
            original_padding_px = padding_px
            original_gutter_px = gutter_px

            # Calculate with minimal values
            padding_px = 0
            gutter_px = 0
            max_cols = max(1, int(canvas_width_px / width))
            max_rows = max(1, int(canvas_height_px / height))

            # If we can fit more photos with minimal values, use those
            if max_cols * max_rows > int((canvas_width_px - 2 * original_padding_px) / (width + original_gutter_px)) * int((canvas_height_px - 2 * original_padding_px) / (height + original_gutter_px)):
                num_cols = max_cols
                num_rows = max_rows
                logger.info(f"Maximizing photos: {num_cols}x{num_rows} = {num_cols * num_rows} photos")
            else:
                # Otherwise use the original calculation
                padding_px = original_padding_px
                gutter_px = original_gutter_px
                num_cols = max(1, int((canvas_width_px - 2 * padding_px) / (width + gutter_px)))
                num_rows = max(1, int((canvas_height_px - 2 * padding_px) / (height + gutter_px)))
                logger.info(f"Using standard layout: {num_cols}x{num_rows} = {num_cols * num_rows} photos")
        else:
            # Standard calculation
            num_cols = max(1, int((canvas_width_px - 2 * padding_px) / (width + gutter_px)))
            num_rows = max(1, int((canvas_height_px - 2 * padding_px) / (height + gutter_px)))
            logger.info(f"Standard layout: {num_cols}x{num_rows} = {num_cols * num_rows} photos")

        # Create the canvas with the specified background color
        bg_color = (255, 255, 255)  # Default white background in BGR format
        if hasattr(print_def, 'paper_color') and print_def.paper_color:
            # Convert hex color to BGR
            paper_color = print_def.paper_color.lstrip('#')
            if len(paper_color) == 6:
                bg_color = tuple(int(paper_color[i:i+2], 16) for i in (4, 2, 0))  # BGR format

        # Prepare border color if borders are enabled
        border_color = (0, 0, 0)  # Default black border in BGR format
        if hasattr(print_def, 'show_borders') and print_def.show_borders and hasattr(print_def, 'border_color') and print_def.border_color:
            # Convert hex color to BGR
            border_color_hex = print_def.border_color.lstrip('#')
            if len(border_color_hex) == 6:
                border_color = tuple(int(border_color_hex[i:i+2], 16) for i in (4, 2, 0))  # BGR format

        # Create the canvas
        canvas = np.ones((canvas_height_px, canvas_width_px, 3), dtype=np.uint8) * np.array(bg_color, dtype=np.uint8)

        # Calculate spacing between photos
        dx = width + gutter_px
        dy = height + gutter_px

        # Calculate starting position (centered if there's extra space)
        offset_x = padding_px
        offset_y = padding_px

        # Center align if there's extra space
        extra_width = canvas_width_px - 2 * padding_px - num_cols * width - (num_cols - 1) * gutter_px
        extra_height = canvas_height_px - 2 * padding_px - num_rows * height - (num_rows - 1) * gutter_px

        if extra_width > 0:
            offset_x += extra_width // 2

        if extra_height > 0:
            offset_y += extra_height // 2

        # Place the photos on the canvas
        for row in range(num_rows):
            for col in range(num_cols):
                x = offset_x + col * dx
                y = offset_y + row * dy

                # Make sure we don't go out of bounds
                if x + width <= canvas_width_px and y + height <= canvas_height_px:
                    # Copy the image to the canvas
                    canvas[y:y+height, x:x+width] = img

                    # Add border if enabled or if it's a default layout (not custom)
                    if (hasattr(print_def, 'show_borders') and print_def.show_borders) or (hasattr(print_def, 'custom') and not print_def.custom):
                        # Calculate border width in pixels using DpiService
                        border_width_px = 0
                        if hasattr(print_def, 'border_width') and print_def.border_width > 0:
                            border_width_px = int(DpiService.convert_to_pixels(print_def.border_width, print_def.units, dpi))

                        # Ensure border width is at least 1 pixel
                        border_width_px = max(1, border_width_px)

                        # Use a light grey border for default layouts
                        if hasattr(print_def, 'custom') and not print_def.custom:
                            border_color = (204, 204, 204)  # Light grey in BGR format

                        # Draw border around the photo
                        cv2.rectangle(
                            canvas,
                            (x, y),
                            (x + width - 1, y + height - 1),
                            border_color,
                            border_width_px
                        )

        # Generate a new image ID
        tiled_image_id = str(uuid.uuid4())

        # Store the tiled image
        image_store[tiled_image_id] = {
            "image": canvas,
            "width": canvas_width_px,
            "height": canvas_height_px,
            "format": "JPEG",
            "landmarks": None,
            "print_definition_id": print_def.id if hasattr(print_def, 'id') else None
        }

        return tiled_image_id, None
    except Exception as e:
        logger.error(f"Error creating tiled print from definition: {str(e)}")
        return None, f"Error creating tiled print from definition: {str(e)}"


def generate_preview_svg(
    image_id: str,
    standard_id: str,
    crown_point: Optional[Point] = None,
    chin_point: Optional[Point] = None
) -> Tuple[Optional[str], Optional[str]]:
    """
    Generate a preview SVG with compliance markers for a photo.

    Args:
        image_id: ID of the image to process
        standard_id: ID of the photo standard to use
        crown_point: Optional crown point (if not provided, will use detected landmarks)
        chin_point: Optional chin point (if not provided, will use detected landmarks)

    Returns:
        Tuple of (SVG content or None, error message or None)
    """
    # Check if image exists
    if image_id not in image_store:
        return None, f"Image with ID {image_id} not found"

    # Get the image and landmarks
    img = image_store[image_id]['image']
    landmarks = image_store[image_id].get('landmarks')

    if landmarks is None:
        return None, "No landmarks detected for this image"

    # Get the photo standard
    standard = get_photo_standard(standard_id)
    if standard is None:
        return None, f"Photo standard with ID {standard_id} not found"

    # Use provided crown and chin points or get them from landmarks
    if crown_point is None or chin_point is None:
        if landmarks.crown_chin is None:
            return None, "No crown-chin points detected and none provided"
        crown_pt = landmarks.crown_chin.crownPoint
        chin_pt = landmarks.crown_chin.chinPoint
    else:
        crown_pt = crown_point
        chin_pt = chin_point

    try:
        # First, crop the photo according to the standard
        processed_img = crop_and_resize_photo(img, crown_pt, chin_pt, standard, landmarks)

        # Create a low-resolution version of the processed image for preview
        preview_img = create_low_res_preview(processed_img)

        # Add a watermark to the preview image
        watermarked_img = add_watermark(preview_img, "Vizaphoto")

        # Generate the SVG with embedded image and compliance markers
        svg_content = create_compliance_svg(watermarked_img, standard, crown_pt, chin_pt, landmarks)

        return svg_content, None
    except Exception as e:
        logger.error(f"Error generating preview SVG: {str(e)}")
        return None, f"Error generating preview SVG: {str(e)}"


def add_watermark(img: np.ndarray, watermark_text: str) -> np.ndarray:
    """
    Add a watermark to an image.

    Note: This function is kept for API consistency, but we now add the watermark
    directly in the SVG with proper transparency.

    Args:
        img: Input image as numpy array
        watermark_text: Text to use as watermark (unused in this implementation)

    Returns:
        Image as numpy array (unchanged)
    """
    # Log that we're using SVG watermarking instead
    logger.debug(f"Watermark '{watermark_text}' will be added in SVG instead of directly on the image")

    # Return the image unchanged - watermark will be added in SVG
    return img.copy()


def create_low_res_preview(img: np.ndarray, max_width: int = 600) -> np.ndarray:
    """
    Create a low-resolution version of an image for preview purposes.

    Args:
        img: Input image as numpy array
        max_width: Maximum width of the preview image

    Returns:
        Low-resolution image as numpy array with white background
    """
    height, width = img.shape[:2]

    # Calculate scale factor to reduce image size
    if width > max_width:
        scale_factor = max_width / width
        new_width = max_width
        new_height = int(height * scale_factor)

        # Resize the image
        preview_img = cv2.resize(img, (new_width, new_height))
    else:
        preview_img = img.copy()

    # Use the white background from the previous processing steps
    bg_color = (255, 255, 255)  # White in BGR format

    # Add a white border to ensure the entire image has a white background
    border_size = 2
    preview_img = cv2.copyMakeBorder(
        preview_img,
        border_size, border_size, border_size, border_size,
        cv2.BORDER_CONSTANT,
        value=bg_color
    )

    # Use maximum quality for the preview
    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 100]  # 100% quality
    _, buffer = cv2.imencode('.jpg', preview_img, encode_param)
    preview_img = cv2.imdecode(buffer, cv2.IMREAD_COLOR)

    return preview_img


def create_compliance_svg(
    img: np.ndarray,
    standard: PhotoStandard,
    crown_point: Point,
    chin_point: Point,
    landmarks: Optional[Any] = None
) -> str:
    """
    Create an SVG with embedded image and compliance markers.

    Args:
        img: Input image as numpy array (already cropped and watermarked)
        standard: Photo standard to use
        crown_point: Crown point
        chin_point: Chin point
        landmarks: Optional facial landmarks for more accurate eye line positioning

    Returns:
        SVG content as string
    """
    # Get image dimensions
    height, width = img.shape[:2]

    # Convert image to base64 for embedding in SVG with maximum quality
    _, buffer = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
    img_base64 = base64.b64encode(buffer).decode('utf-8')

    # Get standard dimensions
    dimensions = standard.dimensions
    units = dimensions.units

    # Get dimensions in the original units
    std_width = dimensions.pictureWidth
    std_height = dimensions.pictureHeight

    # Get face height
    face_height = dimensions.faceHeight

    # For display purposes, scale everything to fit nicely in the SVG
    max_svg_size = 600  # Reduced to make the SVG smaller

    # Calculate scaling factor to fit within max_svg_size
    # Leave more margin for the labels and borders
    max_content_size = max_svg_size * 0.65  # Reduced to leave more space for larger text and spacing
    scale_factor = min(max_content_size / width, max_content_size / height)

    # Calculate SVG dimensions
    svg_width = max_svg_size
    svg_height = max_svg_size

    # Calculate border dimensions (the photo frame)
    border_width = width * scale_factor
    border_height = height * scale_factor

    # Center the border in the SVG with extra space for text and spacing
    border_x = (svg_width - border_width) / 2 + 15  # Add extra space on the left for text and spacing
    border_y = (svg_height - border_height) / 2

    # Calculate the position of crown and chin points in the cropped image
    # For a standard passport photo, the crown is typically at the top of the head
    # and the chin is at the bottom of the face

    # Note: We don't need to calculate the face height in pixels or the ratio for the SVG preview
    # as we're using the standard dimensions directly

    # For the SVG preview, we need to place the crown and chin markers at their actual positions
    # The crown should be at a fixed distance from the top based on the standard
    # and the chin should be at crown + face_height

    # Calculate the crown position in the final image
    # Determine the crown top value to use
    crown_top_mm = None

    if hasattr(dimensions, 'bottomEyeLine') and dimensions.bottomEyeLine is not None and dimensions.bottomEyeLine > 0:
        # Convert eye line to bottom distance into crown top distance
        # The coefficient represents the ratio of chin-to-eye distance relative to total face height
        chin_frown_coefficient = 0.8945 / 1.7699
        crown_top_mm = (std_height - dimensions.bottomEyeLine -
                       face_height * (1 - chin_frown_coefficient))
        logger.info(
            f"SVG Preview: Using bottomEyeLine: {dimensions.bottomEyeLine} {dimensions.units}, calculated crownTop: {crown_top_mm:.2f} {dimensions.units}"
        )
    elif hasattr(dimensions, 'crownTop') and dimensions.crownTop is not None and dimensions.crownTop > 0:
        # Use the specified crown top distance
        crown_top_mm = dimensions.crownTop
        logger.info(
            f"SVG Preview: Using crownTop: {crown_top_mm} {dimensions.units}"
        )
    else:
        # Default to 10% from top if not specified
        crown_top_mm = std_height * 0.1

    # Calculate the crown and chin positions in the SVG
    # Use the actual crown and chin points from the cropped image
    # Since the image is already cropped according to the standard, we can map the points directly
    # The crown point should be at a fixed percentage from the top of the image
    crown_y_percentage = crown_top_mm / std_height
    chin_y_percentage = (crown_top_mm + face_height) / std_height

    # Map these percentages to the SVG coordinates
    crown_y_svg = border_y + (border_height * crown_y_percentage)
    chin_y_svg = border_y + (border_height * chin_y_percentage)

    logger.info(f"SVG Preview: crown_y_percentage={crown_y_percentage:.4f}, chin_y_percentage={chin_y_percentage:.4f}")
    logger.info(f"SVG Preview: crown_y_svg={crown_y_svg:.2f}, chin_y_svg={chin_y_svg:.2f}")

    # Calculate eye line position if bottomEyeLine is specified
    eye_line_y_svg = None
    if hasattr(dimensions, 'bottomEyeLine') and dimensions.bottomEyeLine is not None and dimensions.bottomEyeLine > 0:
        # Try to get actual eye positions from landmarks if available
        if landmarks and hasattr(landmarks, 'key_points'):
            # Check for eye points in key_points (A and B are the pupils)
            left_eye = getattr(landmarks.key_points, 'A', None)
            right_eye = getattr(landmarks.key_points, 'B', None)

            if left_eye and right_eye:
                # Calculate average eye position in the original image
                eye_y_position = (left_eye.y + right_eye.y) / 2

                # We need to calculate the eye position in the cropped image
                # First, calculate the face height in pixels in the original image
                face_height_px = math.sqrt((crown_point.x - chin_point.x)**2 + (crown_point.y - chin_point.y)**2)

                # Calculate the ratio of the eye position between crown and chin
                # This gives us a relative position that we can apply to the cropped image
                if face_height_px > 0:
                    eye_ratio = (eye_y_position - crown_point.y) / face_height_px

                    # Apply this ratio to the SVG coordinates
                    eye_line_y_svg = crown_y_svg + (chin_y_svg - crown_y_svg) * eye_ratio

                    # Log the calculation details
                    logger.info(
                        f"Using actual eye position from landmarks: eye_y_position={eye_y_position:.2f}, "
                        f"crown_point.y={crown_point.y:.2f}, face_height_px={face_height_px:.2f}, "
                        f"eye_ratio={eye_ratio:.4f}, eye_line_y_svg={eye_line_y_svg:.2f}")
                else:
                    # Fallback if we can't calculate the ratio
                    eye_line_y_svg = border_y + (eye_y_position / height * border_height)
                    logger.info(
                        f"Using fallback eye position calculation: eye_y_position={eye_y_position:.2f} -> eye_line_y_svg={eye_line_y_svg:.2f}")
            else:
                # Fall back to standard calculation if eye points aren't available
                # Calculate eye line position as a percentage from the top of the image
                eye_line_percentage = (std_height - dimensions.bottomEyeLine) / std_height
                eye_line_y_svg = border_y + (border_height * eye_line_percentage)
                logger.info(
                    f"Using standard calculation for eye line: bottomEyeLine={dimensions.bottomEyeLine} {units}, "
                    f"eye_line_percentage={eye_line_percentage:.4f}, eye_line_y_svg={eye_line_y_svg:.2f}")
        else:
            # Fall back to standard calculation if landmarks aren't available
            # Calculate eye line position as a percentage from the top of the image
            eye_line_percentage = (std_height - dimensions.bottomEyeLine) / std_height
            eye_line_y_svg = border_y + (border_height * eye_line_percentage)
            logger.info(
                f"Using standard calculation for eye line (no landmarks): bottomEyeLine={dimensions.bottomEyeLine} {units}, "
                f"eye_line_percentage={eye_line_percentage:.4f}, eye_line_y_svg={eye_line_y_svg:.2f}")

    # Note: We're using border_x + border_width * 0.6 directly in the SVG for horizontal markers

    # Format dimensions for display
    if units == "inch":
        width_display = f"{std_width} inch"
        height_display = f"{std_height} inch"
        face_height_display = f"{face_height:.1f} inch"
        eye_line_display = f"{dimensions.bottomEyeLine:.1f} inch" if dimensions.bottomEyeLine is not None else None
    elif units == "mm":
        width_display = f"{std_width} mm"
        height_display = f"{std_height} mm"
        face_height_display = f"{face_height:.1f} mm"
        eye_line_display = f"{dimensions.bottomEyeLine:.1f} mm" if dimensions.bottomEyeLine is not None else None
    else:  # pixel
        width_display = f"{int(std_width)} px"
        height_display = f"{int(std_height)} px"
        face_height_display = f"{int(face_height)} px"
        eye_line_display = f"{int(dimensions.bottomEyeLine)} px" if dimensions.bottomEyeLine is not None else None

    # Create the eye line SVG content separately
    eye_line_svg = ""
    if eye_line_y_svg is not None:
        # Calculate position for vertical line - closer to the photo
        vertical_line_x = border_x + border_width + 40

        eye_line_svg = f'''<!-- Eye line marker - horizontal line extending from inside the photo to the vertical line -->
<line x1="{border_x + border_width * 0.6}" y1="{eye_line_y_svg}" x2="{vertical_line_x}" y2="{eye_line_y_svg}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Vertical line from eye line to bottom of photo -->
<line x1="{vertical_line_x}" y1="{eye_line_y_svg}" x2="{vertical_line_x}" y2="{border_y + border_height}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Eye line text - rotated 90 degrees and positioned outside the vertical line with text-anchor="start" -->
<text x="{vertical_line_x + 18}" y="{eye_line_y_svg + 65}" text-anchor="start" fill="#0EAAC5" font-family="Inter, Arial, sans-serif" font-size="16" transform="rotate(90, {vertical_line_x + 10}, {eye_line_y_svg + 65})">{eye_line_display}</text>'''

    # Create the SVG content
    svg_content = f'''<svg width="{svg_width}" height="{svg_height}" viewBox="0 0 {svg_width} {svg_height}" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g>
<rect width="{svg_width}" height="{svg_height}" fill="white"/>

<!-- Embedded photo with white background -->
<rect x="{border_x}" y="{border_y}" width="{border_width}" height="{border_height}" fill="white"/>
<image x="{border_x}" y="{border_y}" width="{border_width}" height="{border_height}" xlink:href="data:image/jpeg;base64,{img_base64}" preserveAspectRatio="none"/>

<!-- Semi-transparent watermark rectangle -->
<rect x="{border_x}" y="{border_y + border_height * 0.9}" width="{border_width}" height="{border_height * 0.1}" fill="#B3E0EA" fill-opacity="0.6"/>
<text x="{border_x + border_width/2}" y="{border_y + border_height * 0.9 + (border_height * 0.1)/2}" text-anchor="middle" dominant-baseline="middle" fill="#102043" font-family="Inter, Arial, sans-serif" font-size="20">Vizaphoto.com</text>

<!-- Photo border -->
<rect x="{border_x}" y="{border_y}" width="{border_width}" height="{border_height}" fill="none" stroke="#102043" stroke-width="2"/>

<!-- Horizontal measurement line at top -->
<line x1="{border_x}" y1="{border_y - 20}" x2="{border_x + border_width}" y2="{border_y - 20}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Horizontal measurement ticks at top -->
<line x1="{border_x}" y1="{border_y - 25}" x2="{border_x}" y2="{border_y - 15}" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="{border_x + border_width}" y1="{border_y - 25}" x2="{border_x + border_width}" y2="{border_y - 15}" stroke="#0EAAC5" stroke-width="1.5"/>
<text x="{border_x + border_width/2}" y="{border_y - 28}" text-anchor="middle" fill="#0EAAC5" font-family="Inter, Arial, sans-serif" font-size="16">{width_display}</text>

<!-- Vertical measurement line on right -->
<line x1="{border_x + border_width + 20}" y1="{border_y}" x2="{border_x + border_width + 20}" y2="{border_y + border_height}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Vertical measurement ticks on right -->
<line x1="{border_x + border_width + 15}" y1="{border_y}" x2="{border_x + border_width + 25}" y2="{border_y}" stroke="#0EAAC5" stroke-width="1.5"/>
<line x1="{border_x + border_width + 15}" y1="{border_y + border_height}" x2="{border_x + border_width + 25}" y2="{border_y + border_height}" stroke="#0EAAC5" stroke-width="1.5"/>
<!-- Position height text higher up when eye line exists -->
<text x="{border_x + border_width + 30}" y="{border_y + (border_height/4 if eye_line_y_svg is not None else border_height/2)}" text-anchor="middle" fill="#0EAAC5" font-family="Inter, Arial, sans-serif" font-size="16" transform="rotate(90, {border_x + border_width + 30}, {border_y + (border_height/4 if eye_line_y_svg is not None else border_height/2)})">{height_display}</text>

<!-- Head ratio markers with vertical measurement -->
<!-- Vertical measurement lines on left side -->
<line x1="{border_x - 25}" y1="{crown_y_svg}" x2="{border_x - 25}" y2="{chin_y_svg}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Crown marker - horizontal line extending from left side into the photo (60% of width) -->
<line x1="{border_x - 25}" y1="{crown_y_svg}" x2="{border_x + border_width * 0.6}" y2="{crown_y_svg}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Chin marker - horizontal line extending from left side into the photo (60% of width) -->
<line x1="{border_x - 25}" y1="{chin_y_svg}" x2="{border_x + border_width * 0.6}" y2="{chin_y_svg}" stroke="#0EAAC5" stroke-width="1.5"/>

<!-- Face height text -->
<text x="{border_x - 35}" y="{(crown_y_svg + chin_y_svg)/2}" text-anchor="middle" fill="#0EAAC5" font-family="Inter, Arial, sans-serif" font-size="16" transform="rotate(-90, {border_x - 35}, {(crown_y_svg + chin_y_svg)/2})">{face_height_display}</text>

<!-- Eye line marker (only for standards with bottomEyeLine) -->
{eye_line_svg}

<!-- Document info below the box -->
<text x="{border_x + border_width/2}" y="{border_y + border_height + 35}" text-anchor="middle" fill="#0EAAC5" font-family="Inter, Arial, sans-serif" font-size="16">{standard.country} {standard.docType} Photo</text>

</g>
</svg>'''

    return svg_content


def convert_svg_to_jpg(
    svg_content: str,
    width: int = 600,
    height: int = 600,
    quality: int = 100
) -> Tuple[Optional[np.ndarray], Optional[str]]:
    """
    Convert SVG content to a JPG image.

    Args:
        svg_content: SVG content as a string
        width: Width of the output image
        height: Height of the output image
        quality: JPEG quality (0-100)

    Returns:
        Tuple of (image as numpy array or None, error message or None)
    """
    try:
        if CAIROSVG_AVAILABLE:
            # Use CairoSVG to convert SVG to PNG
            png_data = cairosvg.svg2png(
                bytestring=svg_content.encode('utf-8'),
                output_width=width,
                output_height=height
            )

            # Convert PNG to numpy array using PIL
            pil_img = Image.open(io.BytesIO(png_data))
            img = np.array(pil_img.convert('RGB'))

            # Convert from RGB to BGR (OpenCV format)
            img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

            # Apply quality setting for JPEG compression
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, buffer = cv2.imencode('.jpg', img, encode_param)
            img = cv2.imdecode(buffer, cv2.IMREAD_COLOR)

            return img, None
        else:
            # Fallback method: Create a temporary SVG file and use OpenCV
            logger.warning("Using fallback method for SVG to JPG conversion")

            # Extract the base64 image from the SVG
            import re
            img_base64_match = re.search(r'xlink:href="data:image/jpeg;base64,([^"]+)"', svg_content)
            if not img_base64_match:
                return None, "Could not extract base64 image from SVG"

            img_base64 = img_base64_match.group(1)
            img_data = base64.b64decode(img_base64)

            # Convert to numpy array
            nparr = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                return None, "Failed to decode image from SVG"

            # Resize to desired dimensions
            img = cv2.resize(img, (width, height))

            # Apply quality setting for JPEG compression
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, buffer = cv2.imencode('.jpg', img, encode_param)
            img = cv2.imdecode(buffer, cv2.IMREAD_COLOR)

            return img, None
    except Exception as e:
        logger.error(f"Error converting SVG to JPG: {str(e)}")
        return None, f"Error converting SVG to JPG: {str(e)}"


def generate_preview_jpeg(
    image_id: str,
    standard_id: str,
    crown_point: Optional[Point] = None,
    chin_point: Optional[Point] = None
) -> Tuple[Optional[bytes], Optional[str]]:
    """
    Generate a preview JPEG with compliance markers for a photo.

    Args:
        image_id: ID of the image to process
        standard_id: ID of the photo standard to use
        crown_point: Optional crown point (if not provided, will use detected landmarks)
        chin_point: Optional chin point (if not provided, will use detected landmarks)

    Returns:
        Tuple of (JPEG content as bytes or None, error message or None)
    """
    try:
        # First, generate the SVG preview
        svg_content, error = generate_preview_svg(image_id, standard_id, crown_point, chin_point)
        if error:
            return None, error

        # Convert SVG to JPG with maximum quality
        img, error = convert_svg_to_jpg(svg_content, quality=100)
        if error:
            return None, error

        # Encode as JPEG with maximum quality
        _, jpg_data = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 100])

        return jpg_data.tobytes(), None
    except Exception as e:
        logger.error(f"Error generating preview JPEG: {str(e)}")
        return None, f"Error generating preview JPEG: {str(e)}"