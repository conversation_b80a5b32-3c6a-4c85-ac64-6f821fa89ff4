import os
import logging
import asyncio
import uuid
from typing import Optional, <PERSON><PERSON>, Dict, Any
from io import BytesIO
import cv2
import numpy as np
from PIL import Image

try:
    import fal_client
except ImportError:
    fal_client = None

from app.services.landmark_detection import image_store

logger = logging.getLogger(__name__)


class BackgroundRemovalService:
    """
    Service for removing backgrounds from images using BiRefNet v2 API.
    """

    def __init__(self):
        self.model = "fal-ai/birefnet/v2"
        self.api_key = os.getenv("FAL_KEY")
        self.enabled = os.getenv("BACKGROUND_REMOVAL_ENABLED", "true").lower() == "true"

        if not self.api_key and self.enabled:
            logger.warning("FAL_KEY not set. Background removal will be disabled.")
            self.enabled = False

        if not fal_client and self.enabled:
            logger.warning("fal-client not installed. Background removal will be disabled.")
            self.enabled = False

    def is_enabled(self) -> bool:
        """Check if background removal is enabled and properly configured."""
        return self.enabled

    async def remove_background_async(self, image_id: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Remove background from an image asynchronously.

        Args:
            image_id: ID of the image in the image store

        Returns:
            Tuple of (processed_image_id, error_message)
        """
        if not self.is_enabled():
            return None, "Background removal is not enabled or properly configured"

        if image_id not in image_store:
            return None, f"Image with ID {image_id} not found"

        try:
            # Get the image from store
            img_data = image_store[image_id]
            img = img_data['image']

            # Convert OpenCV image (BGR) to PIL image (RGB)
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(img_rgb)

            # Save image to temporary file for upload
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                pil_img.save(temp_file.name, format='JPEG', quality=95)
                temp_file_path = temp_file.name

            try:
                # Upload image to fal.ai
                logger.info(f"Uploading image {image_id} to fal.ai")
                file_url = fal_client.upload_file(temp_file_path)
            finally:
                # Clean up temporary file
                import os
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

            # Submit background removal request
            logger.info(f"Submitting background removal request for image {image_id}")
            handler = await fal_client.submit_async(
                self.model,
                arguments={
                    "image_url": file_url,
                    "model": "Portrait",  # Best for passport photos
                    "operating_resolution": "2048x2048",  # High quality
                    "output_format": "png",  # Preserve transparency
                    "refine_foreground": True  # Better edge quality
                }
            )

            # Wait for completion and get result
            logger.info(f"Waiting for background removal completion for image {image_id}")
            result = await handler.get()

            if not result or 'image' not in result:
                return None, "Failed to get result from background removal API"

            # Download the processed image
            image_url = result['image']['url']
            logger.info(f"Downloading processed image from {image_url}")

            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url)
                if response.status_code != 200:
                    return None, f"Failed to download processed image: {response.status_code}"

                processed_img_bytes = response.content

            # Convert back to OpenCV format
            pil_processed = Image.open(BytesIO(processed_img_bytes))

            # Convert RGBA to RGB with white background for passport photos
            if pil_processed.mode == 'RGBA':
                # Create white background
                white_bg = Image.new('RGB', pil_processed.size, (255, 255, 255))
                white_bg.paste(pil_processed, mask=pil_processed.split()[-1])  # Use alpha channel as mask
                pil_processed = white_bg
            elif pil_processed.mode != 'RGB':
                pil_processed = pil_processed.convert('RGB')

            # Convert to OpenCV format (BGR)
            processed_img_array = np.array(pil_processed)
            processed_img_bgr = cv2.cvtColor(processed_img_array, cv2.COLOR_RGB2BGR)

            # Generate new image ID for processed image
            processed_image_id = str(uuid.uuid4())

            # Store processed image
            height, width = processed_img_bgr.shape[:2]
            image_store[processed_image_id] = {
                'image': processed_img_bgr,
                'width': width,
                'height': height,
                'format': 'JPEG',
                'landmarks': None,
                'original_image_id': image_id,
                'background_removed': True,
                'processing_metadata': {
                    'model': 'Portrait',
                    'resolution': '2048x2048',
                    'api_result': result
                }
            }

            logger.info(f"Background removal completed for image {image_id}, new ID: {processed_image_id}")
            return processed_image_id, None

        except Exception as e:
            logger.error(f"Error removing background for image {image_id}: {str(e)}")
            return None, f"Error removing background: {str(e)}"

    def remove_background_sync(self, image_id: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Synchronous wrapper for background removal.

        Args:
            image_id: ID of the image in the image store

        Returns:
            Tuple of (processed_image_id, error_message)
        """
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(self.remove_background_async(image_id))

    async def get_processing_status(self, request_id: str) -> Dict[str, Any]:
        """
        Get the status of a background removal request.

        Args:
            request_id: The request ID from fal.ai

        Returns:
            Status information dictionary
        """
        if not self.is_enabled():
            return {"error": "Background removal is not enabled"}

        try:
            status = await fal_client.status_async(self.model, request_id, with_logs=True)
            return status
        except Exception as e:
            logger.error(f"Error getting status for request {request_id}: {str(e)}")
            return {"error": str(e)}


# Global service instance
background_removal_service = BackgroundRemovalService()


def remove_background(image_id: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Convenience function to remove background from an image.

    Args:
        image_id: ID of the image in the image store

    Returns:
        Tuple of (processed_image_id, error_message)
    """
    return background_removal_service.remove_background_sync(image_id)


def is_background_removal_enabled() -> bool:
    """
    Check if background removal is enabled.

    Returns:
        True if background removal is enabled and properly configured
    """
    return background_removal_service.is_enabled()
