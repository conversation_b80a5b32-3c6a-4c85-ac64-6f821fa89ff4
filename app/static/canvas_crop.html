<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Crop Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
        }
        .container {
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .controls {
            display: flex;
            gap: 10px;
        }
        .canvas-container {
            position: relative;
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 5px;
            overflow: hidden;
            background-color: #303030;
        }
        #image-canvas {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
        }
        #viewport {
            position: relative;
            width: 100%;
            height: 100%;
            touch-action: none;
            user-select: none;
            cursor: default;
            overflow: hidden;
        }
        .svg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            background: transparent;
        }
        .landmark {
            stroke-width: 2px;
            stroke-linecap: round;
            fill-opacity: 0.5;
            cursor: grab;
        }
        #crown-mark {
            fill: blueviolet;
            stroke: white;
        }
        #chin-mark {
            fill: indianred;
            stroke: white;
        }
        .landmark:hover {
            stroke-width: 3px;
            stroke: orange;
            fill-opacity: 0.7;
        }
        .annotation {
            stroke: lightgray;
            stroke-width: 1.5;
            stroke-dasharray: 5, 5;
            stroke-opacity: 0.7;
        }
        #crop-rect {
            stroke: lightgray;
            stroke-width: 2px;
            fill: none;
        }
        .text {
            fill: white;
            text-shadow: 4px 4px 8px black;
        }
        .button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .spinner {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        .standard-selector {
            margin-bottom: 20px;
        }
        .standard-selector select {
            padding: 8px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ccc;
            min-width: 250px;
        }
        .preview-container {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .preview-container h3 {
            margin-bottom: 10px;
        }
        .preview-image {
            max-width: 100%;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .instructions {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 5px;
            border-left: 5px solid #4CAF50;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2E7D32;
        }
        .instructions ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">Photo Crop Tool</div>
            <div class="controls">
                <input type="file" id="file-input" accept="image/*" style="display: none;">
                <button class="button" id="load-image-btn">Load Image</button>
                <button class="button" id="crop-btn" disabled>Crop Image</button>
                <button class="button" id="reset-btn">Reset</button>
            </div>
        </div>

        <div class="instructions">
            <h3>Instructions</h3>
            <ul>
                <li>Load an image using the "Load Image" button</li>
                <li>Select a document type from the dropdown</li>
                <li>Drag the purple (crown) and red (chin) markers to position them correctly</li>
                <li>Use mouse wheel to zoom in/out and drag to pan the image</li>
                <li>Click "Crop Image" to preview the result</li>
                <li>Click "Save" to save the cropped image</li>
            </ul>
        </div>

        <div class="standard-selector">
            <select id="standard-selector">
                <option value="">Select a document type</option>
                {% for standard in standards %}
                <option value="{{ standard.id }}">{{ standard.text }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="canvas-container">
            <canvas id="image-canvas"></canvas>
            <div id="viewport">
                <div id="spinner" class="spinner" style="display: none;"></div>
                <svg class="svg-overlay" id="overlay">
                    <defs>
                        <filter id="shadow">
                            <feDropShadow dx="0" dy="0.2" stdDeviation="0.2" flood-color="white" />
                        </filter>
                    </defs>
                    <line id="middle-line" class="annotation" x1="0" y1="0" x2="0" y2="0"></line>
                    <line id="crown-line" class="annotation" x1="0" y1="0" x2="0" y2="0"></line>
                    <line id="chin-line" class="annotation" x1="0" y1="0" x2="0" y2="0"></line>
                    <path id="crop-rect" d="M 0 0 L 0 0 L 0 0 L 0 0 Z"></path>
                    <circle class="landmark" id="crown-mark" cx="0" cy="0" r="12" style="visibility: hidden;"></circle>
                    <circle class="landmark" id="chin-mark" cx="0" cy="0" r="12" style="visibility: hidden;"></circle>
                    <text class="text" id="crown-text" x="0" y="0" style="visibility: hidden;">Crown</text>
                    <text class="text" id="chin-text" x="0" y="0" style="visibility: hidden;">Chin</text>
                </svg>
            </div>
        </div>
        <div id="result-container" style="margin-top: 20px; display: none;">
            <h3>Cropped Image Preview:</h3>
            <img id="cropped-image" class="preview-image">
            <div class="button-group">
                <button class="button" id="save-btn">Save Image</button>
                <button class="button" id="back-btn">Back to Editing</button>
            </div>
        </div>
    </div>

    <!-- Main application script -->
    <script src="/static/js/canvas_crop.js"></script>
</body>
</html>
