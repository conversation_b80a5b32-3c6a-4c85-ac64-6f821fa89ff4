/* Crop component styles */

.crop-container {
    position: relative;
    margin: 20px 0;
    overflow: hidden;
    border: 1px solid #ccc;
    background-color: #f0f0f0;
    display: none;
}

.crop-canvas {
    display: block;
    max-width: 100%;
}

.crop-box {
    position: absolute;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5), 0 0 10px rgba(0, 0, 0, 0.5);
    cursor: move;
}

.crop-box.outside-bounds {
    border: 2px solid #dc3545;
    box-shadow: 0 0 0 1px rgba(220, 53, 69, 0.5), 0 0 10px rgba(220, 53, 69, 0.5);
}

.resize-handle {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #fff;
    border: 1px solid #333;
}

.resize-handle.nw {
    top: -5px;
    left: -5px;
    cursor: nw-resize;
}

.resize-handle.ne {
    top: -5px;
    right: -5px;
    cursor: ne-resize;
}

.resize-handle.sw {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize;
}

.resize-handle.se {
    bottom: -5px;
    right: -5px;
    cursor: se-resize;
}

.resize-handle.n {
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    cursor: n-resize;
}

.resize-handle.s {
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    cursor: s-resize;
}

.resize-handle.e {
    right: -5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: e-resize;
}

.resize-handle.w {
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: w-resize;
}

.preview-container {
    margin: 20px 0;
    display: none;
}

.preview-canvas {
    display: block;
    margin: 0 auto;
    border: 1px solid #ccc;
    background-color: #fff;
}

.loading-indicator {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
}

.btn:hover {
    background-color: #0069d9;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

.text-center {
    text-align: center;
}

.mt-3 {
    margin-top: 1rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.hidden {
    display: none;
}
