/* Payment Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 25px;
}

/* Payment Info Styles */
.payment-info {
    text-align: center;
    margin-bottom: 25px;
}

.payment-info h3 {
    color: #333;
    margin-bottom: 8px;
    font-size: 1.3rem;
}

.payment-info p {
    color: #666;
    margin-bottom: 20px;
}

.pricing-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.price-label {
    font-weight: 500;
    color: #333;
}

.price-value {
    font-weight: 700;
    font-size: 1.4rem;
    color: #28a745;
}

.features {
    text-align: left;
}

.features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features li {
    padding: 5px 0;
    color: #555;
    font-size: 0.95rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input:invalid {
    border-color: #dc3545;
}

/* Button Styles */
.payment-buttons {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.button {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.button.secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e1e5e9;
}

.button.secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Processing State */
.processing-info,
.success-info,
.error-info {
    text-align: center;
    padding: 30px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.success-icon,
.error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.processing-info h3,
.success-info h3,
.error-info h3 {
    margin-bottom: 10px;
    color: #333;
}

.processing-info p,
.success-info p,
.error-info p {
    color: #666;
    margin-bottom: 10px;
}

.processing-info small {
    color: #999;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 600px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-header,
    .modal-body {
        padding: 20px;
    }
    
    .payment-buttons {
        flex-direction: column;
    }
    
    .button {
        width: 100%;
    }
}

/* Integration with existing styles */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Payment status indicators */
.payment-status {
    padding: 10px 15px;
    border-radius: 6px;
    margin: 15px 0;
    font-weight: 500;
}

.payment-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.payment-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.payment-status.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}
