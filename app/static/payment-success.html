<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Passport Photo Maker</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .success-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1.5rem;
            animation: bounce 1s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        h1 {
            color: #28a745;
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .message {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .download-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid #28a745;
        }

        .download-info h3 {
            color: #28a745;
            margin-bottom: 0.5rem;
        }

        .download-info p {
            color: #666;
            margin-bottom: 0.5rem;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        .tracking-info {
            margin-top: 2rem;
            padding: 1rem;
            background: #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
            color: #666;
        }

        @media (max-width: 600px) {
            .success-container {
                padding: 2rem;
                margin: 1rem;
            }

            .buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <h1>Payment Successful!</h1>
        
        <div class="message">
            Thank you for your purchase! Your payment has been processed successfully.
        </div>

        <div class="download-info">
            <h3><i class="fas fa-download"></i> Download Access Granted</h3>
            <p><strong>Downloads Remaining:</strong> 5</p>
            <p><strong>Access Valid For:</strong> 30 days</p>
            <p><strong>Download Link Expires:</strong> 15 minutes per download</p>
        </div>

        <div class="buttons">
            <a href="#" id="download-btn" class="btn btn-primary">
                <i class="fas fa-download"></i>
                Download Your Photo
            </a>
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                Process Another Photo
            </a>
        </div>

        <div class="tracking-info" id="tracking-info" style="display: none;">
            <strong>Transaction ID:</strong> <span id="tracking-id"></span>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const trackingId = urlParams.get('tracking_id');
        const orderId = urlParams.get('order_id');

        // Show tracking info if available
        if (trackingId) {
            document.getElementById('tracking-id').textContent = trackingId;
            document.getElementById('tracking-info').style.display = 'block';
        }

        // Handle download button
        document.getElementById('download-btn').addEventListener('click', async (e) => {
            e.preventDefault();
            
            if (orderId) {
                try {
                    // Get download link
                    const response = await fetch(`/api/payment/download/${orderId}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        // Trigger download
                        window.location.href = data.download_url;
                    } else {
                        alert(`Download failed: ${data.error}`);
                    }
                } catch (error) {
                    console.error('Download error:', error);
                    alert('Failed to get download link. Please try again.');
                }
            } else {
                alert('No order ID found. Please contact support.');
            }
        });

        // Auto-redirect after 30 seconds if no action
        setTimeout(() => {
            if (confirm('Would you like to return to the home page?')) {
                window.location.href = '/';
            }
        }, 30000);
    </script>
</body>
</html>
