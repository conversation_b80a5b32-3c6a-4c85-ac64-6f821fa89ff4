<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passport Photo Processor</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-test {
            margin-top: 20px;
            padding: 25px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .section-header h2 {
            margin: 0;
            font-size: 1.5rem;
            color: #3498db;
        }
        .section-header .icon {
            margin-right: 10px;
            font-size: 1.5rem;
            color: #3498db;
        }
        button {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background-color 0.3s;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        button .icon {
            margin-right: 8px;
        }
        #detectBtn {
            background-color: #2ecc71;
        }
        #detectBtn:hover {
            background-color: #27ae60;
        }
        #updateBtn {
            background-color: #e74c3c;
        }
        #updateBtn:hover {
            background-color: #c0392b;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-family: 'Consolas', monospace;
            font-size: 0.9rem;
        }
        .file-upload {
            margin-top: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .file-input-container {
            position: relative;
            margin-bottom: 15px;
        }
        .file-input-label {
            display: inline-block;
            padding: 10px 15px;
            background-color: #e9ecef;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            border: 1px solid #ced4da;
        }
        .file-input-label:hover {
            background-color: #dde2e6;
        }
        .selected-file {
            margin-top: 8px;
            font-size: 0.9rem;
            color: #6c757d;
        }
        .image-preview {
            max-width: 100%;
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .image-preview img {
            max-width: 100%;
            max-height: 400px;
            display: block;
            margin: 0 auto;
        }
        .landmarks-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: all; /* Enable pointer events for dragging */
            cursor: default;
            z-index: 10; /* Ensure canvas is above the image */
        }
        .image-container {
            position: relative;
            display: inline-block;
            margin: 0 auto;
            text-align: center;
            /* Remove width: 100% to prevent stretching */
        }
        .instructions {
            margin-top: 15px;
            padding: 15px;
            background-color: #e8f4fd;
            border-radius: 5px;
            font-size: 0.9rem;
            color: #2c3e50;
            border-left: 4px solid #3498db;
        }
        .instructions h3 {
            margin-top: 0;
            color: #3498db;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .instructions ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
        .status-message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 0.9rem;
            display: none;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Crop Tool Styles */
        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            border-radius: 5px;
            overflow: hidden;
            background-color: #303030;
            margin-top: 20px;
        }

        #cropCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        #cropViewport {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .svg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .annotation {
            stroke: white;
            stroke-width: 1.5;
            stroke-dasharray: 5,5;
            fill: none;
        }

        .landmark {
            fill: purple;
            stroke: white;
            stroke-width: 2;
            cursor: grab;
            pointer-events: all;
        }

        #chin-mark {
            fill: red;
        }

        .text {
            fill: white;
            font-family: Arial, sans-serif;
            font-size: 14px;
            filter: url(#shadow);
            pointer-events: none;
        }

        .warning {
            fill: red;
            font-weight: bold;
        }

        .standard-selector {
            margin: 15px 0;
        }

        .standard-selector select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            font-size: 1rem;
            width: 100%;
            max-width: 400px;
        }

        .button-group {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .cropped-image-container {
            max-width: 100%;
            text-align: center;
            margin-top: 10px;
            position: relative;
            min-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cropped-image-container img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 4px;
        }

        .preview-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .preview-loading span {
            margin-top: 10px;
            color: #495057;
        }

        .preview-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #721c24;
        }

        .preview-error i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .preview-error p {
            margin: 0;
        }

        .svg-preview-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            max-width: 100%;
            overflow: hidden;
        }

        .svg-preview-container svg {
            max-width: 100%;
            height: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .photo-preview-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .photo-preview-container h3 {
            color: #3498db;
            margin-top: 0;
            margin-bottom: 15px;
        }

        .processed-image-container {
            max-width: 100%;
            text-align: center;
            margin: 20px auto;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }

        .processed-image-container img {
            max-width: 100%;
            max-height: 600px;
            border-radius: 4px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -20px;
            margin-left: -20px;
        }

        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }

        /* Collapsible styles */
        .collapsible-container {
            margin-top: 15px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            overflow: hidden;
        }

        .collapsible-button {
            background-color: #f8f9fa;
            color: #495057;
            cursor: pointer;
            padding: 12px 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1rem;
            transition: 0.4s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .collapsible-button:hover {
            background-color: #e9ecef;
        }

        .collapsible-button i {
            transition: transform 0.3s ease;
        }

        .collapsible-button.active i {
            transform: rotate(180deg);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: #f8f9fa;
        }

        .collapsible-content.active {
            max-height: 500px;
            padding: 15px;
        }

        /* Print Layout Styles */
        .print-layout-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
            padding: 10px;
        }

        .print-layout-item {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .print-layout-item:hover {
            border-color: #adb5bd;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .print-layout-item.selected {
            border-color: #3498db;
            border-width: 2px;
            background-color: rgba(52, 152, 219, 0.05);
        }

        /* Custom layout styles */
        .print-layout-item.custom-layout {
            border-color: #9b59b6;
        }

        .print-layout-item.custom-layout:hover {
            border-color: #8e44ad;
        }

        .print-layout-item.custom-layout.selected {
            border-color: #3498db;
        }

        /* Default layout styles with grey borders for easier cutting */
        .print-layout-item.default-layout .print-layout-thumbnail img {
            border: 1px solid #cccccc;
        }

        /* Add borders to the canvas in default layouts */
        .print-layout-item.default-layout .print-layout-thumbnail canvas {
            border: 1px solid #cccccc;
        }

        /* Layout header with title and action buttons */
        .print-layout-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-top: 8px;
        }

        .print-layout-thumbnail {
            width: 100%;
            height: 200px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .print-layout-thumbnail img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .print-layout-title {
            font-weight: 500;
            color: #3498db;
            flex: 1;
        }

        /* Action buttons for custom layouts */
        .print-layout-actions {
            display: flex;
            gap: 5px;
        }

        .action-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .edit-button {
            color: #3498db;
        }

        .delete-button {
            color: #e74c3c;
        }

        .print-layout-details {
            font-size: 0.9rem;
            color: #6c757d;
            text-align: center;
        }

        .print-preview-container {
            margin-top: 30px;
            padding: 20px;
            border-top: 1px solid #dee2e6;
        }

        .print-preview-image {
            width: 100%;
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        .print-preview-image img {
            max-width: 100%;
            max-height: 500px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Add a new layout button */
        .add-layout-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            border: 2px dashed #adb5bd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-layout-button:hover {
            background-color: rgba(52, 152, 219, 0.05);
            border-color: #3498db;
        }

        .add-layout-button i {
            font-size: 2rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .add-layout-button span {
            color: #6c757d;
            text-align: center;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-group small {
            display: block;
            color: #666;
            font-size: 0.8em;
            margin-top: 5px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        .checkbox-group label {
            display: inline;
            margin-bottom: 0;
        }

        .border-options {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .cancel-button {
            background-color: #f44336;
            margin-left: 10px;
        }

        .cancel-button:hover {
            background-color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-id-card"></i> Passport Photo Processor</h1>
        <p>Welcome to the Passport Photo Processor application. This application helps you create passport photos that comply with various country-specific requirements.</p>
        <div class="button-bar" style="display: flex; justify-content: flex-end; margin-bottom: 15px;">
            <button id="print-multiple-photos-btn" class="button" style="display: flex; align-items: center; gap: 5px;">
                <i class="fas fa-print"></i> Print Multiple Photos
            </button>
        </div>

        <div class="api-test">
            <div class="section-header">
                <i class="icon fas fa-server"></i>
                <h2>API Test</h2>
            </div>
            <button id="pingBtn"><i class="icon fas fa-exchange-alt"></i> Test API Ping</button>
            <button id="locationBtn"><i class="icon fas fa-map-marker-alt"></i> Test Location API</button>
            <div id="result">
                <h3>Response:</h3>
                <pre id="response">Click a button to test the API</pre>
            </div>
        </div>

        <div class="api-test">
            <div class="section-header">
                <i class="icon fas fa-face-smile"></i>
                <h2>Facial Landmark Detection</h2>
            </div>

            <div class="file-upload">
                <div class="file-input-container">
                    <label for="imageUpload" class="file-input-label">
                        <i class="fas fa-file-image"></i> Choose Photo
                    </label>
                    <input type="file" id="imageUpload" accept="image/*" />
                    <div class="selected-file" id="selectedFileName">No file selected</div>
                </div>
                <button id="uploadBtn"><i class="icon fas fa-cloud-upload-alt"></i> Upload</button>
            </div>

            <div class="loading" id="uploadLoading">
                <div class="loading-spinner"></div>
                <span>Uploading...</span>
            </div>

            <div class="status-message" id="uploadStatus"></div>

            <div>
                <button id="detectBtn" disabled><i class="icon fas fa-magic"></i> Detect Landmarks</button>
                <button id="updateBtn" disabled><i class="icon fas fa-redo-alt"></i> Reset Points</button>
            </div>

            <div class="loading" id="detectLoading">
                <div class="loading-spinner"></div>
                <span>Detecting landmarks...</span>
            </div>

            <div class="status-message" id="landmarkStatus"></div>

            <div class="image-preview" id="imagePreview">
                <div class="image-container">
                    <img id="previewImage" style="display: none;" />
                    <canvas id="landmarksCanvas" class="landmarks-canvas"></canvas>
                </div>
            </div>

            <div class="instructions">
                <h3><i class="fas fa-hand-pointer"></i> Facial Landmark Points</h3>
                <ul>
                    <li><strong>P (Crown Point):</strong> Should be at the top of the head</li>
                    <li><strong>Q (Chin Point):</strong> Should be at the bottom of the chin</li>
                    <li><strong>A, B:</strong> Left and right eye pupils</li>
                    <li><strong>C, D:</strong> Left and right mouth corners</li>
                    <li><strong>M:</strong> Midpoint between eyes</li>
                    <li><strong>N:</strong> Nose tip</li>
                    <li>Click and drag the P and Q points to adjust their positions</li>
                    <li>The distance between P and Q is shown in the middle</li>
                </ul>
            </div>

            <div class="collapsible-container">
                <button class="collapsible-button" id="landmarksToggle">
                    <i class="fas fa-chevron-down"></i> Show Landmarks Data
                </button>
                <div class="collapsible-content" id="landmarksResult">
                    <pre id="landmarksResponse">Upload an image and detect landmarks</pre>
                </div>
            </div>
        </div>

        <div class="api-test" id="background-removal-section" style="display: none;">
            <div class="section-header">
                <i class="icon fas fa-magic"></i>
                <h2>Background Removal</h2>
            </div>

            <div class="instructions">
                <h3><i class="fas fa-info-circle"></i> Background Removal</h3>
                <ul>
                    <li>Remove the background from your photo for a cleaner passport photo</li>
                    <li>Uses AI-powered background removal optimized for portrait photos</li>
                    <li>The background will be replaced with a clean white background</li>
                    <li>You can skip this step if your photo already has a suitable background</li>
                </ul>
            </div>

            <div class="button-group">
                <button id="removeBackgroundBtn" disabled><i class="icon fas fa-magic"></i> Remove Background</button>
                <button id="skipBackgroundBtn" disabled><i class="icon fas fa-forward"></i> Skip Background Removal</button>
                <button id="compareImagesBtn" disabled style="display: none;"><i class="icon fas fa-exchange-alt"></i> Compare Images</button>
            </div>

            <div class="loading" id="backgroundRemovalLoading">
                <div class="loading-spinner"></div>
                <span>Removing background...</span>
            </div>

            <div class="status-message" id="backgroundRemovalStatus"></div>

            <div class="image-preview" id="backgroundRemovalPreview" style="display: none;">
                <h3>Background Removal Result</h3>
                <div class="image-container">
                    <img id="backgroundRemovedImage" style="display: none;" />
                </div>
                <div class="button-group">
                    <button id="useProcessedImageBtn"><i class="icon fas fa-check"></i> Use This Image</button>
                    <button id="useOriginalImageBtn"><i class="icon fas fa-undo"></i> Use Original Image</button>
                </div>
            </div>

            <div class="image-preview" id="imageComparisonPreview" style="display: none;">
                <h3>Image Comparison</h3>
                <div class="image-container">
                    <img id="comparisonImage" style="display: none;" />
                </div>
            </div>
        </div>

        <div class="api-test">
            <div class="section-header">
                <i class="icon fas fa-crop-alt"></i>
                <h2>Photo Crop Tool</h2>
            </div>

            <div class="instructions">
                <h3><i class="fas fa-info-circle"></i> Instructions</h3>
                <ul>
                    <li>First upload and detect landmarks in the section above</li>
                    <li>Select a document type from the dropdown</li>
                    <li>Drag the purple (crown) and red (chin) markers to position them correctly</li>
                    <li>Use mouse wheel to zoom in/out and drag to pan the image</li>
                    <li>Click "Crop Image" to preview the result</li>
                    <li>Click "Save" to save the cropped image</li>
                </ul>
            </div>

            <div class="standard-selector">
                <label for="standard-selector">Select Document Type:</label>
                <select id="standard-selector">
                    <option value="">Select a document type</option>
                </select>
            </div>

            <div>
                <button id="cropBtn" disabled><i class="icon fas fa-crop"></i> Crop Image</button>
                <button id="resetViewBtn"><i class="icon fas fa-undo"></i> Reset View</button>
            </div>

            <div class="loading" id="cropLoading">
                <div class="loading-spinner"></div>
                <span>Processing image...</span>
            </div>

            <div class="status-message" id="cropStatus"></div>

            <div class="alert alert-danger" id="crop-warning" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i> Warning: The crop box is outside the image boundaries. Please adjust it.
            </div>

            <div class="canvas-container">
                <canvas id="cropCanvas"></canvas>
                <div id="cropViewport">
                    <div id="cropSpinner" class="spinner" style="display: none;"></div>
                    <svg class="svg-overlay" id="cropOverlay">
                        <defs>
                            <filter id="shadow">
                                <feDropShadow dx="0" dy="0.2" stdDeviation="0.2" flood-color="white" />
                            </filter>
                        </defs>
                        <ellipse id="face-ellipse" class="annotation" cx="0" cy="0" rx="0" ry="0" fill="none" stroke="lightblue" stroke-width="1.5" stroke-dasharray="5,5" stroke-opacity="0.7"></ellipse>
                        <line id="middle-line" class="annotation" x1="0" y1="0" x2="0" y2="0"></line>
                        <line id="crown-line" class="annotation" x1="0" y1="0" x2="0" y2="0"></line>
                        <line id="chin-line" class="annotation" x1="0" y1="0" x2="0" y2="0"></line>
                        <path id="crop-rect" d="M 0 0 L 0 0 L 0 0 L 0 0 Z" fill="none" stroke="lightgray" stroke-width="2"></path>
                        <circle class="landmark" id="crown-mark" cx="0" cy="0" r="12" style="visibility: hidden;"></circle>
                        <circle class="landmark" id="chin-mark" cx="0" cy="0" r="12" style="visibility: hidden;"></circle>
                        <text class="text" id="crown-text" x="0" y="0" style="visibility: hidden;">Crown</text>
                        <text class="text" id="chin-text" x="0" y="0" style="visibility: hidden;">Chin</text>
                        <text class="text" id="face-height-text" x="20" y="100" style="visibility: hidden;">Face: 0%</text>
                        <text class="text" id="rotation-text" x="20" y="120" style="visibility: hidden;">Rotation: 0°</text>
                        <text class="text warning" id="warning-text" x="20" y="50" style="visibility: hidden;">Warning: Face proportion does not meet standard requirements</text>
                        <text class="text" id="photo-size-measurement" x="20" y="150" style="visibility: hidden;">Photo Size: 0x0mm</text>
                    </svg>
                </div>
            </div>

            <div id="result-container" style="margin-top: 20px; display: none;">
                <h3>Cropped Image Preview:</h3>
                <div class="cropped-image-container">
                    <div class="preview-loading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <span>Generating preview...</span>
                    </div>
                    <img id="cropped-image" class="preview-image" onload="this.style.display='block'" onerror="this.style.display='none'" style="display: none;">
                    <div class="preview-error" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error loading preview. Please try again.</p>
                    </div>
                </div>
                <div class="button-group">
                    <button class="button" id="saveBtn">Save Image</button>
                    <button class="button" id="backBtn">Back to Editing</button>
                </div>
            </div>
        </div>

        <div class="api-test" id="print-layout-section" style="display: none;">
            <div class="section-header">
                <i class="icon fas fa-print"></i>
                <h2>Print Layout</h2>
                <button id="print-layout-toggle-btn" class="button" style="position: absolute; right: 20px; top: 20px;">
                    <i class="fas fa-times"></i> Close Print Layout
                </button>
            </div>

            <div class="instructions">
                <h3><i class="fas fa-info-circle"></i> Instructions</h3>
                <ul>
                    <li>Select a print layout from the options below</li>
                    <li>Preview how your photo will look in the selected layout</li>
                    <li>Click "Create Print" to generate the final print</li>
                </ul>
            </div>

            <div class="print-layout-container" id="print-layout-container">
                <!-- Print layouts will be loaded here dynamically -->
                <div class="loading" id="print-layout-loading">
                    <div class="loading-spinner"></div>
                    <span>Loading print layouts...</span>
                </div>
            </div>

            <div class="preview-container" id="print-preview-container" style="display: none;">
                <h3>Print Preview</h3>
                <div class="print-preview-image">
                    <img id="print-preview-image" src="" alt="Print Layout Preview">
                </div>
                <div class="button-group">
                    <button class="button" id="create-print-btn">Create Print</button>
                    <button class="button" id="download-print-btn">Download Print</button>
                </div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <script>
        // Geometry classes for crop functionality
        class Point {
            constructor(x, y) {
                this.x = x;
                this.y = y;
            }

            equals(pt) {
                return pt.x === this.x && pt.y === this.y;
            }

            norm() {
                return Math.sqrt(this.x * this.x + this.y * this.y);
            }

            add(pt) {
                return new Point(this.x + pt.x, this.y + pt.y);
            }

            sub(pt) {
                return new Point(this.x - pt.x, this.y - pt.y);
            }

            mult(scalar) {
                return new Point(this.x * scalar, this.y * scalar);
            }

            div(scalar) {
                return new Point(this.x / scalar, this.y / scalar);
            }

            distTo(pt) {
                const dx = this.x - pt.x;
                const dy = this.y - pt.y;
                return Math.sqrt(dx * dx + dy * dy);
            }

            angle(pt) {
                const dx = this.x - pt.x;
                const dy = this.y - pt.y;
                return Math.atan2(dy, dx);
            }
        }

        class RotatedRect {
            constructor(center, length1, length2, angle) {
                this.center = center;
                this.length1 = length1;
                this.length2 = length2;
                this.angle = angle;
            }

            corners() {
                const w2 = this.length1 / 2;
                const h2 = this.length2 / 2;
                const cosAngle = Math.cos(this.angle);
                const sinAngle = Math.sin(this.angle);
                const corners = [];
                for (const p of [
                    [-w2, -h2],
                    [-w2, h2],
                    [w2, h2],
                    [w2, -h2],
                ]) {
                    const x = this.center.x + p[0] * cosAngle - p[1] * sinAngle;
                    const y = this.center.y + p[0] * sinAngle + p[1] * cosAngle;
                    corners.push(new Point(x, y));
                }
                return corners;
            }
        }

        // Photo dimensions class
        class PhotoDimensions {
            constructor(pictureWidth, pictureHeight, units, faceHeight, crownTop, dpi, bottomEyeLine = 0) {
                this.pictureWidth = pictureWidth;
                this.pictureHeight = pictureHeight;
                this.units = units;
                this.faceHeight = faceHeight;
                this.crownTop = crownTop;
                this.dpi = dpi;
                this.bottomEyeLine = bottomEyeLine;
            }
        }

        // Utility functions
        function middlePoint(pt1, pt2) {
            return new Point((pt1.x + pt2.x) / 2, (pt1.y + pt2.y) / 2);
        }

        function pointAtDistance(p0, p1, dist) {
            if (p1.equals(p0)) {
                throw new Error('Input points cannot be equal');
            }
            const ratio = dist / p1.sub(p0).norm();
            return p0.add(p1.sub(p0).mult(ratio));
        }

        function getAffineTransform(fr, to) {
            const x0 = to[0].x;
            const y0 = to[0].y;
            const x1 = to[1].x;
            const y1 = to[1].y;
            const x2 = to[2].x;
            const y2 = to[2].y;
            const u0 = fr[0].x;
            const v0 = fr[0].y;
            const u1 = fr[1].x;
            const v1 = fr[1].y;
            const u2 = fr[2].x;
            const v2 = fr[2].y;

            // Compute matrix transform
            const delta = u0 * v1 + v0 * u2 + u1 * v2 - v1 * u2 - v0 * u1 - u0 * v2;
            const deltaA = x0 * v1 + v0 * x2 + x1 * v2 - v1 * x2 - v0 * x1 - x0 * v2;
            const deltaB = u0 * x1 + x0 * u2 + u1 * x2 - x1 * u2 - x0 * u1 - u0 * x2;
            const deltaC = u0 * v1 * x2 + v0 * x1 * u2 + x0 * u1 * v2 - x0 * v1 * u2 - v0 * u1 * x2 - u0 * x1 * v2;
            const deltaD = y0 * v1 + v0 * y2 + y1 * v2 - v1 * y2 - v0 * y1 - y0 * v2;
            const deltaE = u0 * y1 + y0 * u2 + u1 * y2 - y1 * u2 - y0 * u1 - u0 * y2;
            const deltaF = u0 * v1 * y2 + v0 * y1 * u2 + y0 * u1 * v2 - y0 * v1 * u2 - v0 * u1 * y2 - u0 * y1 * v2;

            return [deltaA / delta, deltaD / delta, deltaB / delta, deltaE / delta, deltaC / delta, deltaF / delta];
        }

        function getCroppingCenter(p, crownPoint, chinPoint) {
            if (!(p.crownTop > 0) && !(p.bottomEyeLine > 0)) {
                // Estimate the center of the picture to be the median point between the crown point and the chin point
                return middlePoint(crownPoint, chinPoint);
            }

            // Calculate the face height in pixels
            const faceHeightPix = crownPoint.distTo(chinPoint);

            // Calculate the ratio between image pixels and photo dimensions
            const mmToPixRatio = faceHeightPix / p.faceHeight;

            let crownTop = 0;
            if (p.crownTop > 0) {
                crownTop = p.crownTop;
            } else if (p.bottomEyeLine > 0) {
                // Convert eye line to bottom distance into crown top distance
                // The coefficient represents the ratio of chin-to-eye distance relative to total face height
                const chinFrownCoefficient = 0.8945 / 1.7699;
                crownTop = p.pictureHeight - p.bottomEyeLine - p.faceHeight * (1 - chinFrownCoefficient);
            }

            // Calculate the distance from crown to center of the photo
            const crownToCenter = p.pictureHeight / 2 - crownTop;

            // Calculate the distance from crown to center in pixels
            const crownToCenterPix = mmToPixRatio * crownToCenter;

            // Calculate the center point by moving from crown towards chin
            return pointAtDistance(crownPoint, chinPoint, crownToCenterPix);
        }

        function getCroppingRectangle(p, crownPoint, chinPoint) {
            // Get the center of the crop rectangle
            const centerPic = getCroppingCenter(p, crownPoint, chinPoint);

            // Calculate dimensions and orientation
            const faceHeightPix = crownPoint.distTo(chinPoint);
            const normal = crownPoint.angle(chinPoint);
            const scale = faceHeightPix / p.faceHeight;
            const cropHeightPix = p.pictureHeight * scale;
            const cropWidthPix = p.pictureWidth * scale;

            return new RotatedRect(centerPic, cropHeightPix, cropWidthPix, normal);
        }

        // Global variables
        let currentImageId = null;
        let landmarks = null;
        let canvasContext = null;
        let imageWidth = 0;
        let imageHeight = 0;

        // Initialize canvas
        const canvas = document.getElementById('landmarksCanvas');
        canvasContext = canvas.getContext('2d');

        // API Test functions
        document.getElementById('pingBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/ping');
                const data = await response.json();
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('response').textContent = 'Error: ' + error.message;
            }
        });

        document.getElementById('locationBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/location');
                const data = await response.json();
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('response').textContent = 'Error: ' + error.message;
            }
        });

        // File input handling
        document.getElementById('imageUpload').addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'No file selected';
            document.getElementById('selectedFileName').textContent = fileName;
        });

        // Facial landmark detection functions
        document.getElementById('uploadBtn').addEventListener('click', async () => {
            const fileInput = document.getElementById('imageUpload');
            const file = fileInput.files[0];
            const uploadLoading = document.getElementById('uploadLoading');
            const uploadStatus = document.getElementById('uploadStatus');

            if (!file) {
                uploadStatus.textContent = 'Please select an image file';
                uploadStatus.className = 'status-message status-error';
                uploadStatus.style.display = 'block';
                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 3000);
                return;
            }

            try {
                // Show loading indicator
                uploadLoading.style.display = 'flex';

                // Create form data
                const formData = new FormData();
                formData.append('file', file);

                // Upload the image
                const response = await fetch('/api/landmarks/upload', {
                    method: 'POST',
                    body: formData
                });

                // Hide loading indicator
                uploadLoading.style.display = 'none';

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();
                currentImageId = data.image_id;

                // Display the image
                const previewImage = document.getElementById('previewImage');
                previewImage.src = URL.createObjectURL(file);
                previewImage.style.display = 'block';

                // Store image dimensions and set up canvas when image loads
                previewImage.onload = () => {
                    // Store original image dimensions
                    imageWidth = previewImage.naturalWidth;
                    imageHeight = previewImage.naturalHeight;

                    // Resize canvas to match image
                    canvas.width = previewImage.clientWidth;
                    canvas.height = previewImage.clientHeight;

                    // Position canvas directly over the image
                    canvas.style.width = previewImage.clientWidth + 'px';
                    canvas.style.height = previewImage.clientHeight + 'px';

                    // Force a redraw if browser is zoomed
                    setTimeout(() => {
                        // Double-check dimensions after browser has fully rendered
                        if (canvas.width !== previewImage.clientWidth ||
                            canvas.height !== previewImage.clientHeight) {
                            canvas.width = previewImage.clientWidth;
                            canvas.height = previewImage.clientHeight;
                            canvas.style.width = previewImage.clientWidth + 'px';
                            canvas.style.height = previewImage.clientHeight + 'px';
                        }
                    }, 100);
                };

                // Enable detect button
                document.getElementById('detectBtn').disabled = false;

                // Check if background removal is available and show the section
                await checkBackgroundRemovalStatus();

                // Show success message
                uploadStatus.textContent = 'Image uploaded successfully!';
                uploadStatus.className = 'status-message status-success';
                uploadStatus.style.display = 'block';
                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 3000);

                // Show response
                document.getElementById('landmarksResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                // Hide loading indicator
                uploadLoading.style.display = 'none';

                // Show error message
                uploadStatus.textContent = 'Error: ' + error.message;
                uploadStatus.className = 'status-message status-error';
                uploadStatus.style.display = 'block';

                document.getElementById('landmarksResponse').textContent = 'Error: ' + error.message;
            }
        });

        // Background removal functions
        let originalImageId = null;
        let processedImageId = null;

        async function checkBackgroundRemovalStatus() {
            try {
                const response = await fetch('/api/background-removal/status');
                const data = await response.json();

                if (data.enabled) {
                    // Show background removal section
                    document.getElementById('background-removal-section').style.display = 'block';

                    // Enable background removal buttons
                    document.getElementById('removeBackgroundBtn').disabled = false;
                    document.getElementById('skipBackgroundBtn').disabled = false;
                } else {
                    // Hide background removal section if not available
                    document.getElementById('background-removal-section').style.display = 'none';
                }
            } catch (error) {
                console.log('Background removal not available:', error);
                document.getElementById('background-removal-section').style.display = 'none';
            }
        }

        document.getElementById('removeBackgroundBtn').addEventListener('click', async () => {
            if (!currentImageId) {
                showBackgroundRemovalStatus('Please upload an image first', 'error');
                return;
            }

            try {
                // Show loading indicator
                document.getElementById('backgroundRemovalLoading').style.display = 'flex';

                // Store original image ID
                originalImageId = currentImageId;

                // Remove background
                const response = await fetch('/api/background-removal/remove', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_id: currentImageId
                    })
                });

                // Hide loading indicator
                document.getElementById('backgroundRemovalLoading').style.display = 'none';

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    processedImageId = data.processed_image_id;

                    // Show the processed image
                    const backgroundRemovedImage = document.getElementById('backgroundRemovedImage');
                    backgroundRemovedImage.src = `/api/background-removal/preview/${processedImageId}?t=${new Date().getTime()}`;
                    backgroundRemovedImage.style.display = 'block';

                    // Show preview section
                    document.getElementById('backgroundRemovalPreview').style.display = 'block';

                    // Enable comparison button
                    document.getElementById('compareImagesBtn').style.display = 'inline-block';
                    document.getElementById('compareImagesBtn').disabled = false;

                    showBackgroundRemovalStatus('Background removed successfully!', 'success');
                } else {
                    showBackgroundRemovalStatus('Error: ' + data.error, 'error');
                }
            } catch (error) {
                // Hide loading indicator
                document.getElementById('backgroundRemovalLoading').style.display = 'none';
                showBackgroundRemovalStatus('Error: ' + error.message, 'error');
            }
        });

        document.getElementById('skipBackgroundBtn').addEventListener('click', () => {
            // Hide background removal section and proceed to landmark detection
            document.getElementById('background-removal-section').style.display = 'none';
            showBackgroundRemovalStatus('Background removal skipped', 'info');
        });

        document.getElementById('useProcessedImageBtn').addEventListener('click', () => {
            if (processedImageId) {
                // Update current image ID to use the processed image
                currentImageId = processedImageId;

                // Update the preview image
                const previewImage = document.getElementById('previewImage');
                previewImage.src = `/api/background-removal/preview/${processedImageId}?t=${new Date().getTime()}`;

                // Hide background removal section
                document.getElementById('background-removal-section').style.display = 'none';

                showBackgroundRemovalStatus('Using background-removed image', 'success');
            }
        });

        document.getElementById('useOriginalImageBtn').addEventListener('click', () => {
            if (originalImageId) {
                // Revert to original image
                currentImageId = originalImageId;

                // Update the preview image
                const previewImage = document.getElementById('previewImage');
                previewImage.src = `/image/${originalImageId}?t=${new Date().getTime()}`;

                // Hide background removal section
                document.getElementById('background-removal-section').style.display = 'none';

                showBackgroundRemovalStatus('Using original image', 'info');
            }
        });

        document.getElementById('compareImagesBtn').addEventListener('click', async () => {
            if (!originalImageId || !processedImageId) {
                showBackgroundRemovalStatus('Both images must be available for comparison', 'error');
                return;
            }

            try {
                // Show comparison image
                const comparisonImage = document.getElementById('comparisonImage');
                comparisonImage.src = `/api/background-removal/compare/${originalImageId}/${processedImageId}?t=${new Date().getTime()}`;
                comparisonImage.style.display = 'block';

                // Show comparison section
                document.getElementById('imageComparisonPreview').style.display = 'block';

                showBackgroundRemovalStatus('Comparison image generated', 'success');
            } catch (error) {
                showBackgroundRemovalStatus('Error generating comparison: ' + error.message, 'error');
            }
        });

        function showBackgroundRemovalStatus(message, type) {
            const statusElement = document.getElementById('backgroundRemovalStatus');
            statusElement.textContent = message;
            statusElement.className = `status-message status-${type}`;
            statusElement.style.display = 'block';
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 3000);
        }

        document.getElementById('detectBtn').addEventListener('click', async () => {
            if (!currentImageId) {
                const landmarkStatus = document.getElementById('landmarkStatus');
                landmarkStatus.textContent = 'Please upload an image first';
                landmarkStatus.className = 'status-message status-error';
                landmarkStatus.style.display = 'block';
                setTimeout(() => {
                    landmarkStatus.style.display = 'none';
                }, 3000);
                return;
            }

            try {
                // Show loading indicator
                const detectLoading = document.getElementById('detectLoading');
                detectLoading.style.display = 'flex';

                // Detect landmarks
                const response = await fetch(`/api/landmarks/detect/${currentImageId}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();

                // Store landmarks
                landmarks = data.landmarks;

                if (landmarks) {
                    // Get the aligned image
                    const previewImage = document.getElementById('previewImage');

                    // Update the image source to use the aligned image
                    previewImage.src = `/api/landmarks/aligned-image/${currentImageId}?t=${new Date().getTime()}`;

                    // Wait for the image to load before drawing landmarks
                    previewImage.onload = () => {
                        // Update image dimensions
                        imageWidth = previewImage.naturalWidth;
                        imageHeight = previewImage.naturalHeight;

                        // Draw landmarks on canvas
                        drawLandmarks(landmarks);
                        document.getElementById('updateBtn').disabled = false;

                        // Hide loading indicator
                        detectLoading.style.display = 'none';

                        // Show success message
                        const landmarkStatus = document.getElementById('landmarkStatus');
                        landmarkStatus.textContent = 'Landmarks detected and image aligned successfully!';
                        landmarkStatus.className = 'status-message status-success';
                        landmarkStatus.style.display = 'block';
                        setTimeout(() => {
                            landmarkStatus.style.display = 'none';
                        }, 3000);
                    };

                    // Handle image loading error
                    previewImage.onerror = () => {
                        // Hide loading indicator
                        detectLoading.style.display = 'none';

                        // Show error message
                        const landmarkStatus = document.getElementById('landmarkStatus');
                        landmarkStatus.textContent = 'Error loading aligned image';
                        landmarkStatus.className = 'status-message status-error';
                        landmarkStatus.style.display = 'block';
                    };
                } else {
                    // Hide loading indicator
                    detectLoading.style.display = 'none';

                    // Show warning message if no landmarks were detected
                    const landmarkStatus = document.getElementById('landmarkStatus');
                    landmarkStatus.textContent = 'No facial landmarks detected. Try a different image.';
                    landmarkStatus.className = 'status-message status-info';
                    landmarkStatus.style.display = 'block';
                }

                // Show response
                document.getElementById('landmarksResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                // Hide loading indicator
                document.getElementById('detectLoading').style.display = 'none';

                // Show error message
                const landmarkStatus = document.getElementById('landmarkStatus');
                landmarkStatus.textContent = 'Error: ' + error.message;
                landmarkStatus.className = 'status-message status-error';
                landmarkStatus.style.display = 'block';

                document.getElementById('landmarksResponse').textContent = 'Error: ' + error.message;
            }
        });

        // Variables to track dragging state
        let isDragging = false;
        let draggedPoint = null;

        // Make sure canvas has pointer events enabled
        canvas.style.pointerEvents = 'all';

        // Add event listeners for manual adjustment of crown-chin points
        canvas.addEventListener('mousedown', handleMouseDown);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseup', handleMouseUp);
        canvas.addEventListener('mouseleave', handleMouseUp);

        // Add touch support for mobile devices
        canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
        canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
        canvas.addEventListener('touchend', handleTouchEnd);
        canvas.addEventListener('touchcancel', handleTouchEnd);

        function handleTouchStart(event) {
            event.preventDefault();
            if (event.touches.length === 1) {
                const touch = event.touches[0];
                const mouseEvent = new MouseEvent('mousedown', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }
        }

        function handleTouchMove(event) {
            event.preventDefault();
            if (event.touches.length === 1) {
                const touch = event.touches[0];
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }
        }

        function handleTouchEnd(event) {
            const mouseEvent = new MouseEvent('mouseup', {});
            canvas.dispatchEvent(mouseEvent);
        }

        function handleMouseDown(event) {
            if (!landmarks || !landmarks.crown_chin) return;

            const rect = canvas.getBoundingClientRect();
            const mouseX = event.clientX - rect.left;
            const mouseY = event.clientY - rect.top;

            // Get current image and canvas dimensions
            const previewImage = document.getElementById('previewImage');

            // Calculate scale factors based on current display size
            const scaleX = canvas.width / imageWidth;
            const scaleY = canvas.height / imageHeight;

            // Check if mouse is over crown (P) or chin (Q) point
            const crownX = landmarks.crown_chin.crownPoint.x * scaleX;
            const crownY = landmarks.crown_chin.crownPoint.y * scaleY;
            const chinX = landmarks.crown_chin.chinPoint.x * scaleX;
            const chinY = landmarks.crown_chin.chinPoint.y * scaleY;

            // Calculate distance to points, adjusting for zoom level
            const distToCrown = Math.sqrt(Math.pow(mouseX - crownX, 2) + Math.pow(mouseY - crownY, 2));
            const distToChin = Math.sqrt(Math.pow(mouseX - chinX, 2) + Math.pow(mouseY - chinY, 2));

            // Adjust hit area based on zoom level (larger hit area when zoomed in)
            const hitRadius = Math.max(15, 15 * (previewImage.clientWidth / imageWidth));

            // If mouse is within hit radius of a point, start dragging
            if (distToCrown < hitRadius) {
                isDragging = true;
                draggedPoint = 'crown';
                canvas.style.cursor = 'grabbing';
            } else if (distToChin < hitRadius) {
                isDragging = true;
                draggedPoint = 'chin';
                canvas.style.cursor = 'grabbing';
            }
        }

        function handleMouseMove(event) {
            if (!isDragging || !draggedPoint) return;

            const rect = canvas.getBoundingClientRect();
            const mouseX = event.clientX - rect.left;
            const mouseY = event.clientY - rect.top;

            // Get current image and canvas dimensions to ensure proper scaling
            const previewImage = document.getElementById('previewImage');

            // Ensure canvas dimensions match the current image display size
            if (canvas.width !== previewImage.clientWidth || canvas.height !== previewImage.clientHeight) {
                canvas.width = previewImage.clientWidth;
                canvas.height = previewImage.clientHeight;
            }

            // Calculate scale factors based on current display size
            const scaleX = canvas.width / imageWidth;
            const scaleY = canvas.height / imageHeight;

            // Update the dragged point position
            if (draggedPoint === 'crown') {
                landmarks.crown_chin.crownPoint.x = mouseX / scaleX;
                landmarks.crown_chin.crownPoint.y = mouseY / scaleY;
            } else if (draggedPoint === 'chin') {
                landmarks.crown_chin.chinPoint.x = mouseX / scaleX;
                landmarks.crown_chin.chinPoint.y = mouseY / scaleY;
            }

            // Redraw landmarks
            drawLandmarks(landmarks);
        }

        async function handleMouseUp() {
            if (isDragging && draggedPoint) {
                // Reset dragging state
                isDragging = false;
                canvas.style.cursor = 'default';

                // Update crown-chin points on the server
                await updateCrownChinPoints();

                draggedPoint = null;
            }
        }

        async function updateCrownChinPoints() {
            if (!currentImageId || !landmarks || !landmarks.crown_chin) {
                return;
            }

            try {
                // Show loading indicator
                const detectLoading = document.getElementById('detectLoading');
                detectLoading.style.display = 'flex';

                // Get current crown and chin points
                const crownPoint = landmarks.crown_chin.crownPoint;
                const chinPoint = landmarks.crown_chin.chinPoint;

                // Update crown-chin points
                const response = await fetch('/api/landmarks/update-crown-chin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_id: currentImageId,
                        crown_point: crownPoint,
                        chin_point: chinPoint
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();

                // Update landmarks
                landmarks.crown_chin = data;

                // Get the aligned image with updated landmarks
                const previewImage = document.getElementById('previewImage');

                // Update the image source to use the aligned image
                previewImage.src = `/api/landmarks/aligned-image/${currentImageId}?t=${new Date().getTime()}`;

                // Wait for the image to load before drawing landmarks
                previewImage.onload = () => {
                    // Update image dimensions
                    imageWidth = previewImage.naturalWidth;
                    imageHeight = previewImage.naturalHeight;

                    // Draw landmarks on canvas
                    drawLandmarks(landmarks);

                    // Hide loading indicator
                    detectLoading.style.display = 'none';

                    // Show success message
                    const landmarkStatus = document.getElementById('landmarkStatus');
                    landmarkStatus.textContent = 'Points updated successfully!';
                    landmarkStatus.className = 'status-message status-success';
                    landmarkStatus.style.display = 'block';
                    setTimeout(() => {
                        landmarkStatus.style.display = 'none';
                    }, 3000);
                };

                // Handle image loading error
                previewImage.onerror = () => {
                    // Hide loading indicator
                    detectLoading.style.display = 'none';

                    // Show error message
                    const landmarkStatus = document.getElementById('landmarkStatus');
                    landmarkStatus.textContent = 'Error loading aligned image';
                    landmarkStatus.className = 'status-message status-error';
                    landmarkStatus.style.display = 'block';
                };

                // Show response
                document.getElementById('landmarksResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                // Hide loading indicator
                document.getElementById('detectLoading').style.display = 'none';

                // Show error message
                const landmarkStatus = document.getElementById('landmarkStatus');
                landmarkStatus.textContent = 'Error updating points: ' + error.message;
                landmarkStatus.className = 'status-message status-error';
                landmarkStatus.style.display = 'block';

                document.getElementById('landmarksResponse').textContent = 'Error: ' + error.message;
            }
        }

        // Update button now resets to the original detected points
        document.getElementById('updateBtn').addEventListener('click', async () => {
            if (!currentImageId) {
                const landmarkStatus = document.getElementById('landmarkStatus');
                landmarkStatus.textContent = 'Please upload an image and detect landmarks first';
                landmarkStatus.className = 'status-message status-error';
                landmarkStatus.style.display = 'block';
                setTimeout(() => {
                    landmarkStatus.style.display = 'none';
                }, 3000);
                return;
            }

            try {
                // Show loading indicator
                const detectLoading = document.getElementById('detectLoading');
                detectLoading.style.display = 'flex';

                // Re-detect landmarks
                const response = await fetch(`/api/landmarks/detect/${currentImageId}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();

                // Store landmarks
                landmarks = data.landmarks;

                if (landmarks) {
                    // Get the aligned image
                    const previewImage = document.getElementById('previewImage');

                    // Update the image source to use the aligned image
                    previewImage.src = `/api/landmarks/aligned-image/${currentImageId}?t=${new Date().getTime()}`;

                    // Wait for the image to load before drawing landmarks
                    previewImage.onload = () => {
                        // Update image dimensions
                        imageWidth = previewImage.naturalWidth;
                        imageHeight = previewImage.naturalHeight;

                        // Draw landmarks on canvas
                        drawLandmarks(landmarks);

                        // Hide loading indicator
                        detectLoading.style.display = 'none';

                        // Show success message
                        const landmarkStatus = document.getElementById('landmarkStatus');
                        landmarkStatus.textContent = 'Points reset successfully!';
                        landmarkStatus.className = 'status-message status-success';
                        landmarkStatus.style.display = 'block';
                        setTimeout(() => {
                            landmarkStatus.style.display = 'none';
                        }, 3000);
                    };

                    // Handle image loading error
                    previewImage.onerror = () => {
                        // Hide loading indicator
                        detectLoading.style.display = 'none';

                        // Show error message
                        const landmarkStatus = document.getElementById('landmarkStatus');
                        landmarkStatus.textContent = 'Error loading aligned image';
                        landmarkStatus.className = 'status-message status-error';
                        landmarkStatus.style.display = 'block';
                    };
                } else {
                    // Hide loading indicator
                    detectLoading.style.display = 'none';
                }

                // Show response
                document.getElementById('landmarksResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                // Hide loading indicator
                document.getElementById('detectLoading').style.display = 'none';

                // Show error message
                const landmarkStatus = document.getElementById('landmarkStatus');
                landmarkStatus.textContent = 'Error resetting points: ' + error.message;
                landmarkStatus.className = 'status-message status-error';
                landmarkStatus.style.display = 'block';

                document.getElementById('landmarksResponse').textContent = 'Error: ' + error.message;
            }
        });

        // Function to draw landmarks on canvas
        function drawLandmarks(landmarks) {
            // Clear canvas
            canvasContext.clearRect(0, 0, canvas.width, canvas.height);

            // Ensure canvas dimensions match the image
            const previewImage = document.getElementById('previewImage');
            const imageContainer = document.querySelector('.image-container');

            // Get the actual displayed dimensions of the image
            const displayedWidth = previewImage.clientWidth;
            const displayedHeight = previewImage.clientHeight;

            // Set canvas dimensions to match the displayed image size
            canvas.width = displayedWidth;
            canvas.height = displayedHeight;

            // Position canvas directly over the image with absolute positioning
            canvas.style.width = displayedWidth + 'px';
            canvas.style.height = displayedHeight + 'px';

            // Make sure the canvas position is reset to top-left of the image
            canvas.style.top = '0px';
            canvas.style.left = '0px';

            // Calculate scale factors based on current display size vs original image size
            const scaleX = displayedWidth / imageWidth;
            const scaleY = displayedHeight / imageHeight;

            // Draw face rectangle with responsive styling
            // Calculate line width based on canvas size
            const rectLineWidth = Math.max(2, Math.min(4, canvas.width / 300));

            // Don't draw the blue rectangle
            // canvasContext.strokeStyle = 'rgba(65, 105, 225, 0.7)';
            // canvasContext.lineWidth = rectLineWidth;
            // canvasContext.strokeRect(
            //     Math.round(landmarks.left * scaleX),
            //     Math.round(landmarks.top * scaleY),
            //     Math.round((landmarks.right - landmarks.left) * scaleX),
            //     Math.round((landmarks.bottom - landmarks.top) * scaleY)
            // );

            // Check if key_points are available
            if (landmarks.key_points) {
                // Draw only the key points (A-Q)
                drawKeyPoints(landmarks.key_points, scaleX, scaleY);
            } else {
                // Fall back to drawing all landmarks if key_points are not available
                // Connect facial landmarks with lines for better visualization
                drawConnectedPoints(landmarks.chin, 'rgba(220, 20, 60, 0.7)', scaleX, scaleY);
                drawConnectedPoints(landmarks.left_eyebrow, 'rgba(148, 0, 211, 0.7)', scaleX, scaleY);
                drawConnectedPoints(landmarks.right_eyebrow, 'rgba(148, 0, 211, 0.7)', scaleX, scaleY);
                drawConnectedPoints(landmarks.nose_bridge, 'rgba(30, 144, 255, 0.7)', scaleX, scaleY);
                drawConnectedPoints(landmarks.nose_tip, 'rgba(30, 144, 255, 0.7)', scaleX, scaleY);

                // Draw eyes as closed shapes
                drawClosedShape(landmarks.left_eye, 'rgba(46, 139, 87, 0.7)', scaleX, scaleY);
                drawClosedShape(landmarks.right_eye, 'rgba(46, 139, 87, 0.7)', scaleX, scaleY);

                // Draw lips as closed shapes
                drawClosedShape(landmarks.top_lip, 'rgba(255, 140, 0, 0.7)', scaleX, scaleY);
                drawClosedShape(landmarks.bottom_lip, 'rgba(255, 140, 0, 0.7)', scaleX, scaleY);
            }

            // Draw crown-chin points and line
            if (landmarks.crown_chin) {
                // Calculate point size based on image dimensions
                const pointSize = Math.max(8, Math.min(12, canvas.width / 80));

                // Draw crown point with improved visibility
                canvasContext.fillStyle = 'rgba(255, 215, 0, 0.9)';
                canvasContext.beginPath();
                canvasContext.arc(
                    Math.round(landmarks.crown_chin.crownPoint.x * scaleX),
                    Math.round(landmarks.crown_chin.crownPoint.y * scaleY),
                    pointSize, 0, 2 * Math.PI
                );
                canvasContext.fill();
                canvasContext.strokeStyle = 'white';
                canvasContext.lineWidth = 2;
                canvasContext.stroke();

                // Add outer glow for better visibility
                canvasContext.strokeStyle = 'rgba(0, 0, 0, 0.5)';
                canvasContext.lineWidth = 1;
                canvasContext.beginPath();
                canvasContext.arc(
                    Math.round(landmarks.crown_chin.crownPoint.x * scaleX),
                    Math.round(landmarks.crown_chin.crownPoint.y * scaleY),
                    pointSize + 2, 0, 2 * Math.PI
                );
                canvasContext.stroke();

                // Draw chin point with improved visibility
                canvasContext.fillStyle = 'rgba(255, 215, 0, 0.9)';
                canvasContext.beginPath();
                canvasContext.arc(
                    Math.round(landmarks.crown_chin.chinPoint.x * scaleX),
                    Math.round(landmarks.crown_chin.chinPoint.y * scaleY),
                    pointSize, 0, 2 * Math.PI
                );
                canvasContext.fill();
                canvasContext.strokeStyle = 'white';
                canvasContext.lineWidth = 2;
                canvasContext.stroke();

                // Add outer glow for better visibility
                canvasContext.strokeStyle = 'rgba(0, 0, 0, 0.5)';
                canvasContext.lineWidth = 1;
                canvasContext.beginPath();
                canvasContext.arc(
                    Math.round(landmarks.crown_chin.chinPoint.x * scaleX),
                    Math.round(landmarks.crown_chin.chinPoint.y * scaleY),
                    pointSize + 2, 0, 2 * Math.PI
                );
                canvasContext.stroke();

                // Draw line between crown and chin with gradient
                const crownX = Math.round(landmarks.crown_chin.crownPoint.x * scaleX);
                const crownY = Math.round(landmarks.crown_chin.crownPoint.y * scaleY);
                const chinX = Math.round(landmarks.crown_chin.chinPoint.x * scaleX);
                const chinY = Math.round(landmarks.crown_chin.chinPoint.y * scaleY);

                // Create gradient with rounded coordinates
                const gradient = canvasContext.createLinearGradient(
                    crownX, crownY, chinX, chinY
                );
                gradient.addColorStop(0, 'rgba(255, 215, 0, 0.9)');
                gradient.addColorStop(1, 'rgba(255, 215, 0, 0.9)');

                // Calculate line width based on canvas size
                const lineWidth = Math.max(2, Math.min(4, canvas.width / 300));
                const dashSize = Math.max(4, Math.min(8, canvas.width / 200));

                // Draw the connecting line with responsive width and dash
                canvasContext.strokeStyle = gradient;
                canvasContext.lineWidth = lineWidth;
                canvasContext.beginPath();
                canvasContext.setLineDash([dashSize, dashSize]);
                canvasContext.moveTo(crownX, crownY);
                canvasContext.lineTo(chinX, chinY);
                canvasContext.stroke();
                canvasContext.setLineDash([]);

                // Add labels for crown and chin points with responsive font size
                const fontSize = Math.max(12, Math.min(18, canvas.width / 60));
                canvasContext.font = `${fontSize}px Arial`;
                canvasContext.textAlign = 'center';

                // Draw text shadow for better visibility
                canvasContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
                canvasContext.fillText(
                    'Crown',
                    Math.round(landmarks.crown_chin.crownPoint.x * scaleX) + 1,
                    Math.round(landmarks.crown_chin.crownPoint.y * scaleY) - (pointSize + 8) + 1
                );

                // Draw main text
                canvasContext.fillStyle = 'white';
                canvasContext.fillText(
                    'Crown',
                    Math.round(landmarks.crown_chin.crownPoint.x * scaleX),
                    Math.round(landmarks.crown_chin.crownPoint.y * scaleY) - (pointSize + 8)
                );

                // Draw text shadow for chin label
                canvasContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
                canvasContext.fillText(
                    'Chin',
                    Math.round(landmarks.crown_chin.chinPoint.x * scaleX) + 1,
                    Math.round(landmarks.crown_chin.chinPoint.y * scaleY) + (pointSize + 15) + 1
                );

                // Draw main text for chin label
                canvasContext.fillStyle = 'white';
                canvasContext.fillText(
                    'Chin',
                    Math.round(landmarks.crown_chin.chinPoint.x * scaleX),
                    Math.round(landmarks.crown_chin.chinPoint.y * scaleY) + (pointSize + 15)
                );

                // Calculate the distance using original coordinates (not scaled)
                const origCrownX = landmarks.crown_chin.crownPoint.x;
                const origCrownY = landmarks.crown_chin.crownPoint.y;
                const origChinX = landmarks.crown_chin.chinPoint.x;
                const origChinY = landmarks.crown_chin.chinPoint.y;

                // Calculate pixel distance in original image coordinates
                const distance = Math.sqrt(
                    Math.pow(origChinX - origCrownX, 2) +
                    Math.pow(origChinY - origCrownY, 2)
                );

                // Display the distance in the middle of the line using already scaled coordinates
                const midX = Math.round((crownX + chinX) / 2);
                const midY = Math.round((crownY + chinY) / 2);

                // Create a background for the text with responsive size
                const distanceText = `${Math.round(distance)}px`;

                // Use the same font size as the labels for consistency
                canvasContext.font = `${fontSize}px Arial`;
                const textWidth = canvasContext.measureText(distanceText).width;
                const padding = Math.max(5, Math.min(8, canvas.width / 200));

                // Draw a semi-transparent background with rounded corners
                canvasContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
                canvasContext.beginPath();
                canvasContext.roundRect(
                    midX - textWidth/2 - padding,
                    midY - fontSize/2 - padding,
                    textWidth + padding*2,
                    fontSize + padding*2,
                    5
                );
                canvasContext.fill();

                // Draw text shadow
                canvasContext.fillStyle = 'rgba(0, 0, 0, 0.5)';
                canvasContext.fillText(
                    distanceText,
                    midX + 1,
                    midY + fontSize/3 + 1
                );

                // Draw main text
                canvasContext.fillStyle = 'white';
                canvasContext.fillText(
                    distanceText,
                    midX,
                    midY + fontSize/3
                );
            }
        }

        // Function to draw points connected by lines
        function drawConnectedPoints(points, color, scaleX, scaleY) {
            if (!points || points.length < 2) return;

            // Calculate line width and point size based on canvas dimensions
            const lineWidth = Math.max(1, Math.min(3, canvas.width / 400));
            const pointSize = Math.max(2, Math.min(4, canvas.width / 300));

            // Draw lines connecting points
            canvasContext.strokeStyle = color;
            canvasContext.lineWidth = lineWidth;
            canvasContext.beginPath();
            canvasContext.moveTo(
                Math.round(points[0].x * scaleX),
                Math.round(points[0].y * scaleY)
            );

            for (let i = 1; i < points.length; i++) {
                canvasContext.lineTo(
                    Math.round(points[i].x * scaleX),
                    Math.round(points[i].y * scaleY)
                );
            }

            canvasContext.stroke();

            // Draw points on top of lines
            canvasContext.fillStyle = color;
            for (const point of points) {
                canvasContext.beginPath();
                canvasContext.arc(
                    Math.round(point.x * scaleX),
                    Math.round(point.y * scaleY),
                    pointSize, 0, 2 * Math.PI
                );
                canvasContext.fill();
            }
        }

        // Function to draw key points (A-Q)
        function drawKeyPoints(keyPoints, scaleX, scaleY) {
            // Define colors for different point types
            const eyeColor = 'rgba(255, 165, 0, 0.9)';      // Orange
            const mouthColor = 'rgba(255, 0, 255, 0.9)';    // Magenta
            const centerColor = 'rgba(0, 255, 255, 0.9)';   // Cyan
            const crownColor = 'rgba(255, 215, 0, 0.9)';    // Gold (changed from blue)
            const chinColor = 'rgba(255, 215, 0, 0.9)';     // Gold (changed from red)

            // Calculate point size based on image dimensions
            const pointSize = Math.max(6, Math.min(10, canvas.width / 100));
            const lineWidth = Math.max(2, Math.min(4, canvas.width / 300));
            const fontSize = Math.max(12, Math.min(16, canvas.width / 70));

            // Function to draw a labeled point
            function drawLabeledPoint(point, label, color) {
                if (!point) return;

                const x = Math.round(point.x * scaleX);
                const y = Math.round(point.y * scaleY);

                // Draw point
                canvasContext.fillStyle = color;
                canvasContext.beginPath();
                canvasContext.arc(x, y, pointSize, 0, 2 * Math.PI);
                canvasContext.fill();

                // Add white border
                canvasContext.strokeStyle = 'white';
                canvasContext.lineWidth = 2;
                canvasContext.stroke();

                // Add outer glow
                canvasContext.strokeStyle = 'rgba(0, 0, 0, 0.5)';
                canvasContext.lineWidth = 1;
                canvasContext.beginPath();
                canvasContext.arc(x, y, pointSize + 2, 0, 2 * Math.PI);
                canvasContext.stroke();

                // Add label
                canvasContext.font = `${fontSize}px Arial`;
                canvasContext.textAlign = 'center';

                // Draw text shadow
                canvasContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
                canvasContext.fillText(label, x + 1, y - (pointSize + 5) + 1);

                // Draw main text
                canvasContext.fillStyle = 'white';
                canvasContext.fillText(label, x, y - (pointSize + 5));

                return { x, y };
            }

            // Draw eye points (A, B)
            const ptA = drawLabeledPoint(keyPoints.A, 'A', eyeColor);
            const ptB = drawLabeledPoint(keyPoints.B, 'B', eyeColor);

            // Draw mouth points (C, D)
            const ptC = drawLabeledPoint(keyPoints.C, 'C', mouthColor);
            const ptD = drawLabeledPoint(keyPoints.D, 'D', mouthColor);

            // Draw center line points (P, M, N, Q)
            const ptP = drawLabeledPoint(keyPoints.P, 'P', crownColor);
            const ptM = drawLabeledPoint(keyPoints.M, 'M', centerColor);
            const ptN = drawLabeledPoint(keyPoints.N, 'N', centerColor);
            const ptQ = drawLabeledPoint(keyPoints.Q, 'Q', chinColor);

            // Draw horizontal line connecting A and B (eye pupils)
            if (ptA && ptB) {
                canvasContext.strokeStyle = eyeColor;
                canvasContext.lineWidth = lineWidth;
                canvasContext.beginPath();
                canvasContext.moveTo(ptA.x, ptA.y);
                canvasContext.lineTo(ptB.x, ptB.y);
                canvasContext.stroke();
            }

            // Draw horizontal line connecting C and D (mouth corners)
            if (ptC && ptD) {
                canvasContext.strokeStyle = mouthColor;
                canvasContext.lineWidth = lineWidth;
                canvasContext.beginPath();
                canvasContext.moveTo(ptC.x, ptC.y);
                canvasContext.lineTo(ptD.x, ptD.y);
                canvasContext.stroke();
            }

            // Draw vertical line connecting P and Q (crown and chin)
            if (ptP && ptQ) {
                // Create gradient (now using the same gold color for both ends)
                const gradient = canvasContext.createLinearGradient(ptP.x, ptP.y, ptQ.x, ptQ.y);
                gradient.addColorStop(0, 'rgba(255, 215, 0, 0.9)');  // Gold
                gradient.addColorStop(1, 'rgba(255, 215, 0, 0.9)');  // Gold

                // Draw dashed line
                const dashSize = Math.max(4, Math.min(8, canvas.width / 200));
                canvasContext.strokeStyle = gradient;
                canvasContext.lineWidth = lineWidth;
                canvasContext.beginPath();
                canvasContext.setLineDash([dashSize, dashSize]);
                canvasContext.moveTo(ptP.x, ptP.y);
                canvasContext.lineTo(ptQ.x, ptQ.y);
                canvasContext.stroke();
                canvasContext.setLineDash([]);

                // Calculate and display the distance
                if (keyPoints.P && keyPoints.Q) {
                    const distance = Math.sqrt(
                        Math.pow(keyPoints.Q.x - keyPoints.P.x, 2) +
                        Math.pow(keyPoints.Q.y - keyPoints.P.y, 2)
                    );

                    const midX = Math.round((ptP.x + ptQ.x) / 2);
                    const midY = Math.round((ptP.y + ptQ.y) / 2);

                    // Create a background for the text
                    const distanceText = `${Math.round(distance)}px`;
                    canvasContext.font = `${fontSize}px Arial`;
                    const textWidth = canvasContext.measureText(distanceText).width;
                    const padding = Math.max(5, Math.min(8, canvas.width / 200));

                    // Draw background
                    canvasContext.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    canvasContext.beginPath();
                    canvasContext.roundRect(
                        midX - textWidth/2 - padding,
                        midY - fontSize/2 - padding,
                        textWidth + padding*2,
                        fontSize + padding*2,
                        5
                    );
                    canvasContext.fill();

                    // Draw text
                    canvasContext.fillStyle = 'white';
                    canvasContext.textAlign = 'center';
                    canvasContext.fillText(distanceText, midX, midY + fontSize/3);
                }
            }
        }

        // Function to draw closed shapes (like eyes and lips)
        function drawClosedShape(points, color, scaleX, scaleY) {
            if (!points || points.length < 3) return;

            // Calculate line width based on canvas dimensions
            const lineWidth = Math.max(1, Math.min(3, canvas.width / 400));

            // Draw filled shape
            canvasContext.fillStyle = color;
            canvasContext.beginPath();
            canvasContext.moveTo(
                Math.round(points[0].x * scaleX),
                Math.round(points[0].y * scaleY)
            );

            for (let i = 1; i < points.length; i++) {
                canvasContext.lineTo(
                    Math.round(points[i].x * scaleX),
                    Math.round(points[i].y * scaleY)
                );
            }

            // Close the shape
            canvasContext.closePath();
            canvasContext.fill();

            // Draw outline with responsive line width
            canvasContext.strokeStyle = color;
            canvasContext.lineWidth = lineWidth;
            canvasContext.stroke();
        }

        // Add window resize event listener to redraw landmarks when window is resized
        window.addEventListener('resize', handleWindowResize);

        // Add zoom event listeners for different browsers
        window.addEventListener('wheel', handleZoom, { passive: false });
        window.addEventListener('gestureend', handleZoom);

        // Add specific event listener for browser zoom (Ctrl+wheel)
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && landmarks) {
                // Listen for Ctrl key being pressed (common for zoom)
                setTimeout(handleWindowResize, 100);
            }
        });

        // Add MutationObserver to detect changes in image size (which can happen during zoom)
        const imageContainer = document.querySelector('.image-container');
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                if (landmarks) {
                    handleWindowResize();
                }
            }
        });
        resizeObserver.observe(imageContainer);

        function handleWindowResize() {
            if (landmarks) {
                // Force redraw with a small delay to allow browser to complete resize/zoom
                setTimeout(() => {
                    // Get the current image element
                    const previewImage = document.getElementById('previewImage');

                    // Force a complete redraw of the landmarks
                    drawLandmarks(landmarks);

                    // Log dimensions for debugging
                    console.log('Window resize/zoom detected');
                    console.log('Image natural dimensions:', imageWidth, 'x', imageHeight);
                    console.log('Image displayed dimensions:', previewImage.clientWidth, 'x', previewImage.clientHeight);
                    console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);
                }, 100);
            }
        }

        function handleZoom(event) {
            // Prevent default zoom behavior if it's a wheel event
            if (event.type === 'wheel' && event.ctrlKey) {
                event.preventDefault();
            }

            // Redraw landmarks after zoom
            if (landmarks) {
                setTimeout(() => {
                    handleWindowResize();
                }, 100);
            }
        }

        // Crop Box Functionality
        class CropBoxApp {
            constructor() {
                // DOM elements
                this.canvas = document.getElementById('cropCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.overlay = document.getElementById('cropOverlay');
                this.viewport = document.getElementById('cropViewport');
                this.standardSelector = document.getElementById('standard-selector');
                this.cropBtn = document.getElementById('cropBtn');
                this.resetViewBtn = document.getElementById('resetViewBtn');
                this.saveBtn = document.getElementById('saveBtn');
                this.backBtn = document.getElementById('backBtn');
                this.resultContainer = document.getElementById('result-container');
                this.croppedImage = document.getElementById('cropped-image');
                this.cropSpinner = document.getElementById('cropSpinner');
                this.cropStatus = document.getElementById('cropStatus');
                this.cropLoading = document.getElementById('cropLoading');

                // Landmark elements
                this.crownMark = document.getElementById('crown-mark');
                this.chinMark = document.getElementById('chin-mark');
                this.crownText = document.getElementById('crown-text');
                this.chinText = document.getElementById('chin-text');
                this.middleLine = document.getElementById('middle-line');
                this.crownLine = document.getElementById('crown-line');
                this.chinLine = document.getElementById('chin-line');
                this.cropRect = document.getElementById('crop-rect');
                this.faceEllipse = document.getElementById('face-ellipse');
                this.faceHeightText = document.getElementById('face-height-text');
                this.rotationText = document.getElementById('rotation-text');
                this.warningText = document.getElementById('warning-text');
                this.photoSizeMeasurement = document.getElementById('photo-size-measurement');

                // State variables
                this.image = null;
                this.imageWidth = 0;
                this.imageHeight = 0;
                this.viewportWidth = 0;
                this.viewportHeight = 0;
                this.xLeft = 0;
                this.yTop = 0;
                this.zoom = 1;
                this.ratio = 0;
                this.crownPoint = new Point(0, 0);
                this.chinPoint = new Point(0, 0);
                this.isDragging = false;
                this.dragTarget = null;
                this.dragStartX = 0;
                this.dragStartY = 0;
                this.photoDimensions = null;
                this.standardId = null;
                this.croppedImageData = null;

                // Default photo dimensions (US Passport)
                this.defaultPhotoDimensions = new PhotoDimensions(
                    2, // width in inches
                    2, // height in inches
                    'inch',
                    1.38, // face height in inches
                    0.5, // crown top in inches
                    300 // dpi
                );

                // Initialize
                this.initEventListeners();
                this.loadPhotoStandards();
                this.resizeCanvas();
            }

            initEventListeners() {
                // Button event listeners
                this.cropBtn.addEventListener('click', () => this.cropImage());
                this.resetViewBtn.addEventListener('click', () => this.resetView());
                this.saveBtn.addEventListener('click', () => this.saveImage());
                this.backBtn.addEventListener('click', () => {
                    this.resultContainer.style.display = 'none';
                    this.cropBtn.disabled = false;
                });
                this.standardSelector.addEventListener('change', (e) => this.handleStandardChange(e));

                // Window resize event
                window.addEventListener('resize', () => this.resizeCanvas());

                // Mouse wheel for zooming
                this.viewport.addEventListener('wheel', (e) => this.handleMouseWheel(e));

                // Mouse events for dragging landmarks
                this.overlay.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                document.addEventListener('mouseup', () => this.handleMouseUp());

                // Prevent context menu
                this.viewport.addEventListener('contextmenu', (e) => e.preventDefault());

                // Connect to landmark detection
                document.getElementById('detectBtn').addEventListener('click', () => {
                    // When landmarks are detected, update the crop box
                    setTimeout(() => {
                        if (landmarks && landmarks.crown_chin) {
                            this.updateFromLandmarks();
                        }
                    }, 1000);
                });
            }

            loadPhotoStandards() {
                // Fetch photo standards from the API
                fetch('/api/photostandards')
                    .then(response => response.json())
                    .then(data => {
                        if (data.standards && data.standards.length > 0) {
                            // Clear existing options
                            this.standardSelector.innerHTML = '<option value="">Select a document type</option>';

                            // Add options for each standard
                            data.standards.forEach(standard => {
                                const option = document.createElement('option');
                                option.value = standard.id;
                                option.textContent = standard.text;
                                this.standardSelector.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading photo standards:', error);
                        this.showStatus('Error loading photo standards: ' + error.message, 'error');
                    });
            }

            showStatus(message, type = 'info') {
                this.cropStatus.textContent = message;
                this.cropStatus.className = 'status-message status-' + type;
                this.cropStatus.style.display = 'block';
                setTimeout(() => {
                    this.cropStatus.style.display = 'none';
                }, 3000);
            }

            resizeCanvas() {
                const container = this.canvas.parentElement;
                this.viewportWidth = container.clientWidth;
                this.viewportHeight = container.clientHeight;
                this.canvas.width = this.viewportWidth;
                this.canvas.height = this.viewportHeight;

                if (this.image) {
                    this.renderImage();
                    this.renderLandmarks();
                }
            }

            handleStandardChange(e) {
                const standardId = e.target.value;
                if (!standardId) {
                    this.standardId = null;
                    this.photoDimensions = this.defaultPhotoDimensions;
                    return;
                }

                this.standardId = standardId;
                this.cropSpinner.style.display = 'block';

                // Fetch the standard details
                fetch(`/api/photostandards/${standardId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.standard) {
                            const standard = data.standard;
                            // Create a PhotoDimensions object from the standard
                            this.photoDimensions = new PhotoDimensions(
                                standard.dimensions.pictureWidth,
                                standard.dimensions.pictureHeight,
                                standard.dimensions.units,
                                standard.dimensions.faceHeight,
                                standard.dimensions.crownTop || 0,
                                standard.dimensions.dpi,
                                standard.dimensions.bottomEyeLine || 0
                            );

                            // Update the crop rectangle
                            this.renderLandmarks();
                            this.cropSpinner.style.display = 'none';
                            this.cropBtn.disabled = false;
                        } else {
                            console.error('Invalid standard data format:', data);
                            this.cropSpinner.style.display = 'none';
                            this.showStatus('Error: Invalid standard data format', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading photo standard:', error);
                        this.cropSpinner.style.display = 'none';
                        this.showStatus('Error loading photo standard: ' + error.message, 'error');
                    });
            }

            updateFromLandmarks() {
                if (!landmarks || !landmarks.crown_chin || !currentImageId) return;

                // Load the image if not already loaded
                if (!this.image) {
                    this.image = document.getElementById('previewImage');
                    this.imageWidth = this.image.naturalWidth;
                    this.imageHeight = this.image.naturalHeight;
                    this.zoomFit();
                }

                // Set the crown and chin points
                this.crownPoint = new Point(
                    landmarks.crown_chin.crownPoint.x,
                    landmarks.crown_chin.crownPoint.y
                );
                this.chinPoint = new Point(
                    landmarks.crown_chin.chinPoint.x,
                    landmarks.crown_chin.chinPoint.y
                );

                // Render the image and landmarks
                this.renderImage();
                this.renderLandmarks();
            }

            zoomFit() {
                this.zoom = 1;
                this.ratio = this.computeMinScale(this.imageWidth, this.imageHeight);
                this.xLeft = this.viewportWidth / 2 - (this.ratio * this.imageWidth) / 2;
                this.yTop = this.viewportHeight / 2 - (this.ratio * this.imageHeight) / 2;
            }

            computeMinScale(width, height) {
                const xRatio = this.viewportWidth / width;
                const yRatio = this.viewportHeight / height;
                return Math.min(xRatio, yRatio) * 0.9; // 90% to leave some margin
            }

            renderImage() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                if (!this.image) {
                    return;
                }

                const scaledWidth = this.imageWidth * this.ratio * this.zoom;
                const scaledHeight = this.imageHeight * this.ratio * this.zoom;

                try {
                    this.ctx.drawImage(this.image, this.xLeft, this.yTop, scaledWidth, scaledHeight);
                } catch (e) {
                    console.error(`Error rendering image: ${e}`);
                }
            }

            renderLandmarks() {
                if (!this.image || !this.crownPoint || !this.chinPoint) return;

                // Convert image coordinates to screen coordinates
                const crownScreen = this.pixelToScreen(this.crownPoint);
                const chinScreen = this.pixelToScreen(this.chinPoint);

                // Position landmarks
                this.positionLandmark(this.crownMark, crownScreen);
                this.positionLandmark(this.chinMark, chinScreen);

                // Position text labels
                this.crownText.setAttribute('x', crownScreen.x);
                this.crownText.setAttribute('y', crownScreen.y - 20);
                this.chinText.setAttribute('x', chinScreen.x);
                this.chinText.setAttribute('y', chinScreen.y + 30);

                // Make landmarks visible
                this.crownMark.style.visibility = 'visible';
                this.chinMark.style.visibility = 'visible';
                this.crownText.style.visibility = 'visible';
                this.chinText.style.visibility = 'visible';

                // Draw middle line
                const middle = middlePoint(this.crownPoint, this.chinPoint);
                const middleScreen = this.pixelToScreen(middle);
                this.middleLine.setAttribute('x1', crownScreen.x);
                this.middleLine.setAttribute('y1', crownScreen.y);
                this.middleLine.setAttribute('x2', chinScreen.x);
                this.middleLine.setAttribute('y2', chinScreen.y);

                // Calculate angle for perpendicular lines
                const angle = Math.atan2(chinScreen.y - crownScreen.y, chinScreen.x - crownScreen.x);
                const perpAngle = angle - Math.PI / 2;
                const lineLength = crownScreen.distTo(chinScreen) * 0.3; // 30% of face height

                // Draw crown perpendicular line
                const crownPerpX1 = crownScreen.x - Math.cos(perpAngle) * lineLength;
                const crownPerpY1 = crownScreen.y - Math.sin(perpAngle) * lineLength;
                const crownPerpX2 = crownScreen.x + Math.cos(perpAngle) * lineLength;
                const crownPerpY2 = crownScreen.y + Math.sin(perpAngle) * lineLength;

                this.crownLine.setAttribute('x1', crownPerpX1);
                this.crownLine.setAttribute('y1', crownPerpY1);
                this.crownLine.setAttribute('x2', crownPerpX2);
                this.crownLine.setAttribute('y2', crownPerpY2);

                // Draw chin perpendicular line
                const chinPerpX1 = chinScreen.x - Math.cos(perpAngle) * lineLength;
                const chinPerpY1 = chinScreen.y - Math.sin(perpAngle) * lineLength;
                const chinPerpX2 = chinScreen.x + Math.cos(perpAngle) * lineLength;
                const chinPerpY2 = chinScreen.y + Math.sin(perpAngle) * lineLength;

                this.chinLine.setAttribute('x1', chinPerpX1);
                this.chinLine.setAttribute('y1', chinPerpY1);
                this.chinLine.setAttribute('x2', chinPerpX2);
                this.chinLine.setAttribute('y2', chinPerpY2);

                // Calculate face height and angle in degrees
                const faceHeight = crownScreen.distTo(chinScreen);
                const angleDeg = (angle * 180) / Math.PI;

                // Display rotation angle
                // Adjust angle to show rotation from vertical (90 degrees offset)
                const rotationAngle = Math.round(90 - angleDeg);
                this.rotationText.textContent = `Rotation: ${rotationAngle}°`;
                this.rotationText.setAttribute('x', crownScreen.x - 100);
                this.rotationText.setAttribute('y', crownScreen.y);
                this.rotationText.style.visibility = 'visible';

                // Draw face ellipse
                const ra = faceHeight / 2;  // Major axis radius (half of face height)
                const rb = 0.68 * ra;       // Minor axis radius (approximation for face width)
                const pc = middleScreen;    // Center point of the ellipse

                this.faceEllipse.setAttribute('rx', ra);
                this.faceEllipse.setAttribute('ry', rb);
                this.faceEllipse.setAttribute('cx', pc.x);
                this.faceEllipse.setAttribute('cy', pc.y);
                this.faceEllipse.setAttribute('transform', `rotate(${angleDeg}, ${pc.x}, ${pc.y})`);

                // Make face ellipse visible
                this.faceEllipse.style.visibility = 'visible';

                if (this.photoDimensions) {
                    // Calculate and display face height percentage
                    const p = this.photoDimensions;
                    const faceHeightPercentage = (p.faceHeight / p.pictureHeight * 100).toFixed(1);
                    this.faceHeightText.textContent = `Face: ${faceHeightPercentage}%`;
                    this.faceHeightText.setAttribute('x', pc.x + 10);
                    this.faceHeightText.setAttribute('y', pc.y);
                    this.faceHeightText.style.visibility = 'visible';

                    // Check if proportions meet standards and show warning if needed
                    const proportionsOk = this.checkProportions();
                    if (!proportionsOk) {
                        this.warningText.style.visibility = 'visible';
                        this.cropRect.setAttribute('stroke', 'red');
                    } else {
                        this.warningText.style.visibility = 'hidden';
                        this.cropRect.setAttribute('stroke', 'lightgray');
                    }

                    // Update photo size measurement
                    this.photoSizeMeasurement.textContent = `Photo Size: ${p.pictureWidth.toFixed(1)}x${p.pictureHeight.toFixed(1)}${p.units}`;
                    this.photoSizeMeasurement.style.visibility = 'visible';

                    // Draw crop rectangle
                    this.renderCropRect();
                }
            }

            renderCropRect() {
                if (!this.photoDimensions || !this.crownPoint || !this.chinPoint || this.crownPoint.equals(this.chinPoint)) return;

                const p = this.photoDimensions;
                const rotRect = getCroppingRectangle(p, this.crownPoint, this.chinPoint);
                const corners = rotRect.corners();

                // Convert corners to screen coordinates
                const screenCorners = corners.map(corner => this.pixelToScreen(corner));

                // Create path for crop rectangle
                let pathData = `M ${screenCorners[0].x} ${screenCorners[0].y}`;
                for (let i = 1; i < screenCorners.length; i++) {
                    pathData += ` L ${screenCorners[i].x} ${screenCorners[i].y}`;
                }
                pathData += ' Z';

                // Update crop rectangle
                this.cropRect.setAttribute('d', pathData);
            }

            positionLandmark(element, point) {
                element.setAttribute('cx', point.x);
                element.setAttribute('cy', point.y);
            }

            pixelToScreen(point) {
                return new Point(
                    this.xLeft + point.x * this.ratio * this.zoom,
                    this.yTop + point.y * this.ratio * this.zoom
                );
            }

            screenToPixel(point) {
                return new Point(
                    (point.x - this.xLeft) / (this.ratio * this.zoom),
                    (point.y - this.yTop) / (this.ratio * this.zoom)
                );
            }

            handleMouseWheel(e) {
                e.preventDefault();

                if (!this.image) return;

                const rect = this.viewport.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                const scale = e.deltaY < 0 ? 1.1 : 1 / 1.1;
                let newZoom = this.zoom * scale;

                // Limit zoom
                if (newZoom < 0.1) newZoom = 0.1;
                if (newZoom > 10) newZoom = 10;

                const mousePoint = this.screenToPixel(new Point(mouseX, mouseY));
                const effectiveScale = this.ratio * (this.zoom - newZoom);

                this.yTop += mousePoint.y * effectiveScale;
                this.xLeft += mousePoint.x * effectiveScale;
                this.zoom = newZoom;

                this.renderImage();
                this.renderLandmarks();
            }

            handleMouseDown(e) {
                const rect = this.viewport.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                // Check if clicking on a landmark
                const crownScreen = this.pixelToScreen(this.crownPoint);
                const chinScreen = this.pixelToScreen(this.chinPoint);

                const crownDist = Math.sqrt(Math.pow(mouseX - crownScreen.x, 2) + Math.pow(mouseY - crownScreen.y, 2));
                const chinDist = Math.sqrt(Math.pow(mouseX - chinScreen.x, 2) + Math.pow(mouseY - chinScreen.y, 2));

                const landmarkRadius = 12; // Same as in CSS

                if (crownDist <= landmarkRadius) {
                    this.isDragging = true;
                    this.dragTarget = 'crown';
                    // Change cursor to grabbing
                    this.crownMark.style.cursor = 'grabbing';
                } else if (chinDist <= landmarkRadius) {
                    this.isDragging = true;
                    this.dragTarget = 'chin';
                    // Change cursor to grabbing
                    this.chinMark.style.cursor = 'grabbing';
                } else {
                    // Start panning
                    this.isDragging = true;
                    this.dragTarget = 'canvas';
                    this.dragStartX = mouseX;
                    this.dragStartY = mouseY;
                    // Change cursor to grabbing
                    this.viewport.style.cursor = 'grabbing';
                }
            }

            handleMouseMove(e) {
                if (!this.isDragging) return;

                const rect = this.viewport.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                if (this.dragTarget === 'crown') {
                    this.crownPoint = this.screenToPixel(new Point(mouseX, mouseY));
                    this.renderLandmarks();
                } else if (this.dragTarget === 'chin') {
                    this.chinPoint = this.screenToPixel(new Point(mouseX, mouseY));
                    this.renderLandmarks();
                } else if (this.dragTarget === 'canvas') {
                    // Pan the canvas
                    this.xLeft += mouseX - this.dragStartX;
                    this.yTop += mouseY - this.dragStartY;
                    this.dragStartX = mouseX;
                    this.dragStartY = mouseY;
                    this.renderImage();
                    this.renderLandmarks();
                }
            }

            handleMouseUp() {
                // Reset cursors
                this.crownMark.style.cursor = 'grab';
                this.chinMark.style.cursor = 'grab';
                this.viewport.style.cursor = 'default';

                this.isDragging = false;
                this.dragTarget = null;
            }

            checkProportions() {
                if (!this.photoDimensions || !this.crownPoint || !this.chinPoint) return true;

                const p = this.photoDimensions;
                const faceHeightPix = this.crownPoint.distTo(this.chinPoint);
                const mmToPixRatio = faceHeightPix / p.faceHeight;

                // Calculate the actual face height percentage
                const actualFaceHeightPercentage = (p.faceHeight / p.pictureHeight) * 100;

                // Define acceptable range (±5% of the standard's face height percentage)
                const minAcceptable = actualFaceHeightPercentage - 5;
                const maxAcceptable = actualFaceHeightPercentage + 5;

                // Calculate the current face height percentage based on the crop box
                const cropHeightPix = p.pictureHeight * mmToPixRatio;
                const currentFaceHeightPercentage = (faceHeightPix / cropHeightPix) * 100;

                // Check if the crop box is outside the image boundaries
                const rotRect = getCroppingRectangle(p, this.crownPoint, this.chinPoint);
                const corners = rotRect.corners();

                // Check if any corner is outside the image
                const isOutsideBounds = corners.some(corner => {
                    return (
                        corner.x < 0 ||
                        corner.y < 0 ||
                        corner.x > this.imageWidth ||
                        corner.y > this.imageHeight
                    );
                });

                if (isOutsideBounds) {
                    // Update warning text to indicate the crop box is outside the image
                    this.warningText.textContent = "Warning: Crop box is outside the image boundaries";
                    // Show the warning message in the alert box
                    document.getElementById('crop-warning').style.display = 'block';
                    return false;
                } else {
                    // Reset warning text to the default message
                    this.warningText.textContent = "Warning: Face proportion does not meet standard requirements";
                    // Hide the warning message in the alert box
                    document.getElementById('crop-warning').style.display = 'none';
                }

                // Check if the current percentage is within the acceptable range
                return currentFaceHeightPercentage >= minAcceptable && currentFaceHeightPercentage <= maxAcceptable;
            }

            resetView() {
                if (!this.image) return;

                this.zoomFit();
                this.renderImage();
                this.renderLandmarks();
                this.resultContainer.style.display = 'none';
                this.cropBtn.disabled = false;
            }

            cropImage() {
                if (!this.image || !this.crownPoint || !this.chinPoint || this.crownPoint.equals(this.chinPoint)) {
                    this.showStatus('Please upload an image and detect landmarks first', 'error');
                    return;
                }

                if (!this.standardId) {
                    this.showStatus('Please select a document type first', 'error');
                    return;
                }

                // Show loading indicators
                this.cropLoading.style.display = 'flex';
                this.previewLoading = document.querySelector('.preview-loading');
                this.previewError = document.querySelector('.preview-error');

                if (this.previewLoading) {
                    this.previewLoading.style.display = 'flex';
                }
                if (this.previewError) {
                    this.previewError.style.display = 'none';
                }
                if (this.croppedImage) {
                    this.croppedImage.style.display = 'none';
                }

                // Prepare request data
                const requestData = {
                    image_id: currentImageId,
                    standard_id: this.standardId,
                    crown_point: {
                        x: this.crownPoint.x,
                        y: this.crownPoint.y
                    },
                    chin_point: {
                        x: this.chinPoint.x,
                        y: this.chinPoint.y
                    }
                };

                // Get the JPEG preview with compliance markers
                fetch('/api/photo/preview-photo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Error generating preview');
                        });
                    }
                    return response.blob();
                })
                .then(imageBlob => {
                    // Clear previous content
                    if (this.resultContainer) {
                        this.resultContainer.innerHTML = '';
                        this.resultContainer.style.display = 'block';
                    }

                    // Create a heading for the preview section
                    const previewHeading = document.createElement('h3');
                    previewHeading.textContent = 'Photo Preview with Compliance Markers';
                    this.resultContainer.appendChild(previewHeading);

                    // Create an image element for the preview
                    const previewImage = document.createElement('img');
                    previewImage.className = 'preview-image';
                    previewImage.style.maxWidth = '100%';
                    previewImage.src = URL.createObjectURL(imageBlob);

                    // Create a container for the preview
                    const previewContainer = document.createElement('div');
                    previewContainer.className = 'preview-container';
                    previewContainer.appendChild(previewImage);

                    // Add the preview to the result container
                    this.resultContainer.appendChild(previewContainer);

                    // Add buttons for saving or going back
                    const buttonContainer = document.createElement('div');
                    buttonContainer.className = 'button-group';
                    buttonContainer.style.marginTop = '15px';
                    buttonContainer.style.display = 'flex';
                    buttonContainer.style.gap = '10px';
                    buttonContainer.style.justifyContent = 'center';

                    const saveButton = document.createElement('button');
                    saveButton.textContent = 'Save Image';
                    saveButton.className = 'button';
                    saveButton.onclick = () => this.saveImage();

                    const backButton = document.createElement('button');
                    backButton.textContent = 'Back to Editing';
                    backButton.className = 'button';
                    backButton.onclick = () => this.resetView();

                    buttonContainer.appendChild(saveButton);
                    buttonContainer.appendChild(backButton);
                    this.resultContainer.appendChild(buttonContainer);

                    // Hide loading indicators
                    this.cropBtn.disabled = true;
                    this.cropLoading.style.display = 'none';

                    if (this.previewLoading) {
                        this.previewLoading.style.display = 'none';
                    }

                    // Add a message about the preview
                    this.showStatus('Preview generated successfully. Click Save to process the image.', 'success');

                    // Store the preview image URL for later use
                    this.previewImageUrl = previewImage.src;
                })
                .catch(error => {
                    console.error('Error generating preview:', error);
                    this.cropLoading.style.display = 'none';

                    // Show error in preview container
                    if (this.previewLoading) {
                        this.previewLoading.style.display = 'none';
                    }
                    if (this.previewError) {
                        this.previewError.style.display = 'flex';
                    }

                    this.resultContainer.style.display = 'block';
                    this.showStatus('Error: ' + error.message, 'error');
                });
            }

            createPrintLayout(imageId) {
                if (!imageId) {
                    this.showStatus('No image available for print layout', 'error');
                    return;
                }

                console.log('Creating print layout for image ID:', imageId);
                console.log('PrintLayoutManager exists:', !!window.printLayoutManager);

                // Use the PrintLayoutManager to show the print layout section
                if (window.printLayoutManager) {
                    // Make sure the print layout section exists
                    const printLayoutSection = document.getElementById('print-layout-section');
                    if (!printLayoutSection) {
                        console.error('Print layout section not found in the DOM');
                        this.showStatus('Print layout section not found', 'error');
                        return;
                    }

                    // Show the print layout section
                    window.printLayoutManager.show(imageId);
                } else {
                    console.error('PrintLayoutManager not initialized');
                    this.showStatus('Print layout manager not initialized', 'error');

                    // Try to initialize it now
                    window.printLayoutManager = new PrintLayoutManager();
                    if (window.printLayoutManager) {
                        window.printLayoutManager.show(imageId);
                    }
                }
            }

            saveImage() {
                if (!currentImageId || !this.standardId || !this.crownPoint || !this.chinPoint) {
                    this.showStatus('Please crop the image first', 'error');
                    return;
                }

                this.cropLoading.style.display = 'flex';
                this.showStatus('Processing image...', 'info');

                // Create form data with the necessary parameters
                const formData = new FormData();
                formData.append('image_id', currentImageId);
                formData.append('standard_id', this.standardId);
                formData.append('crown_x', this.crownPoint.x);
                formData.append('crown_y', this.crownPoint.y);
                formData.append('chin_x', this.chinPoint.x);
                formData.append('chin_y', this.chinPoint.y);

                // Send to server
                fetch('/api/photo/crop', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || `Server responded with status: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    this.cropLoading.style.display = 'none';
                    if (data.processed_image_id) {
                        // Show the processed image in the result container
                        const processedImageId = data.processed_image_id;
                        const processedImageUrl = `/api/photo/processed/${processedImageId}`;

                        // Clear the result container
                        this.resultContainer.innerHTML = '';

                        // Create a heading for the processed image
                        const processedHeading = document.createElement('h3');
                        processedHeading.textContent = 'Your Processed Passport Photo';
                        this.resultContainer.appendChild(processedHeading);

                        // Create an image element for the processed image
                        const processedImg = document.createElement('img');
                        processedImg.src = processedImageUrl;
                        processedImg.style.maxWidth = '100%';
                        processedImg.style.margin = '0 auto';
                        processedImg.style.display = 'block';
                        processedImg.style.border = '1px solid #ddd';
                        processedImg.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                        this.resultContainer.appendChild(processedImg);

                        // Add a message about the download
                        const downloadMessage = document.createElement('p');
                        downloadMessage.textContent = 'Click the button below to download your photo.';
                        downloadMessage.style.textAlign = 'center';
                        downloadMessage.style.marginTop = '15px';
                        this.resultContainer.appendChild(downloadMessage);

                        // Add a button container
                        const buttonContainer = document.createElement('div');
                        buttonContainer.className = 'button-group';
                        buttonContainer.style.marginTop = '15px';
                        buttonContainer.style.display = 'flex';
                        buttonContainer.style.gap = '10px';
                        buttonContainer.style.justifyContent = 'center';

                        // Add a download button
                        const downloadButton = document.createElement('button');
                        downloadButton.textContent = 'Download Photo';
                        downloadButton.className = 'button';
                        downloadButton.onclick = () => {
                            window.location.href = `/api/photo/processed/${processedImageId}?download=true`;
                        };
                        buttonContainer.appendChild(downloadButton);

                        // Add a print layout button
                        const printLayoutButton = document.createElement('button');
                        printLayoutButton.textContent = 'Create Print Layout';
                        printLayoutButton.className = 'button';
                        printLayoutButton.onclick = () => {
                            this.createPrintLayout(processedImageId);
                        };
                        buttonContainer.appendChild(printLayoutButton);

                        // Add a back button
                        const backButton = document.createElement('button');
                        backButton.textContent = 'Process Another Photo';
                        backButton.className = 'button';
                        backButton.onclick = () => {
                            window.location.reload();
                        };
                        buttonContainer.appendChild(backButton);

                        this.resultContainer.appendChild(buttonContainer);

                        this.showStatus('Image processed successfully!', 'success');
                    } else {
                        this.showStatus('Error: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    this.cropLoading.style.display = 'none';
                    console.error('Error saving image:', error);
                    this.showStatus('Error: ' + error.message, 'error');
                });
            }
        }

        // Print Layout Manager class
        class PrintLayoutManager {
            constructor() {
                // Check if the print layout section exists in the DOM
                const printLayoutSectionExists = document.getElementById('print-layout-section') !== null;
                console.log('Print layout section exists in DOM:', printLayoutSectionExists);

                // If the print layout section doesn't exist, create it
                if (!printLayoutSectionExists) {
                    console.error('Print layout section not found in the DOM. This is a critical error.');
                    alert('Error: Print layout section not found. The print layout functionality will not work properly.');
                }

                // Get DOM elements
                this.printLayoutSection = document.getElementById('print-layout-section');
                this.printLayoutContainer = document.getElementById('print-layout-container');
                this.printLayoutLoading = document.getElementById('print-layout-loading');
                this.printPreviewContainer = document.getElementById('print-preview-container');
                this.printPreviewImage = document.getElementById('print-preview-image');
                this.createPrintBtn = document.getElementById('create-print-btn');
                this.downloadPrintBtn = document.getElementById('download-print-btn');

                // Log initialization status for debugging
                console.log('PrintLayoutManager initialization:');
                console.log('- printLayoutSection:', !!this.printLayoutSection);
                console.log('- printLayoutContainer:', !!this.printLayoutContainer);
                console.log('- printLayoutLoading:', !!this.printLayoutLoading);
                console.log('- printPreviewContainer:', !!this.printPreviewContainer);
                console.log('- printPreviewImage:', !!this.printPreviewImage);
                console.log('- createPrintBtn:', !!this.createPrintBtn);
                console.log('- downloadPrintBtn:', !!this.downloadPrintBtn);

                // Check if all required elements exist
                if (!this.printLayoutSection || !this.printLayoutContainer || !this.printLayoutLoading) {
                    console.error('Required print layout elements not found in the DOM');
                }

                this.selectedLayout = null;
                this.imageId = null;
                this.tiledImageId = null;

                // Initialize event listeners
                this.initEventListeners();

                // Preload print definitions to ensure they're available when needed
                this.preloadPrintDefinitions();
            }

            // Preload print definitions
            async preloadPrintDefinitions() {
                try {
                    console.log('Preloading print definitions...');
                    const response = await fetch('/api/print/definitions');
                    if (!response.ok) {
                        throw new Error(`Failed to preload print definitions: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`Preloaded ${data.print_definitions ? data.print_definitions.length : 0} print definitions`);

                    // Store the definitions for later use
                    this.cachedPrintDefinitions = data.print_definitions || [];
                } catch (error) {
                    console.error('Error preloading print definitions:', error);
                }
            }

            initEventListeners() {
                // Create print button
                this.createPrintBtn.addEventListener('click', () => this.createPrint());

                // Download print button
                this.downloadPrintBtn.addEventListener('click', () => this.downloadPrint());

                // Print layout toggle button
                const toggleBtn = document.getElementById('print-layout-toggle-btn');
                if (toggleBtn) {
                    toggleBtn.addEventListener('click', () => this.hide());
                }

                // Print multiple photos button
                const printMultipleBtn = document.getElementById('print-multiple-photos-btn');
                if (printMultipleBtn) {
                    printMultipleBtn.addEventListener('click', () => this.showMultiplePhotos());
                }
            }

            // Show the print layout section
            show(imageId) {
                if (!imageId) {
                    console.error('No image ID provided');
                    return;
                }

                console.log('Showing print layout for image ID:', imageId);

                // Check if the print layout section exists
                if (!this.printLayoutSection) {
                    console.error('Print layout section not found');

                    // Try to get it again
                    this.printLayoutSection = document.getElementById('print-layout-section');
                    if (!this.printLayoutSection) {
                        alert('Error: Print layout section not found. Please refresh the page and try again.');
                        return;
                    }
                }

                // Store the image ID
                this.imageId = imageId;

                // Make sure the print layout container exists
                if (!this.printLayoutContainer) {
                    this.printLayoutContainer = document.getElementById('print-layout-container');
                }

                if (!this.printLayoutLoading) {
                    this.printLayoutLoading = document.getElementById('print-layout-loading');
                }

                // Show the print layout section
                this.printLayoutSection.style.display = 'block';

                // Scroll to the print layout section
                this.printLayoutSection.scrollIntoView({ behavior: 'smooth' });

                // Load print layouts
                this.loadPrintLayouts();
            }

            // Load print layouts from the server
            async loadPrintLayouts() {
                try {
                    console.log('Loading print layouts for image ID:', this.imageId);

                    // Check if the print layout container exists
                    if (!this.printLayoutContainer) {
                        console.error('Print layout container not found');
                        this.printLayoutContainer = document.getElementById('print-layout-container');
                        if (!this.printLayoutContainer) {
                            alert('Error: Print layout container not found. Please refresh the page and try again.');
                            return;
                        }
                    }

                    // Check if the loading indicator exists
                    if (!this.printLayoutLoading) {
                        console.error('Print layout loading indicator not found');
                        this.printLayoutLoading = document.getElementById('print-layout-loading');
                    }

                    // Show loading indicator if it exists
                    if (this.printLayoutLoading) {
                        this.printLayoutLoading.style.display = 'flex';
                    }

                    // Check if we have cached print definitions
                    let definitions = [];

                    if (this.cachedPrintDefinitions && this.cachedPrintDefinitions.length > 0) {
                        console.log('Using cached print definitions');
                        definitions = this.cachedPrintDefinitions.filter(def =>
                            !(def.width === 0 && def.height === 0 && def.resolution === 0)
                        );
                    } else {
                        // Fetch print definitions from the API
                        console.log('Fetching print definitions from API');
                        const response = await fetch('/api/print/definitions');
                        if (!response.ok) {
                            throw new Error(`Failed to load print definitions: ${response.statusText}`);
                        }

                        const data = await response.json();
                        console.log('Received print definitions:', data);

                        if (data.error) {
                            throw new Error(data.error);
                        }

                        // Add print layouts
                        if (data.print_definitions && data.print_definitions.length > 0) {
                            console.log(`Found ${data.print_definitions.length} print definitions`);

                            // Skip the first definition if it's "Digital Size" with no dimensions
                            definitions = data.print_definitions.filter(def =>
                                !(def.width === 0 && def.height === 0 && def.resolution === 0)
                            );

                            // Cache the definitions for future use
                            this.cachedPrintDefinitions = data.print_definitions;
                        }
                    }

                    // Clear the container
                    this.printLayoutContainer.innerHTML = '';

                    // Log the number of definitions after filtering
                    console.log(`Processing ${definitions.length} print definitions after filtering`);

                    if (definitions.length > 0) {
                        // Generate previews for all layouts at once
                        await Promise.all(definitions.map(async (definition) => {
                            try {
                                // Add an ID if not present
                                if (!definition.id) {
                                    definition.id = definition.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
                                }

                                console.log(`Processing definition: ${definition.title} (${definition.id})`);

                                // Create a tiled print preview for this layout
                                if (this.imageId) {
                                    await this.addPrintLayoutWithPreview(definition);
                                } else {
                                    this.addPrintLayout(definition);
                                }
                            } catch (error) {
                                console.error(`Error generating preview for ${definition.title}:`, error);
                            }
                        }));

                        // Add a button to create a custom layout
                        this.addCustomLayoutButton();
                    } else {
                        console.warn('No print definitions found');
                        this.printLayoutContainer.innerHTML = '<p>No print layouts available</p>';
                    }

                    // Hide loading indicator
                    if (this.printLayoutLoading) {
                        this.printLayoutLoading.style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error loading print layouts:', error);

                    if (this.printLayoutContainer) {
                        this.printLayoutContainer.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                <h3>Error Loading Print Layouts</h3>
                                <p>${error.message}</p>
                                <button class="button" onclick="window.printLayoutManager.loadPrintLayouts()">
                                    <i class="fas fa-sync"></i> Try Again
                                </button>
                            </div>
                        `;
                    }

                    // Hide loading indicator
                    if (this.printLayoutLoading) {
                        this.printLayoutLoading.style.display = 'none';
                    }

                    // Show an alert for better visibility
                    alert(`Error loading print layouts: ${error.message}`);
                }
            }

            // Add a custom layout button
            addCustomLayoutButton() {
                const button = document.createElement('div');
                button.className = 'add-layout-button';
                button.innerHTML = `
                    <i class="fas fa-plus-circle"></i>
                    <span>Add custom print size</span>
                `;

                button.addEventListener('click', () => {
                    this.showCustomLayoutModal();
                });

                this.printLayoutContainer.appendChild(button);
            }

            // Show the custom layout modal
            showCustomLayoutModal() {
                // Create modal if it doesn't exist
                if (!document.getElementById('custom-layout-modal')) {
                    const modal = document.createElement('div');
                    modal.id = 'custom-layout-modal';
                    modal.className = 'modal';
                    modal.innerHTML = `
                        <div class="modal-content">
                            <span class="close">&times;</span>
                            <h2>Create Custom Print Layout</h2>
                            <form id="custom-layout-form">
                                <div class="form-group">
                                    <label for="layout-title">Title:</label>
                                    <input type="text" id="layout-title" required placeholder="e.g., My Custom Layout">
                                </div>

                                <div class="form-group">
                                    <label for="layout-width">Width:</label>
                                    <input type="number" id="layout-width" required min="1" step="0.01" value="6">
                                </div>

                                <div class="form-group">
                                    <label for="layout-height">Height:</label>
                                    <input type="number" id="layout-height" required min="1" step="0.01" value="4">
                                </div>

                                <div class="form-group">
                                    <label for="layout-units">Units:</label>
                                    <select id="layout-units" required>
                                        <option value="inch">Inches</option>
                                        <option value="cm">Centimeters</option>
                                        <option value="mm">Millimeters</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="layout-resolution">Resolution (DPI):</label>
                                    <input type="number" id="layout-resolution" required min="72" value="300">
                                </div>

                                <div class="form-group">
                                    <label for="layout-padding">Padding:</label>
                                    <input type="number" id="layout-padding" min="0" step="0.01" value="0">
                                    <small>Space around the edges of the print</small>
                                </div>

                                <div class="form-group">
                                    <label for="layout-gutter">Gutter:</label>
                                    <input type="number" id="layout-gutter" min="0" step="0.01" value="0">
                                    <small>Space between photos</small>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="layout-maximize-photos">
                                    <label for="layout-maximize-photos">Maximize number of photos</label>
                                    <small>Automatically adjust padding and gutter to fit as many photos as possible</small>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="layout-custom-photo-count">
                                    <label for="layout-custom-photo-count">Custom photo count</label>
                                    <small>Specify exact number of photos to include</small>
                                </div>

                                <div class="form-group photo-count-options" style="display: none;">
                                    <div class="form-row" style="display: flex; gap: 10px;">
                                        <div>
                                            <label for="layout-num-rows">Rows:</label>
                                            <input type="number" id="layout-num-rows" min="1" step="1" value="2" style="width: 60px;">
                                        </div>
                                        <div>
                                            <label for="layout-num-cols">Columns:</label>
                                            <input type="number" id="layout-num-cols" min="1" step="1" value="3" style="width: 60px;">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="layout-show-borders">
                                    <label for="layout-show-borders">Show borders around photos</label>
                                </div>

                                <div class="form-group border-options" style="display: none;">
                                    <label for="layout-border-width">Border width:</label>
                                    <input type="number" id="layout-border-width" min="0.01" step="0.01" value="0.02">

                                    <label for="layout-border-color">Border color:</label>
                                    <input type="color" id="layout-border-color" value="#000000">
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="button">Create Layout</button>
                                    <button type="button" class="button cancel-button">Cancel</button>
                                </div>
                            </form>
                        </div>
                    `;

                    document.body.appendChild(modal);

                    // Add event listeners
                    const closeBtn = modal.querySelector('.close');
                    closeBtn.addEventListener('click', () => {
                        modal.style.display = 'none';
                    });

                    const cancelBtn = modal.querySelector('.cancel-button');
                    cancelBtn.addEventListener('click', () => {
                        modal.style.display = 'none';
                    });

                    const showBordersCheckbox = modal.querySelector('#layout-show-borders');
                    const borderOptions = modal.querySelector('.border-options');

                    showBordersCheckbox.addEventListener('change', () => {
                        borderOptions.style.display = showBordersCheckbox.checked ? 'block' : 'none';
                    });

                    // Add event listener for custom photo count checkbox
                    const customPhotoCountCheckbox = modal.querySelector('#layout-custom-photo-count');
                    const photoCountOptions = modal.querySelector('.photo-count-options');
                    const maximizePhotosCheckbox = modal.querySelector('#layout-maximize-photos');

                    customPhotoCountCheckbox.addEventListener('change', () => {
                        photoCountOptions.style.display = customPhotoCountCheckbox.checked ? 'block' : 'none';

                        // Disable maximize photos if custom photo count is checked
                        if (customPhotoCountCheckbox.checked) {
                            maximizePhotosCheckbox.checked = false;
                            maximizePhotosCheckbox.disabled = true;
                        } else {
                            maximizePhotosCheckbox.disabled = false;
                        }
                    });

                    // Also disable custom photo count if maximize photos is checked
                    maximizePhotosCheckbox.addEventListener('change', () => {
                        if (maximizePhotosCheckbox.checked) {
                            customPhotoCountCheckbox.checked = false;
                            customPhotoCountCheckbox.disabled = true;
                            photoCountOptions.style.display = 'none';
                        } else {
                            customPhotoCountCheckbox.disabled = false;
                        }
                    });

                    const form = modal.querySelector('#custom-layout-form');

                    // Reset the form to default values
                    form.reset();

                    // Reset the modal title
                    modal.querySelector('h2').textContent = 'Create Custom Print Layout';

                    // Reset the submit button text
                    form.querySelector('button[type="submit"]').textContent = 'Create Layout';

                    // Add the submit event listener
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.createCustomLayout();
                    });

                    // Close modal when clicking outside
                    window.addEventListener('click', (event) => {
                        if (event.target === modal) {
                            modal.style.display = 'none';
                        }
                    });
                }

                // Show the modal
                const modal = document.getElementById('custom-layout-modal');

                // Reset the form if it's not an edit operation
                const form = modal.querySelector('#custom-layout-form');
                if (!form.dataset.editId) {
                    // Reset the form to default values
                    form.reset();

                    // Reset the modal title
                    modal.querySelector('h2').textContent = 'Create Custom Print Layout';

                    // Reset the submit button text
                    form.querySelector('button[type="submit"]').textContent = 'Create Layout';

                    // Reset the border options display
                    const borderOptions = form.querySelector('.border-options');
                    borderOptions.style.display = 'none';

                    // Remove the edit ID if it exists
                    delete form.dataset.editId;
                }

                modal.style.display = 'block';
            }

            // Create a custom layout from form data
            async createCustomLayout() {
                const modal = document.getElementById('custom-layout-modal');
                const form = modal.querySelector('#custom-layout-form');

                // Get form values
                const title = form.querySelector('#layout-title').value;
                const width = parseFloat(form.querySelector('#layout-width').value);
                const height = parseFloat(form.querySelector('#layout-height').value);
                const units = form.querySelector('#layout-units').value;
                const resolution = parseInt(form.querySelector('#layout-resolution').value);
                const padding = parseFloat(form.querySelector('#layout-padding').value) || 0;
                const gutter = parseFloat(form.querySelector('#layout-gutter').value) || 0;
                const maximizePhotos = form.querySelector('#layout-maximize-photos').checked;
                const customPhotoCount = form.querySelector('#layout-custom-photo-count').checked;
                const numRows = parseInt(form.querySelector('#layout-num-rows').value) || 2;
                const numCols = parseInt(form.querySelector('#layout-num-cols').value) || 3;
                const showBorders = form.querySelector('#layout-show-borders').checked;
                const borderWidth = parseFloat(form.querySelector('#layout-border-width').value) || 0.02;
                const borderColor = form.querySelector('#layout-border-color').value;

                // Create layout definition
                const customLayout = {
                    title: title,
                    width: width,
                    height: height,
                    units: units,
                    resolution: resolution,
                    padding: padding,
                    gutter: gutter,
                    custom: true,
                    paper_color: "#ffffff",
                    show_borders: showBorders,
                    border_width: borderWidth,
                    border_color: borderColor,
                    maximize_photos: maximizePhotos,
                    custom_photo_count: customPhotoCount,
                    num_rows: numRows,
                    num_cols: numCols
                };

                try {
                    // Save the custom layout to the server
                    const response = await fetch('/api/print/definitions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(customLayout)
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to create custom layout: ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Add the new layout to the UI
                    if (data.print_definition) {
                        // Add to cached definitions
                        if (this.cachedPrintDefinitions) {
                            this.cachedPrintDefinitions.push(data.print_definition);
                        }

                        // Add to UI
                        if (this.imageId) {
                            await this.addPrintLayoutWithPreview(data.print_definition);
                        } else {
                            this.addPrintLayout(data.print_definition);
                        }

                        // Hide the modal
                        modal.style.display = 'none';

                        // Show success message
                        alert(`Custom layout "${title}" created successfully!`);
                    }
                } catch (error) {
                    console.error('Error creating custom layout:', error);
                    alert(`Error creating custom layout: ${error.message}`);
                }
            }

            // Add a print layout with a real preview
            async addPrintLayoutWithPreview(definition) {
                try {
                    // Create a tiled print using the selected layout
                    const url = new URL('/api/print/create-tiled-print', window.location.origin);
                    url.searchParams.append('image_id', this.imageId);
                    url.searchParams.append('print_definition_id', definition.id);

                    // Use custom photo count if specified
                    if (definition.custom_photo_count) {
                        url.searchParams.append('num_rows', definition.num_rows || 2);
                        url.searchParams.append('num_cols', definition.num_cols || 3);
                    } else {
                        // Default values
                        url.searchParams.append('num_rows', '2');
                        url.searchParams.append('num_cols', '3');
                    }
                    url.searchParams.append('spacing_mm', '2.0');

                    const response = await fetch(url, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to generate preview: ${response.statusText}`);
                    }

                    const data = await response.json();
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Store the tiled image ID in the definition for later use
                    definition.tiledImageId = data.tiled_image_id;

                    // Add the layout with the preview image
                    this.addPrintLayout(definition, `/api/print/image/${data.tiled_image_id}`);
                } catch (error) {
                    console.error(`Error generating preview for ${definition.title}:`, error);
                    // Fall back to the standard layout without a preview
                    this.addPrintLayout(definition);
                }
            }

            // Add a print layout to the container
            addPrintLayout(definition, previewUrl = null) {
                // Check if the print layout container exists
                if (!this.printLayoutContainer) {
                    console.error('Print layout container not found');
                    this.printLayoutContainer = document.getElementById('print-layout-container');
                    if (!this.printLayoutContainer) {
                        console.error('Cannot add print layout: container not found');
                        return;
                    }
                }

                console.log(`Adding print layout: ${definition.title} (${definition.id})`);

                const layoutItem = document.createElement('div');
                layoutItem.className = 'print-layout-item';
                layoutItem.dataset.id = definition.id;

                // Add a class for custom layouts
                if (definition.custom) {
                    layoutItem.classList.add('custom-layout');
                } else {
                    // Add a slight grey border for default layouts for easier cutting
                    layoutItem.classList.add('default-layout');
                }

                // Create thumbnail
                const thumbnail = document.createElement('div');
                thumbnail.className = 'print-layout-thumbnail';

                if (previewUrl) {
                    // Use the actual preview image
                    const img = document.createElement('img');
                    img.src = previewUrl;
                    img.alt = definition.title;
                    thumbnail.appendChild(img);
                } else {
                    // Create a visual representation of the print layout
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = 200;
                    canvas.height = 200;

                    // Draw the layout representation
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);

                    // Draw the paper outline
                    ctx.strokeStyle = '#3498db';
                    ctx.lineWidth = 2;

                    // Calculate aspect ratio
                    const aspectRatio = definition.width / definition.height;
                    let width, height;

                    if (aspectRatio > 1) {
                        width = 180;
                        height = width / aspectRatio;
                    } else {
                        height = 180;
                        width = height * aspectRatio;
                    }

                    const x = (canvas.width - width) / 2;
                    const y = (canvas.height - height) / 2;

                    ctx.strokeRect(x, y, width, height);

                    // Calculate number of photos that would fit
                    const photoWidth = width / 3;
                    const photoHeight = height / 2;
                    const rows = Math.floor(height / photoHeight);
                    const cols = Math.floor(width / photoWidth);

                    // Draw sample photos inside
                    for (let row = 0; row < rows; row++) {
                        for (let col = 0; col < cols; col++) {
                            const photoX = x + col * photoWidth;
                            const photoY = y + row * photoHeight;

                            ctx.fillStyle = '#e9ecef';
                            ctx.fillRect(photoX, photoY, photoWidth, photoHeight);

                            // Add a slight grey border for all photos for easier cutting
                            // Use a lighter grey for default layouts
                            ctx.strokeStyle = definition.custom ? '#aaaaaa' : '#cccccc';
                            ctx.lineWidth = 1;
                            ctx.strokeRect(photoX, photoY, photoWidth, photoHeight);

                            // Draw a face-like shape
                            ctx.fillStyle = '#adb5bd';
                            const faceSize = Math.min(photoWidth, photoHeight) * 0.5;
                            const faceX = photoX + (photoWidth - faceSize) / 2;
                            const faceY = photoY + (photoHeight - faceSize) / 2;
                            ctx.beginPath();
                            ctx.arc(faceX + faceSize/2, faceY + faceSize/2, faceSize/2, 0, Math.PI * 2);
                            ctx.fill();
                        }
                    }

                    thumbnail.appendChild(canvas);
                }

                layoutItem.appendChild(thumbnail);

                // Create title and action buttons container
                const titleContainer = document.createElement('div');
                titleContainer.className = 'print-layout-header';

                // Create title
                const title = document.createElement('div');
                title.className = 'print-layout-title';
                title.textContent = definition.title;
                titleContainer.appendChild(title);

                // Add edit/delete icons for custom layouts
                if (definition.custom) {
                    const actionButtons = document.createElement('div');
                    actionButtons.className = 'print-layout-actions';

                    // Edit button
                    const editButton = document.createElement('button');
                    editButton.className = 'action-button edit-button';
                    editButton.innerHTML = '<i class="fas fa-edit"></i>';
                    editButton.title = 'Edit layout';
                    editButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent layout selection
                        this.editCustomLayout(definition);
                    });
                    actionButtons.appendChild(editButton);

                    // Delete button
                    const deleteButton = document.createElement('button');
                    deleteButton.className = 'action-button delete-button';
                    deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i>';
                    deleteButton.title = 'Delete layout';
                    deleteButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent layout selection
                        this.deleteCustomLayout(definition);
                    });
                    actionButtons.appendChild(deleteButton);

                    titleContainer.appendChild(actionButtons);
                }

                layoutItem.appendChild(titleContainer);

                // Create details
                if (definition.width > 0 && definition.height > 0) {
                    const details = document.createElement('div');
                    details.className = 'print-layout-details';
                    const units = definition.units === 'inch' ? '″' : definition.units;

                    // Show dimensions
                    let detailsText = `${definition.width} × ${definition.height} ${units}`;

                    // Add photo count if custom
                    if (definition.custom_photo_count) {
                        detailsText += ` (${definition.num_rows}×${definition.num_cols} photos)`;
                    }

                    details.textContent = detailsText;
                    layoutItem.appendChild(details);
                }

                // Add click event to select this layout
                layoutItem.addEventListener('click', () => {
                    this.selectLayout(definition);
                });

                this.printLayoutContainer.appendChild(layoutItem);
            }

            // Select a layout
            selectLayout(definition) {
                this.selectedLayout = definition;

                // Update UI
                const layoutItems = document.querySelectorAll('.print-layout-item');
                layoutItems.forEach(item => {
                    if (item.dataset.id === definition.id) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                });

                // Generate preview
                this.showPreview(definition);
            }

            // Show the preview for the selected layout
            async showPreview(definition) {
                if (!definition) {
                    console.error('No layout selected');
                    return;
                }

                try {
                    // If we already have a tiled image ID for this definition, use it
                    if (definition.tiledImageId) {
                        this.tiledImageId = definition.tiledImageId;

                        // Show the preview container
                        this.printPreviewContainer.style.display = 'block';

                        // Update the preview image
                        this.printPreviewImage.src = `/api/print/image/${this.tiledImageId}?t=${new Date().getTime()}`;

                        // Scroll to the preview
                        this.printPreviewContainer.scrollIntoView({ behavior: 'smooth' });
                        return;
                    }

                    // Otherwise, generate a new preview
                    await this.generatePreview();
                } catch (error) {
                    console.error('Error showing preview:', error);
                    alert(`Error showing preview: ${error.message}`);
                }
            }

            // Generate a preview of the selected layout
            async generatePreview() {
                if (!this.selectedLayout || !this.imageId) {
                    console.error('No layout or image selected');
                    return;
                }

                try {
                    this.printLayoutLoading.style.display = 'flex';

                    // Create a tiled print using the selected layout
                    const url = new URL('/api/print/create-tiled-print', window.location.origin);
                    url.searchParams.append('image_id', this.imageId);
                    url.searchParams.append('print_definition_id', this.selectedLayout.id);

                    // Use custom photo count if specified
                    if (this.selectedLayout.custom_photo_count) {
                        url.searchParams.append('num_rows', this.selectedLayout.num_rows || 2);
                        url.searchParams.append('num_cols', this.selectedLayout.num_cols || 3);
                    } else {
                        // Default values
                        url.searchParams.append('num_rows', '2');
                        url.searchParams.append('num_cols', '3');
                    }
                    url.searchParams.append('spacing_mm', '2.0');

                    const response = await fetch(url, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to generate preview: ${response.statusText}`);
                    }

                    const data = await response.json();
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Store the tiled image ID
                    this.tiledImageId = data.tiled_image_id;

                    // Also store it in the definition for future use
                    this.selectedLayout.tiledImageId = this.tiledImageId;

                    // Show the preview container
                    this.printPreviewContainer.style.display = 'block';

                    // Update the preview image
                    this.printPreviewImage.src = `/api/print/image/${this.tiledImageId}?t=${new Date().getTime()}`;

                    // Scroll to the preview
                    this.printPreviewContainer.scrollIntoView({ behavior: 'smooth' });

                    this.printLayoutLoading.style.display = 'none';
                } catch (error) {
                    console.error('Error generating preview:', error);
                    this.printLayoutLoading.style.display = 'none';
                    alert(`Error generating preview: ${error.message}`);
                }
            }

            // Create the final print
            createPrint() {
                if (!this.tiledImageId) {
                    alert('Please select a print layout first');
                    return;
                }

                // Open the print in a new window
                window.open(`/api/print/image/${this.tiledImageId}`, '_blank');
            }

            // Download the print
            downloadPrint() {
                if (!this.tiledImageId) {
                    alert('Please select a print layout first');
                    return;
                }

                // Create a download link
                const link = document.createElement('a');
                link.href = `/api/print/image/${this.tiledImageId}?download=true`;
                link.download = `passport_print_${new Date().toISOString().slice(0, 10)}.jpg`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // Hide the print layout section
            hide() {
                this.printLayoutSection.style.display = 'none';
                this.printPreviewContainer.style.display = 'none';
                this.selectedLayout = null;
                this.imageId = null;
                this.tiledImageId = null;
            }

            // Edit a custom layout
            editCustomLayout(definition) {
                console.log('Editing custom layout:', definition);

                // Create modal if it doesn't exist
                if (!document.getElementById('custom-layout-modal')) {
                    this.showCustomLayoutModal();
                }

                const modal = document.getElementById('custom-layout-modal');
                const form = modal.querySelector('#custom-layout-form');

                // Update modal title
                modal.querySelector('h2').textContent = 'Edit Custom Print Layout';

                // Fill form with definition values
                form.querySelector('#layout-title').value = definition.title;
                form.querySelector('#layout-width').value = definition.width;
                form.querySelector('#layout-height').value = definition.height;
                form.querySelector('#layout-units').value = definition.units;
                form.querySelector('#layout-resolution').value = definition.resolution;
                form.querySelector('#layout-padding').value = definition.padding || 0;
                form.querySelector('#layout-gutter').value = definition.gutter || 0;
                form.querySelector('#layout-maximize-photos').checked = definition.maximize_photos || false;
                form.querySelector('#layout-custom-photo-count').checked = definition.custom_photo_count || false;
                form.querySelector('#layout-num-rows').value = definition.num_rows || 2;
                form.querySelector('#layout-num-cols').value = definition.num_cols || 3;
                form.querySelector('#layout-show-borders').checked = definition.show_borders || false;

                // Show/hide photo count options based on checkbox
                const photoCountOptions = form.querySelector('.photo-count-options');
                photoCountOptions.style.display = definition.custom_photo_count ? 'block' : 'none';

                // Handle checkbox states
                const maximizePhotosCheckbox = form.querySelector('#layout-maximize-photos');
                const customPhotoCountCheckbox = form.querySelector('#layout-custom-photo-count');

                if (definition.custom_photo_count) {
                    maximizePhotosCheckbox.disabled = true;
                } else if (definition.maximize_photos) {
                    customPhotoCountCheckbox.disabled = true;
                }

                // Show border options if borders are enabled
                const borderOptions = form.querySelector('.border-options');
                borderOptions.style.display = definition.show_borders ? 'block' : 'none';

                if (definition.show_borders) {
                    form.querySelector('#layout-border-width').value = definition.border_width || 0.02;
                    form.querySelector('#layout-border-color').value = definition.border_color || '#000000';
                }

                // Update submit button text
                form.querySelector('button[type="submit"]').textContent = 'Update Layout';

                // Store the definition ID in a data attribute
                form.dataset.editId = definition.id;

                // Remove any existing event listeners
                const oldForm = form.cloneNode(true);
                form.parentNode.replaceChild(oldForm, form);

                // Get the new form reference
                const newForm = document.getElementById('custom-layout-form');

                // Add the new submit handler
                newForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateCustomLayout(definition.id);
                });

                // Show the modal
                modal.style.display = 'block';
            }

            // Update a custom layout
            async updateCustomLayout(layoutId) {
                const modal = document.getElementById('custom-layout-modal');
                const form = modal.querySelector('#custom-layout-form');

                // Get form values
                const title = form.querySelector('#layout-title').value;
                const width = parseFloat(form.querySelector('#layout-width').value);
                const height = parseFloat(form.querySelector('#layout-height').value);
                const units = form.querySelector('#layout-units').value;
                const resolution = parseInt(form.querySelector('#layout-resolution').value);
                const padding = parseFloat(form.querySelector('#layout-padding').value) || 0;
                const gutter = parseFloat(form.querySelector('#layout-gutter').value) || 0;
                const maximizePhotos = form.querySelector('#layout-maximize-photos').checked;
                const customPhotoCount = form.querySelector('#layout-custom-photo-count').checked;
                const numRows = parseInt(form.querySelector('#layout-num-rows').value) || 2;
                const numCols = parseInt(form.querySelector('#layout-num-cols').value) || 3;
                const showBorders = form.querySelector('#layout-show-borders').checked;
                const borderWidth = parseFloat(form.querySelector('#layout-border-width').value) || 0.02;
                const borderColor = form.querySelector('#layout-border-color').value;

                // Create updated layout definition
                const updatedLayout = {
                    title: title,
                    width: width,
                    height: height,
                    units: units,
                    resolution: resolution,
                    padding: padding,
                    gutter: gutter,
                    custom: true,
                    paper_color: "#ffffff",
                    show_borders: showBorders,
                    border_width: borderWidth,
                    border_color: borderColor,
                    maximize_photos: maximizePhotos,
                    custom_photo_count: customPhotoCount,
                    num_rows: numRows,
                    num_cols: numCols
                };

                try {
                    // Update the layout on the server
                    const response = await fetch(`/api/print/definitions/${layoutId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(updatedLayout)
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to update custom layout: ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Update the layout in the cached definitions
                    if (this.cachedPrintDefinitions) {
                        const index = this.cachedPrintDefinitions.findIndex(def => def.id === layoutId);
                        if (index !== -1) {
                            this.cachedPrintDefinitions[index] = data.print_definition;
                        }
                    }

                    // Hide the modal
                    modal.style.display = 'none';

                    // Reload the print layouts
                    this.loadPrintLayouts();

                    // Show success message
                    alert(`Custom layout "${title}" updated successfully!`);
                } catch (error) {
                    console.error('Error updating custom layout:', error);
                    alert(`Error updating custom layout: ${error.message}`);
                }
            }

            // Delete a custom layout
            async deleteCustomLayout(definition) {
                // Confirm deletion
                if (!confirm(`Are you sure you want to delete the custom layout "${definition.title}"?`)) {
                    return;
                }

                try {
                    // Delete the layout from the server
                    const response = await fetch(`/api/print/definitions/${definition.id}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to delete custom layout: ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Remove the layout from the cached definitions
                    if (this.cachedPrintDefinitions) {
                        this.cachedPrintDefinitions = this.cachedPrintDefinitions.filter(def => def.id !== definition.id);
                    }

                    // Reload the print layouts
                    this.loadPrintLayouts();

                    // Show success message
                    alert(`Custom layout "${definition.title}" deleted successfully!`);
                } catch (error) {
                    console.error('Error deleting custom layout:', error);
                    alert(`Error deleting custom layout: ${error.message}`);
                }
            }

            // Show the print layout section for multiple photos
            showMultiplePhotos() {
                // Show the print layout section
                this.printLayoutSection.style.display = 'block';

                // Scroll to the print layout section
                this.printLayoutSection.scrollIntoView({ behavior: 'smooth' });

                // Show a message about selecting photos
                this.printLayoutContainer.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <h3>Print Multiple Photos</h3>
                        <p>To print multiple photos, you need to process at least one photo first.</p>
                        <p>Once you have processed a photo, you can select a print layout and create a print with multiple copies of your photo.</p>
                        <button id="process-photo-btn" class="button" style="margin-top: 15px;">
                            <i class="fas fa-camera"></i> Process a Photo Now
                        </button>
                    </div>
                `;

                // Add event listener to the process photo button
                const processPhotoBtn = document.getElementById('process-photo-btn');
                if (processPhotoBtn) {
                    processPhotoBtn.addEventListener('click', () => {
                        this.hide();
                        // Scroll to the upload section
                        document.querySelector('.api-test').scrollIntoView({ behavior: 'smooth' });
                    });
                }
            }
        }

        // Initialize the crop box application when the DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Check if we're on the edit page
            if (window.location.pathname === '/edit') {
                // Initialize edit page
                initEditPage();
            } else {
                // Initialize crop box application
                const cropBoxApp = new CropBoxApp();

                // Initialize print layout manager
                console.log('Initializing PrintLayoutManager...');
                window.printLayoutManager = new PrintLayoutManager();
                console.log('PrintLayoutManager initialized successfully');

                // Initialize collapsible sections
                const landmarksToggle = document.getElementById('landmarksToggle');
                const landmarksContent = document.getElementById('landmarksResult');

                if (landmarksToggle && landmarksContent) {
                    landmarksToggle.addEventListener('click', function() {
                        this.classList.toggle('active');
                        if (landmarksContent.classList.contains('active')) {
                            landmarksContent.classList.remove('active');
                            this.innerHTML = '<i class="fas fa-chevron-down"></i> Show Landmarks Data';
                        } else {
                            landmarksContent.classList.add('active');
                            this.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Landmarks Data';
                        }
                    });
                }
            }
        });

        // Function to initialize the edit page
        function initEditPage() {
            // Get the main content element
            const mainContent = document.getElementById('main-content');
            if (!mainContent) return;

            // Get the processed image ID from localStorage
            const processedImageId = localStorage.getItem('currentProcessedImageId');
            if (!processedImageId) {
                mainContent.innerHTML = `
                    <div class="container">
                        <h1>Edit Photo</h1>
                        <div class="alert alert-danger">
                            No processed image found. Please <a href="/">go back</a> and process an image first.
                        </div>
                    </div>
                `;
                return;
            }

            // Display the edit page content
            mainContent.innerHTML = `
                <div class="container">
                    <h1>Your Processed Photo</h1>
                    <div class="processed-image-container">
                        <img id="processed-image" src="/api/photo/image/${processedImageId}" alt="Processed Photo">
                    </div>
                    <div class="button-group" style="margin-top: 20px;">
                        <button class="button" id="downloadBtn">Download Photo</button>
                        <button class="button" id="backBtn">Process Another Photo</button>
                    </div>
                </div>
            `;

            // Add event listeners
            document.getElementById('downloadBtn').addEventListener('click', () => {
                window.location.href = `/api/photo/image/${processedImageId}?download=true`;
            });

            document.getElementById('backBtn').addEventListener('click', () => {
                localStorage.removeItem('currentProcessedImageId');
                window.location.href = '/';
            });
        }
    </script>
</body>
</html>
