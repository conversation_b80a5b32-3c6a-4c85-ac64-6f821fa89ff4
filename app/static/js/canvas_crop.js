// Geometry classes
class Point {
    constructor(x, y) {
        this.x = x;
        this.y = y;
    }

    equals(pt) {
        return pt.x === this.x && pt.y === this.y;
    }

    norm() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }

    add(pt) {
        return new Point(this.x + pt.x, this.y + pt.y);
    }

    sub(pt) {
        return new Point(this.x - pt.x, this.y - pt.y);
    }

    mult(scalar) {
        return new Point(this.x * scalar, this.y * scalar);
    }

    div(scalar) {
        return new Point(this.x / scalar, this.y / scalar);
    }

    distTo(pt) {
        const dx = this.x - pt.x;
        const dy = this.y - pt.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    angle(pt) {
        const dx = this.x - pt.x;
        const dy = this.y - pt.y;
        return Math.atan2(dy, dx);
    }
}

class Rect {
    constructor(cx, cy, width, height) {
        this.cx = cx;
        this.cy = cy;
        this.width = width;
        this.height = height;
    }

    topLeft() {
        return new Point(this.cx - this.width / 2, this.cy - this.height / 2);
    }

    bottomRight() {
        return new Point(this.cx + this.width / 2, this.cy + this.height / 2);
    }
}

class RotatedRect {
    constructor(center, width, height, angle) {
        this.center = center;
        this.width = width;
        this.height = height;
        this.angle = angle;
    }

    corners() {
        const w2 = this.width / 2;
        const h2 = this.height / 2;
        const cosAngle = Math.cos(this.angle);
        const sinAngle = Math.sin(this.angle);
        const corners = [];
        for (const p of [
            [-w2, -h2],
            [-w2, h2],
            [w2, h2],
            [w2, -h2],
        ]) {
            const x = this.center.x + p[0] * cosAngle - p[1] * sinAngle;
            const y = this.center.y + p[0] * sinAngle + p[1] * cosAngle;
            corners.push(new Point(x, y));
        }
        return corners;
    }

    boundingBox() {
        const corners = this.corners();
        const c0 = corners[0];
        let x1 = c0.x;
        let x2 = c0.x;
        let y1 = c0.y;
        let y2 = c0.y;
        for (const c of corners) {
            if (c.x < x1) x1 = c.x;
            if (c.x > x2) x2 = c.x;
            if (c.y < y1) y1 = c.y;
            if (c.y > y2) y2 = c.y;
        }
        return new Rect((x1 + x2) / 2, (y1 + y2) / 2, x2 - x1, y2 - y1);
    }
}

// Utility functions
function middlePoint(pt1, pt2) {
    return new Point((pt1.x + pt2.x) / 2, (pt1.y + pt2.y) / 2);
}

function pointAtDistance(p0, p1, dist) {
    if (p1.equals(p0)) {
        console.error('Input points cannot be equal, returning p0');
        return p0;
    }

    // Calculate vector from p0 to p1
    const vec = p1.sub(p0);

    // Calculate the norm (length) of the vector
    const norm = vec.norm();

    // Calculate the ratio of the desired distance to the vector length
    const ratio = dist / norm;

    // Log the calculation for debugging
    console.log("pointAtDistance calculation:", {
        p0, p1, dist, norm, ratio,
        result: p0.add(vec.mult(ratio))
    });

    // Return the point at the specified distance from p0 towards p1
    return p0.add(vec.mult(ratio));
}

function getAffineTransform(fr, to) {
    const x0 = to[0].x;
    const y0 = to[0].y;
    const x1 = to[1].x;
    const y1 = to[1].y;
    const x2 = to[2].x;
    const y2 = to[2].y;
    const u0 = fr[0].x;
    const v0 = fr[0].y;
    const u1 = fr[1].x;
    const v1 = fr[1].y;
    const u2 = fr[2].x;
    const v2 = fr[2].y;

    // Compute matrix transform
    const delta = u0 * v1 + v0 * u2 + u1 * v2 - v1 * u2 - v0 * u1 - u0 * v2;
    const deltaA = x0 * v1 + v0 * x2 + x1 * v2 - v1 * x2 - v0 * x1 - x0 * v2;
    const deltaB = u0 * x1 + x0 * u2 + u1 * x2 - x1 * u2 - x0 * u1 - u0 * x2;
    const deltaC = u0 * v1 * x2 + v0 * x1 * u2 + x0 * u1 * v2 - x0 * v1 * u2 - v0 * u1 * x2 - u0 * x1 * v2;
    const deltaD = y0 * v1 + v0 * y2 + y1 * v2 - v1 * y2 - v0 * y1 - y0 * v2;
    const deltaE = u0 * y1 + y0 * u2 + u1 * y2 - y1 * u2 - y0 * u1 - u0 * y2;
    const deltaF = u0 * v1 * y2 + v0 * y1 * u2 + y0 * u1 * v2 - y0 * v1 * u2 - v0 * u1 * y2 - u0 * y1 * v2;

    return [deltaA / delta, deltaD / delta, deltaB / delta, deltaE / delta, deltaC / delta, deltaF / delta];
}

// Photo dimensions class
class PhotoDimensions {
    constructor(pictureWidth, pictureHeight, units, faceHeight, crownTop, dpi, bottomEyeLine = 0) {
        this.pictureWidth = pictureWidth;
        this.pictureHeight = pictureHeight;
        this.units = units;
        this.faceHeight = faceHeight;
        this.crownTop = crownTop;
        this.dpi = dpi;
        this.bottomEyeLine = bottomEyeLine;
    }
}

// Default photo dimensions (US Passport)
const defaultPhotoDimensions = new PhotoDimensions(
    2, // width in inches
    2, // height in inches
    'inch',
    1.38, // face height in inches
    0.5, // crown top in inches
    300 // dpi
);

function getCroppingCenter(p, crownPoint, chinPoint) {
    if (!(p.crownTop > 0) && !(p.bottomEyeLine > 0)) {
        // Estimate the center of the picture to be the median point between the crown point and the chin point
        const center = middlePoint(crownPoint, chinPoint);
        console.log("Using middle point as center:", center);
        return center;
    }

    // Calculate the face height in pixels
    const faceHeightPix = crownPoint.distTo(chinPoint);

    // Calculate the ratio between image pixels and photo dimensions
    const mmToPixRatio = faceHeightPix / p.faceHeight;

    let crownTop = 0;
    if (p.crownTop > 0) {
        crownTop = p.crownTop;
        console.log("Using crownTop value:", crownTop, p.units);
    } else if (p.bottomEyeLine > 0) {
        // Convert eye line to bottom distance into crown top distance
        // The coefficient represents the ratio of chin-to-eye distance relative to total face height
        const chinFrownCoefficient = 0.8945 / 1.7699;
        crownTop = p.pictureHeight - p.bottomEyeLine - p.faceHeight * (1 - chinFrownCoefficient);
        console.log("Calculated crownTop from bottomEyeLine:", crownTop, p.units);
    }

    // Calculate the distance from crown to center of the photo
    const crownToCenter = p.pictureHeight / 2 - crownTop;

    // Calculate the distance from crown to center in pixels
    const crownToCenterPix = mmToPixRatio * crownToCenter;

    console.log("Cropping center calculation:", {
        faceHeightPix,
        mmToPixRatio,
        crownTop,
        crownToCenter,
        crownToCenterPix
    });

    // Calculate the center point by moving from crown towards chin
    const center = pointAtDistance(crownPoint, chinPoint, crownToCenterPix);
    console.log("Calculated center:", center);
    return center;
}

function getCroppingRectangle(p, crownPoint, chinPoint) {
    // Get the center of the crop rectangle
    const centerPic = getCroppingCenter(p, crownPoint, chinPoint);

    // Calculate dimensions and orientation
    const faceHeightPix = crownPoint.distTo(chinPoint);
    const normal = crownPoint.angle(chinPoint);
    const scale = faceHeightPix / p.faceHeight;
    const cropHeightPix = p.pictureHeight * scale;
    const cropWidthPix = p.pictureWidth * scale;

    // Log the calculated values for debugging
    console.log("Crop rectangle calculation:", {
        centerPic,
        faceHeightPix,
        normal: normal * (180 / Math.PI), // Convert to degrees for readability
        scale,
        cropHeightPix,
        cropWidthPix
    });

    return new RotatedRect(centerPic, cropWidthPix, cropHeightPix, normal);
}

// Main application class
class CanvasCropApp {
    constructor() {
        // DOM elements
        this.canvas = document.getElementById('image-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.overlay = document.getElementById('overlay');
        this.viewport = document.getElementById('viewport');
        this.fileInput = document.getElementById('file-input');
        this.loadImageBtn = document.getElementById('load-image-btn');
        this.cropBtn = document.getElementById('crop-btn');
        this.resetBtn = document.getElementById('reset-btn');
        this.spinner = document.getElementById('spinner');
        this.resultContainer = document.getElementById('result-container');
        this.croppedImage = document.getElementById('cropped-image');
        this.saveBtn = document.getElementById('save-btn');
        this.backBtn = document.getElementById('back-btn');
        this.standardSelector = document.getElementById('standard-selector');

        // Landmark elements
        this.crownMark = document.getElementById('crown-mark');
        this.chinMark = document.getElementById('chin-mark');
        this.crownText = document.getElementById('crown-text');
        this.chinText = document.getElementById('chin-text');
        this.middleLine = document.getElementById('middle-line');
        this.crownLine = document.getElementById('crown-line');
        this.chinLine = document.getElementById('chin-line');
        this.cropRect = document.getElementById('crop-rect');
        this.faceEllipse = document.getElementById('face-ellipse');
        this.faceHeightText = document.getElementById('face-height-text');
        this.rotationText = document.getElementById('rotation-text');
        this.warningText = document.getElementById('warning-text');

        // Measurements panel elements
        this.measurementsPanel = document.getElementById('measurements-panel');
        this.measurementsTitle = document.getElementById('measurements-title');
        this.faceHeightMeasurement = document.getElementById('face-height-measurement');
        this.crownTopMeasurement = document.getElementById('crown-top-measurement');
        this.faceWidthMeasurement = document.getElementById('face-width-measurement');
        this.photoSizeMeasurement = document.getElementById('photo-size-measurement');

        // State variables
        this.image = null;
        this.imageWidth = 0;
        this.imageHeight = 0;
        this.viewportWidth = 0;
        this.viewportHeight = 0;
        this.xLeft = 0;
        this.yTop = 0;
        this.zoom = 1;
        this.ratio = 0;
        this.crownPoint = new Point(0, 0);
        this.chinPoint = new Point(0, 0);
        this.isDragging = false;
        this.dragTarget = null;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.photoDimensions = defaultPhotoDimensions;
        this.imageId = null;
        this.standardId = null;
        this.croppedImageData = null;

        // Smooth dragging variables
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        this.velocityX = 0;
        this.velocityY = 0;
        this.smoothingFactor = 0.3; // Lower = smoother but more lag (0-1)
        this.dragThreshold = 2; // Minimum distance to move before registering drag

        // Distance visualization elements
        this.crownTopLine = null;
        this.crownTopText = null;

        // Initialize
        this.initEventListeners();
        this.resizeCanvas();
    }

    initEventListeners() {
        // Button event listeners
        this.loadImageBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.cropBtn.addEventListener('click', () => this.cropImage());
        this.resetBtn.addEventListener('click', () => this.resetView());
        this.saveBtn.addEventListener('click', () => this.saveImage());
        this.backBtn.addEventListener('click', () => {
            this.resultContainer.style.display = 'none';
            this.cropBtn.disabled = false;
        });
        this.standardSelector.addEventListener('change', (e) => this.handleStandardChange(e));

        // Window resize event
        window.addEventListener('resize', () => this.resizeCanvas());

        // Mouse wheel for zooming
        this.viewport.addEventListener('wheel', (e) => this.handleMouseWheel(e));

        // Mouse events for dragging landmarks
        this.overlay.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('mouseup', () => this.handleMouseUp());

        // Prevent context menu
        this.viewport.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    resizeCanvas() {
        const container = this.canvas.parentElement;
        this.viewportWidth = container.clientWidth;
        this.viewportHeight = container.clientHeight;
        this.canvas.width = this.viewportWidth;
        this.canvas.height = this.viewportHeight;

        console.log(`Canvas resized to ${this.viewportWidth}x${this.viewportHeight}`);

        if (this.image) {
            console.log('Image exists, rendering after resize');
            this.renderImage();
            this.renderLandmarks();
        } else {
            console.log('No image to render after resize');
        }
    }

    handleStandardChange(e) {
        const standardId = e.target.value;
        if (!standardId) {
            this.standardId = null;
            this.photoDimensions = defaultPhotoDimensions;
            return;
        }

        this.standardId = standardId;
        this.spinner.style.display = 'block';

        // Fetch the standard details
        fetch(`/photostandards/${standardId}`)
            .then(response => response.json())
            .then(data => {
                if (data.standard) {
                    const standard = data.standard;
                    // Create a PhotoDimensions object from the standard
                    this.photoDimensions = new PhotoDimensions(
                        standard.dimensions.pictureWidth,
                        standard.dimensions.pictureHeight,
                        standard.dimensions.units,
                        standard.dimensions.faceHeight,
                        standard.dimensions.crownTop || 0,
                        standard.dimensions.dpi,
                        standard.dimensions.bottomEyeLine || 0
                    );

                    // Update the crop rectangle
                    this.renderLandmarks();
                    this.spinner.style.display = 'none';
                } else {
                    console.error('Invalid standard data format:', data);
                    this.spinner.style.display = 'none';
                    alert('Error: Invalid standard data format');
                }
            })
            .catch(error => {
                console.error('Error loading photo standard:', error);
                this.spinner.style.display = 'none';
                alert('Error loading photo standard: ' + error.message);
            });
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;

        this.spinner.style.display = 'block';

        // Create a FormData object
        const formData = new FormData();
        formData.append('file', file);

        // Upload the image to the server
        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            this.imageId = data.image_id;

            // Load the image
            this.image = new Image();
            this.image.onload = () => {
                this.imageWidth = this.image.naturalWidth;
                this.imageHeight = this.image.naturalHeight;
                this.zoomFit();
                this.initLandmarks();
                this.renderImage();
                this.renderLandmarks();
                this.spinner.style.display = 'none';
                this.cropBtn.disabled = false;
                console.log(`Image loaded successfully: ${this.imageWidth}x${this.imageHeight}`);
            };
            this.image.onerror = (e) => {
                console.error(`Error loading image: ${e}`);
                this.spinner.style.display = 'none';
                alert('Error loading image. Please try again.');
            };
            this.image.src = `/images/${this.imageId}`;
            console.log(`Loading image from: /images/${this.imageId}`);

            // Detect landmarks
            fetch(`/detect/${this.imageId}`)
                .then(response => response.json())
                .then(landmarksData => {
                    if (landmarksData.landmarks && landmarksData.landmarks.crown_chin) {
                        // Set the crown and chin points
                        this.crownPoint = new Point(
                            landmarksData.landmarks.crown_chin.crownPoint.x,
                            landmarksData.landmarks.crown_chin.crownPoint.y
                        );
                        this.chinPoint = new Point(
                            landmarksData.landmarks.crown_chin.chinPoint.x,
                            landmarksData.landmarks.crown_chin.chinPoint.y
                        );
                        this.renderLandmarks();
                    }
                })
                .catch(error => {
                    console.error('Error detecting landmarks:', error);
                });
        })
        .catch(error => {
            console.error('Error uploading image:', error);
            this.spinner.style.display = 'none';
            alert('Error uploading image: ' + error.message);
        });
    }

    initLandmarks() {
        // Place landmarks at reasonable initial positions
        const centerX = this.imageWidth / 2;
        const topThird = this.imageHeight / 3;
        const bottomThird = this.imageHeight * 2 / 3;

        this.crownPoint = new Point(centerX, topThird);
        this.chinPoint = new Point(centerX, bottomThird);

        // Make landmarks visible
        this.crownMark.style.visibility = 'visible';
        this.chinMark.style.visibility = 'visible';
        this.crownText.style.visibility = 'visible';
        this.chinText.style.visibility = 'visible';

        // Hide face ellipse, face height text, rotation text, warning, and measurements panel initially
        this.faceEllipse.style.visibility = 'hidden';
        this.faceHeightText.style.visibility = 'hidden';
        this.rotationText.style.visibility = 'hidden';
        this.warningText.style.visibility = 'hidden';

        // Hide measurements panel
        this.measurementsPanel.style.visibility = 'hidden';
        this.measurementsTitle.style.visibility = 'hidden';
        this.faceHeightMeasurement.style.visibility = 'hidden';
        this.crownTopMeasurement.style.visibility = 'hidden';
        this.faceWidthMeasurement.style.visibility = 'hidden';
        this.photoSizeMeasurement.style.visibility = 'hidden';
    }

    zoomFit() {
        this.zoom = 1;
        this.ratio = this.computeMinScale(this.imageWidth, this.imageHeight);
        this.xLeft = this.viewportWidth / 2 - (this.ratio * this.imageWidth) / 2;
        this.yTop = this.viewportHeight / 2 - (this.ratio * this.imageHeight) / 2;
    }

    computeMinScale(width, height) {
        const xRatio = this.viewportWidth / width;
        const yRatio = this.viewportHeight / height;
        return Math.min(xRatio, yRatio) * 0.9; // 90% to leave some margin
    }

    renderImage() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        if (!this.image) {
            console.log('No image to render');
            return;
        }

        const scaledWidth = this.imageWidth * this.ratio * this.zoom;
        const scaledHeight = this.imageHeight * this.ratio * this.zoom;

        console.log(`Rendering image at (${this.xLeft}, ${this.yTop}) with dimensions ${scaledWidth}x${scaledHeight}`);

        try {
            this.ctx.drawImage(this.image, this.xLeft, this.yTop, scaledWidth, scaledHeight);
            console.log('Image rendered successfully');
        } catch (e) {
            console.error(`Error rendering image: ${e}`);
        }
    }

    renderLandmarks() {
        if (!this.image || !this.crownPoint || !this.chinPoint) return;

        // Convert image coordinates to screen coordinates
        const crownScreen = this.pixelToScreen(this.crownPoint);
        const chinScreen = this.pixelToScreen(this.chinPoint);

        // Position landmarks
        this.positionLandmark(this.crownMark, crownScreen);
        this.positionLandmark(this.chinMark, chinScreen);

        // Position text labels
        this.crownText.setAttribute('x', crownScreen.x);
        this.crownText.setAttribute('y', crownScreen.y - 20);
        this.chinText.setAttribute('x', chinScreen.x);
        this.chinText.setAttribute('y', chinScreen.y + 30);

        // Draw middle line
        const middle = middlePoint(this.crownPoint, this.chinPoint);
        const middleScreen = this.pixelToScreen(middle);
        this.middleLine.setAttribute('x1', crownScreen.x);
        this.middleLine.setAttribute('y1', crownScreen.y);
        this.middleLine.setAttribute('x2', chinScreen.x);
        this.middleLine.setAttribute('y2', chinScreen.y);

        // Calculate angle for perpendicular lines
        const angle = Math.atan2(chinScreen.y - crownScreen.y, chinScreen.x - crownScreen.x);
        const perpAngle = angle - Math.PI / 2;
        const lineLength = crownScreen.distTo(chinScreen) * 0.3; // 30% of face height

        // Draw crown perpendicular line
        const crownPerpX1 = crownScreen.x - Math.cos(perpAngle) * lineLength;
        const crownPerpY1 = crownScreen.y - Math.sin(perpAngle) * lineLength;
        const crownPerpX2 = crownScreen.x + Math.cos(perpAngle) * lineLength;
        const crownPerpY2 = crownScreen.y + Math.sin(perpAngle) * lineLength;

        this.crownLine.setAttribute('x1', crownPerpX1);
        this.crownLine.setAttribute('y1', crownPerpY1);
        this.crownLine.setAttribute('x2', crownPerpX2);
        this.crownLine.setAttribute('y2', crownPerpY2);

        // Draw chin perpendicular line
        const chinPerpX1 = chinScreen.x - Math.cos(perpAngle) * lineLength;
        const chinPerpY1 = chinScreen.y - Math.sin(perpAngle) * lineLength;
        const chinPerpX2 = chinScreen.x + Math.cos(perpAngle) * lineLength;
        const chinPerpY2 = chinScreen.y + Math.sin(perpAngle) * lineLength;

        this.chinLine.setAttribute('x1', chinPerpX1);
        this.chinLine.setAttribute('y1', chinPerpY1);
        this.chinLine.setAttribute('x2', chinPerpX2);
        this.chinLine.setAttribute('y2', chinPerpY2);

        // Calculate face height and angle in degrees
        const faceHeight = crownScreen.distTo(chinScreen);
        const angleDeg = (angle * 180) / Math.PI;

        // Display rotation angle
        // Adjust angle to show rotation from vertical (90 degrees offset)
        const rotationAngle = Math.round(90 - angleDeg);
        this.rotationText.textContent = `Rotation: ${rotationAngle}°`;
        this.rotationText.setAttribute('x', crownScreen.x - 100);
        this.rotationText.setAttribute('y', crownScreen.y);
        this.rotationText.style.visibility = 'visible';

        // Draw face ellipse
        const ra = faceHeight / 2;  // Major axis radius (half of face height)
        const rb = 0.68 * ra;       // Minor axis radius (approximation for face width)
        const pc = middleScreen;    // Center point of the ellipse

        this.faceEllipse.setAttribute('rx', ra);
        this.faceEllipse.setAttribute('ry', rb);
        this.faceEllipse.setAttribute('cx', pc.x);
        this.faceEllipse.setAttribute('cy', pc.y);
        this.faceEllipse.setAttribute('transform', `rotate(${angleDeg}, ${pc.x}, ${pc.y})`);

        // Make face ellipse visible
        this.faceEllipse.style.visibility = 'visible';

        // Calculate and display face height percentage
        const p = this.photoDimensions;
        const faceHeightPercentage = (p.faceHeight / p.pictureHeight * 100).toFixed(1);
        this.faceHeightText.textContent = `Face: ${faceHeightPercentage}%`;
        this.faceHeightText.setAttribute('x', pc.x + 10);
        this.faceHeightText.setAttribute('y', pc.y);
        this.faceHeightText.style.visibility = 'visible';

        // Check if proportions meet standards and show warning if needed
        const proportionsOk = this.checkProportions();
        if (!proportionsOk) {
            this.warningText.style.visibility = 'visible';
            this.cropRect.setAttribute('stroke', 'red');
        } else {
            this.warningText.style.visibility = 'hidden';
            this.cropRect.setAttribute('stroke', 'lightgray');
        }

        // Update and show measurements panel
        this.updateMeasurements();

        // Draw crop rectangle
        this.renderCropRect();
    }

    renderCropRect() {
        if (!this.crownPoint || !this.chinPoint || this.crownPoint.equals(this.chinPoint)) return;

        const p = this.photoDimensions;
        const rotRect = getCroppingRectangle(p, this.crownPoint, this.chinPoint);
        const corners = rotRect.corners();

        // Convert corners to screen coordinates
        const screenCorners = corners.map(corner => this.pixelToScreen(corner));

        // Log the screen corners for debugging
        console.log("Screen corners:", screenCorners);

        // Create path for crop rectangle
        let pathData = `M ${screenCorners[0].x} ${screenCorners[0].y}`;
        for (let i = 1; i < screenCorners.length; i++) {
            pathData += ` L ${screenCorners[i].x} ${screenCorners[i].y}`;
        }
        pathData += ' Z';

        // Update crop rectangle
        this.cropRect.setAttribute('d', pathData);

        // Calculate and display the crown-to-top distance
        if (p.crownTop > 0) {
            // Calculate the face height in pixels
            const faceHeightPix = this.crownPoint.distTo(this.chinPoint);

            // Calculate the ratio between image pixels and photo dimensions
            const mmToPixRatio = faceHeightPix / p.faceHeight;

            // Calculate the crown-to-top distance in pixels
            const crownTopPix = p.crownTop * mmToPixRatio;

            // Get screen coordinates for crown and chin points
            const crownScreen = this.pixelToScreen(this.crownPoint);
            const chinScreen = this.pixelToScreen(this.chinPoint);

            // Calculate the angle of the face line
            const angle = Math.atan2(chinScreen.y - crownScreen.y, chinScreen.x - crownScreen.x);

            // Calculate the perpendicular angle (90 degrees rotated)
            const perpAngle = angle - Math.PI / 2;

            // Calculate the top point of the crop box (perpendicular to the face line)
            const topPoint = new Point(
                this.crownPoint.x - Math.cos(perpAngle) * crownTopPix,
                this.crownPoint.y - Math.sin(perpAngle) * crownTopPix
            );

            // Convert to screen coordinates
            const topScreen = this.pixelToScreen(topPoint);

            // Draw crown-to-top line
            if (!this.crownTopLine) {
                this.crownTopLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                this.crownTopLine.setAttribute('stroke', 'orange');
                this.crownTopLine.setAttribute('stroke-width', '2');
                this.crownTopLine.setAttribute('stroke-dasharray', '5,5');
                this.overlay.appendChild(this.crownTopLine);

                // Add text label for crown-to-top distance
                this.crownTopText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                this.crownTopText.setAttribute('fill', 'orange');
                this.crownTopText.setAttribute('font-size', '12');
                this.crownTopText.textContent = `${p.crownTop}${p.units}`;
                this.overlay.appendChild(this.crownTopText);
            } else {
                // Update text content in case the standard changed
                this.crownTopText.textContent = `${p.crownTop}${p.units}`;
            }

            // Update crown-to-top line position
            this.crownTopLine.setAttribute('x1', crownScreen.x);
            this.crownTopLine.setAttribute('y1', crownScreen.y);
            this.crownTopLine.setAttribute('x2', topScreen.x);
            this.crownTopLine.setAttribute('y2', topScreen.y);

            // Update crown-to-top text position
            this.crownTopText.setAttribute('x', (crownScreen.x + topScreen.x) / 2 + 10);
            this.crownTopText.setAttribute('y', (crownScreen.y + topScreen.y) / 2);

            // Remove chin-to-bottom line if it exists
            if (this.chinBottomLine) {
                this.overlay.removeChild(this.chinBottomLine);
                this.overlay.removeChild(this.chinBottomText);
                this.chinBottomLine = null;
                this.chinBottomText = null;
            }
        } else if (this.crownTopLine) {
            // Hide the lines if crownTop is not defined
            this.overlay.removeChild(this.crownTopLine);
            this.overlay.removeChild(this.crownTopText);
            this.crownTopLine = null;
            this.crownTopText = null;

            if (this.chinBottomLine) {
                this.overlay.removeChild(this.chinBottomLine);
                this.overlay.removeChild(this.chinBottomText);
                this.chinBottomLine = null;
                this.chinBottomText = null;
            }
        }
    }

    positionLandmark(element, point) {
        element.setAttribute('cx', point.x);
        element.setAttribute('cy', point.y);
    }

    pixelToScreen(point) {
        return new Point(
            this.xLeft + point.x * this.ratio * this.zoom,
            this.yTop + point.y * this.ratio * this.zoom
        );
    }

    screenToPixel(point) {
        return new Point(
            (point.x - this.xLeft) / (this.ratio * this.zoom),
            (point.y - this.yTop) / (this.ratio * this.zoom)
        );
    }

    handleMouseWheel(e) {
        e.preventDefault();

        if (!this.image) return;

        const rect = this.viewport.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const scale = e.deltaY < 0 ? 1.1 : 1 / 1.1;
        let newZoom = this.zoom * scale;

        // Limit zoom
        if (newZoom < 0.1) newZoom = 0.1;
        if (newZoom > 10) newZoom = 10;

        const mousePoint = this.screenToPixel(new Point(mouseX, mouseY));
        const effectiveScale = this.ratio * (this.zoom - newZoom);

        this.yTop += mousePoint.y * effectiveScale;
        this.xLeft += mousePoint.x * effectiveScale;
        this.zoom = newZoom;

        this.renderImage();
        this.renderLandmarks();
    }

    handleMouseDown(e) {
        const rect = this.viewport.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Check if clicking on a landmark
        const crownScreen = this.pixelToScreen(this.crownPoint);
        const chinScreen = this.pixelToScreen(this.chinPoint);

        const crownDist = Math.sqrt(Math.pow(mouseX - crownScreen.x, 2) + Math.pow(mouseY - crownScreen.y, 2));
        const chinDist = Math.sqrt(Math.pow(mouseX - chinScreen.x, 2) + Math.pow(mouseY - chinScreen.y, 2));

        const landmarkRadius = 12; // Same as in CSS

        // Initialize smooth dragging variables
        this.lastMouseX = mouseX;
        this.lastMouseY = mouseY;
        this.velocityX = 0;
        this.velocityY = 0;

        if (crownDist <= landmarkRadius) {
            this.isDragging = true;
            this.dragTarget = 'crown';
            // Change cursor to grabbing
            this.crownMark.style.cursor = 'grabbing';
        } else if (chinDist <= landmarkRadius) {
            this.isDragging = true;
            this.dragTarget = 'chin';
            // Change cursor to grabbing
            this.chinMark.style.cursor = 'grabbing';
        } else {
            // Start panning
            this.isDragging = true;
            this.dragTarget = 'canvas';
            this.dragStartX = mouseX;
            this.dragStartY = mouseY;
            // Change cursor to grabbing
            this.viewport.style.cursor = 'grabbing';
        }
    }

    handleMouseMove(e) {
        if (!this.isDragging) return;

        const rect = this.viewport.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Calculate mouse movement distance
        const dx = mouseX - this.lastMouseX;
        const dy = mouseY - this.lastMouseY;

        // Calculate velocity (for inertia)
        this.velocityX = dx;
        this.velocityY = dy;

        // Check if movement exceeds threshold
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance < this.dragThreshold && this.dragTarget !== 'canvas') {
            return; // Ignore small movements for landmarks (reduces jitter)
        }

        // Apply smoothing
        const smoothX = this.lastMouseX + dx * this.smoothingFactor;
        const smoothY = this.lastMouseY + dy * this.smoothingFactor;

        if (this.dragTarget === 'crown') {
            // Apply smoothing to crown point movement
            this.crownPoint = this.screenToPixel(new Point(smoothX, smoothY));
            this.renderLandmarks();
        } else if (this.dragTarget === 'chin') {
            // Apply smoothing to chin point movement
            this.chinPoint = this.screenToPixel(new Point(smoothX, smoothY));
            this.renderLandmarks();
        } else if (this.dragTarget === 'canvas') {
            // Pan the canvas
            this.xLeft += mouseX - this.dragStartX;
            this.yTop += mouseY - this.dragStartY;
            this.dragStartX = mouseX;
            this.dragStartY = mouseY;
            this.renderImage();
            this.renderLandmarks();
        }

        // Update last mouse position
        this.lastMouseX = mouseX;
        this.lastMouseY = mouseY;
    }

    handleMouseUp() {
        // Reset cursors
        this.crownMark.style.cursor = 'grab';
        this.chinMark.style.cursor = 'grab';
        this.viewport.style.cursor = 'default';

        // Apply inertia if dragging a landmark
        if (this.isDragging && (this.dragTarget === 'crown' || this.dragTarget === 'chin')) {
            const target = this.dragTarget;
            const vx = this.velocityX;
            const vy = this.velocityY;

            // Only apply inertia if there's significant velocity
            if (Math.abs(vx) > 1 || Math.abs(vy) > 1) {
                // Apply inertia with decay
                let frame = 0;
                const applyInertia = () => {
                    frame++;
                    if (frame > 10) return; // Stop after 10 frames

                    // Calculate decay factor (slows down over time)
                    const decay = 0.8 - (frame * 0.07);
                    if (decay <= 0) return;

                    // Apply inertia with decay
                    const inertiaX = vx * decay;
                    const inertiaY = vy * decay;

                    // Apply movement
                    const point = target === 'crown' ? this.crownPoint : this.chinPoint;
                    const screenPoint = this.pixelToScreen(point);
                    const newScreenPoint = new Point(
                        screenPoint.x + inertiaX,
                        screenPoint.y + inertiaY
                    );
                    const newPoint = this.screenToPixel(newScreenPoint);

                    // Update point
                    if (target === 'crown') {
                        this.crownPoint = newPoint;
                    } else {
                        this.chinPoint = newPoint;
                    }

                    this.renderLandmarks();

                    // Continue inertia
                    requestAnimationFrame(applyInertia);
                };

                // Start inertia animation
                requestAnimationFrame(applyInertia);
            }
        }

        this.isDragging = false;
        this.dragTarget = null;
    }

    updateMeasurements() {
        if (!this.photoDimensions || !this.crownPoint || !this.chinPoint) return;

        const p = this.photoDimensions;
        const faceHeightPix = this.crownPoint.distTo(this.chinPoint);
        const mmToPixRatio = faceHeightPix / p.faceHeight;

        // Calculate face width (approximation based on face height)
        const faceWidthMm = p.faceHeight * 0.68;

        // Calculate crown to top distance
        const crownTopMm = p.crownTop;

        // Calculate face rotation angle
        const angle = Math.atan2(this.chinPoint.y - this.crownPoint.y, this.chinPoint.x - this.crownPoint.x);
        const angleDeg = (angle * 180) / Math.PI;
        const rotationAngle = Math.round(90 - angleDeg);

        // Update measurement texts
        this.faceHeightMeasurement.textContent = `Face Height: ${p.faceHeight.toFixed(1)}${p.units}`;
        this.crownTopMeasurement.textContent = `Crown to Top: ${crownTopMm.toFixed(1)}${p.units}`;
        this.faceWidthMeasurement.textContent = `Face Width: ${faceWidthMm.toFixed(1)}${p.units}`;
        this.photoSizeMeasurement.textContent = `Photo Size: ${p.pictureWidth.toFixed(1)}x${p.pictureHeight.toFixed(1)}${p.units} (${rotationAngle}°)`;

        // Show measurements panel
        this.measurementsPanel.style.visibility = 'visible';
        this.measurementsTitle.style.visibility = 'visible';
        this.faceHeightMeasurement.style.visibility = 'visible';
        this.crownTopMeasurement.style.visibility = 'visible';
        this.faceWidthMeasurement.style.visibility = 'visible';
        this.photoSizeMeasurement.style.visibility = 'visible';
    }

    checkProportions() {
        if (!this.photoDimensions || !this.crownPoint || !this.chinPoint) return true;

        const p = this.photoDimensions;
        const faceHeightPix = this.crownPoint.distTo(this.chinPoint);
        const mmToPixRatio = faceHeightPix / p.faceHeight;

        // Calculate the actual face height percentage
        const actualFaceHeightPercentage = (p.faceHeight / p.pictureHeight) * 100;

        // Define acceptable range (±5% of the standard's face height percentage)
        const minAcceptable = actualFaceHeightPercentage - 5;
        const maxAcceptable = actualFaceHeightPercentage + 5;

        // Calculate the current face height percentage based on the crop box
        const cropHeightPix = p.pictureHeight * mmToPixRatio;
        const currentFaceHeightPercentage = (faceHeightPix / cropHeightPix) * 100;

        // Check if the crop box is outside the image boundaries
        const rotRect = getCroppingRectangle(p, this.crownPoint, this.chinPoint);
        const corners = rotRect.corners();

        // Log the corners for debugging
        console.log("Crop box corners:", corners);
        console.log("Image dimensions:", this.imageWidth, "x", this.imageHeight);

        // Check if any corner is outside the image
        const isOutsideBounds = corners.some(corner => {
            const outside = (
                corner.x < 0 ||
                corner.y < 0 ||
                corner.x > this.imageWidth ||
                corner.y > this.imageHeight
            );
            if (outside) {
                console.log("Corner outside bounds:", corner);
            }
            return outside;
        });

        if (isOutsideBounds) {
            // Update warning text to indicate the crop box is outside the image
            this.warningText.textContent = "Warning: Crop box is outside the image boundaries";
            return false;
        } else {
            // Reset warning text to the default message
            this.warningText.textContent = "Warning: Face proportion does not meet standard requirements";
        }

        // Check if the current percentage is within the acceptable range
        return currentFaceHeightPercentage >= minAcceptable && currentFaceHeightPercentage <= maxAcceptable;
    }

    resetView() {
        if (!this.image) return;

        // Remove distance visualization elements if they exist
        if (this.crownTopLine) {
            this.overlay.removeChild(this.crownTopLine);
            this.overlay.removeChild(this.crownTopText);
            this.crownTopLine = null;
            this.crownTopText = null;
        }

        // Reset face ellipse, face height text, rotation text, and warning
        this.faceEllipse.style.visibility = 'hidden';
        this.faceHeightText.style.visibility = 'hidden';
        this.rotationText.style.visibility = 'hidden';
        this.warningText.style.visibility = 'hidden';

        // Hide measurements panel
        this.measurementsPanel.style.visibility = 'hidden';
        this.measurementsTitle.style.visibility = 'hidden';
        this.faceHeightMeasurement.style.visibility = 'hidden';
        this.crownTopMeasurement.style.visibility = 'hidden';
        this.faceWidthMeasurement.style.visibility = 'hidden';
        this.photoSizeMeasurement.style.visibility = 'hidden';

        this.zoomFit();
        this.initLandmarks();
        this.renderImage();
        this.renderLandmarks();
        this.resultContainer.style.display = 'none';
        this.cropBtn.disabled = false;
    }

    cropImage() {
        if (!this.image || !this.crownPoint || !this.chinPoint || this.crownPoint.equals(this.chinPoint)) return;
        if (!this.standardId) {
            alert('Please select a document type first');
            return;
        }

        // Calculate dimensions for cropped image
        const p = this.photoDimensions;
        const width = p.pictureWidth * p.dpi;
        const height = p.pictureHeight * p.dpi;

        // Create canvas for cropped image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        // Calculate crop rectangle
        const rotRect = getCroppingRectangle(p, this.crownPoint, this.chinPoint);
        const fromPoints = rotRect.corners();
        const toPoints = [
            new Point(0, height),
            new Point(width, height),
            new Point(width, 0),
            new Point(0, 0)
        ];

        // Apply affine transform
        const [a, b, c, d, e, f] = getAffineTransform(fromPoints, toPoints);
        ctx.save();
        ctx.transform(a, b, c, d, e, f);
        ctx.drawImage(this.image, 0, 0);
        ctx.restore();

        // Display cropped image
        this.croppedImage.src = canvas.toDataURL();
        this.croppedImageData = canvas.toDataURL('image/jpeg');
        this.resultContainer.style.display = 'block';
        this.cropBtn.disabled = true;
    }

    saveImage() {
        if (!this.croppedImageData || !this.imageId || !this.standardId) {
            alert('Please crop the image first');
            return;
        }

        this.spinner.style.display = 'block';

        // Convert data URL to blob
        fetch(this.croppedImageData)
            .then(res => res.blob())
            .then(blob => {
                // Create form data
                const formData = new FormData();
                formData.append('image_id', this.imageId);
                formData.append('standard_id', this.standardId);
                formData.append('crown_x', this.crownPoint.x);
                formData.append('crown_y', this.crownPoint.y);
                formData.append('chin_x', this.chinPoint.x);
                formData.append('chin_y', this.chinPoint.y);

                // Send to server
                return fetch('/crop', {
                    method: 'POST',
                    body: formData
                });
            })
            .then(response => response.json())
            .then(data => {
                this.spinner.style.display = 'none';
                if (data.processed_image_id) {
                    alert('Image saved successfully!');
                    // Redirect to the next page or show the result
                    window.location.href = `/edit?image_id=${data.processed_image_id}`;
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                this.spinner.style.display = 'none';
                console.error('Error saving image:', error);
                alert('Error saving image: ' + error.message);
            });
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new CanvasCropApp();
});
