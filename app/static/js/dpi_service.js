/**
 * DPI Service for managing DPI-related operations in the frontend.
 */

class DpiService {
    /**
     * Get the optimal DPI value between two DPI values.
     * @param {number} standardDpi - DPI value from the photo standard
     * @param {number} printDefDpi - DPI value from the print definition
     * @returns {number} The higher DPI value
     */
    static getOptimalDpi(standardDpi, printDefDpi) {
        return Math.max(standardDpi, printDefDpi);
    }

    /**
     * Get the recommended DPI for a specific printer type.
     * @param {string} printerType - Type of printer (inkjet, laser, photo, professional)
     * @returns {number} Recommended DPI value
     */
    static getRecommendedDpi(printerType) {
        switch (printerType) {
            case 'inkjet':
                return 300;
            case 'laser':
                return 600;
            case 'photo':
                return 300;
            case 'professional':
                return 1200;
            default:
                return 300; // Default
        }
    }

    /**
     * Convert a value from a physical unit to pixels based on DPI.
     * @param {number} value - Value to convert
     * @param {string} unit - Unit of measurement (pixel, inch, mm, cm)
     * @param {number} dpi - DPI value
     * @returns {number} Value in pixels
     */
    static convertToPixels(value, unit, dpi) {
        if (unit === 'pixel') {
            return value;
        } else if (unit === 'inch') {
            return value * dpi;
        } else if (unit === 'mm') {
            return value * dpi / 25.4;
        } else if (unit === 'cm') {
            return value * dpi / 2.54;
        } else {
            throw new Error(`Unknown unit: ${unit}`);
        }
    }

    /**
     * Convert a value from pixels to a physical unit based on DPI.
     * @param {number} value - Value in pixels
     * @param {string} unit - Target unit of measurement (pixel, inch, mm, cm)
     * @param {number} dpi - DPI value
     * @returns {number} Value in the specified unit
     */
    static convertFromPixels(value, unit, dpi) {
        if (unit === 'pixel') {
            return value;
        } else if (unit === 'inch') {
            return value / dpi;
        } else if (unit === 'mm') {
            return value / dpi * 25.4;
        } else if (unit === 'cm') {
            return value / dpi * 2.54;
        } else {
            throw new Error(`Unknown unit: ${unit}`);
        }
    }

    /**
     * Convert a value from one unit to another based on DPI.
     * @param {number} value - Value to convert
     * @param {string} fromUnit - Source unit of measurement
     * @param {string} toUnit - Target unit of measurement
     * @param {number} dpi - DPI value
     * @returns {number} Converted value
     */
    static convertUnits(value, fromUnit, toUnit, dpi) {
        if (fromUnit === toUnit) {
            return value;
        }
        
        // Convert to pixels first
        const pixels = DpiService.convertToPixels(value, fromUnit, dpi);
        
        // Convert from pixels to target unit
        return DpiService.convertFromPixels(pixels, toUnit, dpi);
    }

    /**
     * Calculate the estimated file size for an image based on dimensions and DPI.
     * @param {number} width - Width in physical units
     * @param {number} height - Height in physical units
     * @param {string} units - Units of width and height (inch, mm, cm)
     * @param {number} dpi - DPI value
     * @param {string} format - Image format (jpeg, png)
     * @returns {number} Estimated file size in KB
     */
    static calculateEstimatedFileSize(width, height, units, dpi, format = 'jpeg') {
        // Convert dimensions to pixels
        const pixelWidth = DpiService.convertToPixels(width, units, dpi);
        const pixelHeight = DpiService.convertToPixels(height, units, dpi);
        
        // Calculate number of pixels
        const numPixels = pixelWidth * pixelHeight;
        
        // Estimate file size based on format
        // These are rough estimates and will vary based on image content
        if (format === 'jpeg') {
            // JPEG with quality 90
            return Math.round(numPixels * 0.25 / 1024);
        } else if (format === 'png') {
            // PNG (lossless)
            return Math.round(numPixels * 0.8 / 1024);
        } else {
            // Default
            return Math.round(numPixels * 0.3 / 1024);
        }
    }

    /**
     * Get a human-readable quality level based on DPI.
     * @param {number} dpi - DPI value
     * @returns {string} Quality level (Low, Standard, High, Ultra High)
     */
    static getQualityLevel(dpi) {
        if (dpi < 150) {
            return 'Low';
        } else if (dpi < 300) {
            return 'Standard';
        } else if (dpi < 600) {
            return 'High';
        } else {
            return 'Ultra High';
        }
    }

    /**
     * Embed DPI information in a data URL.
     * @param {string} dataUrl - Image data URL
     * @param {number} dpi - DPI value to embed
     * @returns {string} Data URL with embedded DPI information
     */
    static embedDpiInDataUrl(dataUrl, dpi) {
        const parts = dataUrl.split(',');
        const format = parts[0];
        const data = parts[1];
        
        if (format.includes('image/png')) {
            return DpiService._embedDpiInPngDataUrl(format, data, dpi);
        } else if (format.includes('image/jpeg')) {
            return DpiService._embedDpiInJpegDataUrl(format, data, dpi);
        } else {
            // Unsupported format, return original
            return dataUrl;
        }
    }

    /**
     * Embed DPI in PNG data URL.
     * @private
     */
    static _embedDpiInPngDataUrl(format, data, dpi) {
        // This is a simplified implementation
        // For a complete implementation, we would need to decode the base64,
        // modify the PNG chunks, and re-encode to base64
        
        // For now, we'll use the server-side endpoint to handle this
        console.warn('Client-side PNG DPI embedding not fully implemented. Using server-side fallback.');
        
        // Return the original data URL
        return `${format},${data}`;
    }

    /**
     * Embed DPI in JPEG data URL.
     * @private
     */
    static _embedDpiInJpegDataUrl(format, data, dpi) {
        // This is a simplified implementation
        // For a complete implementation, we would need to decode the base64,
        // modify the JFIF APP0 marker, and re-encode to base64
        
        // For now, we'll use the server-side endpoint to handle this
        console.warn('Client-side JPEG DPI embedding not fully implemented. Using server-side fallback.');
        
        // Return the original data URL
        return `${format},${data}`;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DpiService;
}
