// Global variables
let selectedLayout = null;
let printDefinitions = [];
let imageId = null;
let croppedImageId = null;
let tiledImageId = null;

// Get the image ID from URL parameters
function getImageIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('image_id');
}

// Initialize the page
document.addEventListener('DOMContentLoaded', async () => {
    // Get the image ID from URL
    imageId = getImageIdFromUrl();
    if (!imageId) {
        showError('No image ID provided. Please go back and crop an image first.');
        return;
    }

    // Load print definitions
    await loadPrintDefinitions();

    // Set up event listeners
    setupEventListeners();
});

// Load print definitions from the server
async function loadPrintDefinitions() {
    try {
        showSpinner();
        const response = await fetch('/api/print/definitions');
        if (!response.ok) {
            throw new Error(`Failed to load print definitions: ${response.statusText}`);
        }

        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }

        printDefinitions = data.print_definitions || [];
        renderPrintDefinitions();
    } catch (error) {
        showError(`Error loading print definitions: ${error.message}`);
    } finally {
        hideSpinner();
    }
}

// Render print definitions in the container
function renderPrintDefinitions() {
    const container = document.getElementById('printLayoutContainer');
    const addCustomLayout = document.getElementById('addCustomLayout');

    // Clear existing layouts except the "Add Custom Layout" button
    const children = Array.from(container.children);
    children.forEach(child => {
        if (child.id !== 'addCustomLayout') {
            container.removeChild(child);
        }
    });

    // Add print definitions
    printDefinitions.forEach(definition => {
        const layoutItem = createLayoutItem(definition);
        container.insertBefore(layoutItem, addCustomLayout);
    });
}

// Create a layout item element
function createLayoutItem(definition) {
    const layoutItem = document.createElement('div');
    layoutItem.className = 'print-layout-item';
    layoutItem.dataset.id = definition.id;

    // Create thumbnail
    const thumbnail = document.createElement('div');
    thumbnail.className = 'print-layout-thumbnail';
    
    // Create a visual representation of the print layout
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 200;
    canvas.height = 150;
    
    // Draw the layout representation
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw the paper outline
    ctx.strokeStyle = '#3498db';
    ctx.lineWidth = 2;
    
    // Calculate aspect ratio
    const aspectRatio = definition.width / definition.height;
    let width, height;
    
    if (aspectRatio > 1) {
        width = 180;
        height = width / aspectRatio;
    } else {
        height = 130;
        width = height * aspectRatio;
    }
    
    const x = (canvas.width - width) / 2;
    const y = (canvas.height - height) / 2;
    
    ctx.strokeRect(x, y, width, height);
    
    // Draw a sample photo inside
    const photoWidth = width * 0.4;
    const photoHeight = height * 0.4;
    const photoX = x + (width - photoWidth) / 2;
    const photoY = y + (height - photoHeight) / 2;
    
    ctx.fillStyle = '#e9ecef';
    ctx.fillRect(photoX, photoY, photoWidth, photoHeight);
    
    thumbnail.appendChild(canvas);
    layoutItem.appendChild(thumbnail);

    // Create title
    const title = document.createElement('div');
    title.className = 'print-layout-title';
    title.textContent = definition.title;
    layoutItem.appendChild(title);

    // Create details
    const details = document.createElement('div');
    details.className = 'print-layout-details';
    details.textContent = `${definition.width} × ${definition.height} ${definition.units}`;
    layoutItem.appendChild(details);

    // Add custom controls if it's a custom layout
    if (definition.custom) {
        const controls = document.createElement('div');
        controls.className = 'custom-controls';
        
        // Edit button
        const editBtn = document.createElement('button');
        editBtn.className = 'btn btn-sm btn-outline-secondary';
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = 'Edit';
        editBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            openEditModal(definition);
        });
        controls.appendChild(editBtn);
        
        // Delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn btn-sm btn-outline-danger';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = 'Delete';
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            deleteCustomLayout(definition.id);
        });
        controls.appendChild(deleteBtn);
        
        layoutItem.appendChild(controls);
    }

    // Add click event to select this layout
    layoutItem.addEventListener('click', () => {
        selectLayout(definition.id);
    });

    return layoutItem;
}

// Select a layout
function selectLayout(layoutId) {
    // Find the layout definition
    const definition = printDefinitions.find(def => def.id === layoutId);
    if (!definition) return;

    // Update selected layout
    selectedLayout = definition;

    // Update UI
    const layoutItems = document.querySelectorAll('.print-layout-item');
    layoutItems.forEach(item => {
        if (item.dataset.id === layoutId) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });

    // Enable next button
    document.getElementById('nextButton').disabled = false;

    // Generate preview
    generatePreview(layoutId);
}

// Generate a preview of the selected layout
async function generatePreview(layoutId) {
    try {
        showSpinner();
        
        // Create a tiled print using the selected layout
        const response = await fetch(`/api/print/create-tiled-print?image_id=${imageId}&print_definition_id=${layoutId}`);
        
        if (!response.ok) {
            throw new Error(`Failed to generate preview: ${response.statusText}`);
        }
        
        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Store the tiled image ID
        tiledImageId = data.tiled_image_id;
        
        // Show the preview container
        const previewContainer = document.getElementById('previewContainer');
        previewContainer.style.display = 'block';
        
        // Update the preview image
        const previewImage = document.getElementById('previewImage');
        previewImage.src = `/api/print/image/${tiledImageId}?t=${new Date().getTime()}`;
        
    } catch (error) {
        showError(`Error generating preview: ${error.message}`);
    } finally {
        hideSpinner();
    }
}

// Open the custom layout modal
function openCustomLayoutModal() {
    const modal = document.getElementById('customLayoutModal');
    modal.style.display = 'block';
    
    // Reset the form
    document.getElementById('customLayoutForm').reset();
}

// Open the edit modal for a custom layout
function openEditModal(definition) {
    const modal = document.getElementById('customLayoutModal');
    modal.style.display = 'block';
    
    // Fill the form with the definition values
    document.getElementById('layoutName').value = definition.title;
    document.getElementById('layoutWidth').value = definition.width;
    document.getElementById('layoutHeight').value = definition.height;
    document.getElementById('layoutGutter').value = definition.gutter;
    document.getElementById('layoutPadding').value = definition.padding;
    document.getElementById('layoutUnits').value = definition.units;
    document.getElementById('layoutResolution').value = definition.resolution;
    
    // Store the definition ID in the form
    document.getElementById('customLayoutForm').dataset.id = definition.id;
}

// Close the custom layout modal
function closeCustomLayoutModal() {
    const modal = document.getElementById('customLayoutModal');
    modal.style.display = 'none';
    
    // Clear the form dataset
    document.getElementById('customLayoutForm').dataset.id = '';
}

// Save a custom layout
async function saveCustomLayout(event) {
    event.preventDefault();
    
    try {
        showSpinner();
        
        const form = document.getElementById('customLayoutForm');
        const formData = new FormData(form);
        const layoutData = Object.fromEntries(formData.entries());
        
        // Convert numeric values
        layoutData.width = parseFloat(layoutData.width);
        layoutData.height = parseFloat(layoutData.height);
        layoutData.gutter = parseFloat(layoutData.gutter);
        layoutData.padding = parseFloat(layoutData.padding);
        layoutData.resolution = parseFloat(layoutData.resolution);
        
        // Check if we're editing an existing layout
        const layoutId = form.dataset.id;
        let response;
        
        if (layoutId) {
            // Update existing layout
            response = await fetch(`/api/print/definitions/${layoutId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(layoutData)
            });
        } else {
            // Create new layout
            response = await fetch('/api/print/definitions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(layoutData)
            });
        }
        
        if (!response.ok) {
            throw new Error(`Failed to save layout: ${response.statusText}`);
        }
        
        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Reload print definitions
        await loadPrintDefinitions();
        
        // Close the modal
        closeCustomLayoutModal();
        
    } catch (error) {
        showError(`Error saving layout: ${error.message}`);
    } finally {
        hideSpinner();
    }
}

// Delete a custom layout
async function deleteCustomLayout(layoutId) {
    if (!confirm('Are you sure you want to delete this custom layout?')) {
        return;
    }
    
    try {
        showSpinner();
        
        const response = await fetch(`/api/print/definitions/${layoutId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            throw new Error(`Failed to delete layout: ${response.statusText}`);
        }
        
        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Reload print definitions
        await loadPrintDefinitions();
        
        // If the deleted layout was selected, clear selection
        if (selectedLayout && selectedLayout.id === layoutId) {
            selectedLayout = null;
            document.getElementById('nextButton').disabled = true;
            document.getElementById('previewContainer').style.display = 'none';
        }
        
    } catch (error) {
        showError(`Error deleting layout: ${error.message}`);
    } finally {
        hideSpinner();
    }
}

// Set up event listeners
function setupEventListeners() {
    // Add custom layout button
    document.getElementById('addCustomLayout').addEventListener('click', openCustomLayoutModal);
    
    // Close modal button
    document.querySelector('.close-modal').addEventListener('click', closeCustomLayoutModal);
    
    // Cancel button in modal
    document.getElementById('cancelButton').addEventListener('click', closeCustomLayoutModal);
    
    // Custom layout form submission
    document.getElementById('customLayoutForm').addEventListener('submit', saveCustomLayout);
    
    // Back button
    document.getElementById('backButton').addEventListener('click', () => {
        window.location.href = `/crop?image_id=${imageId}`;
    });
    
    // Next button
    document.getElementById('nextButton').addEventListener('click', () => {
        if (selectedLayout && tiledImageId) {
            window.location.href = `/print-preview?image_id=${tiledImageId}`;
        }
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', (event) => {
        const modal = document.getElementById('customLayoutModal');
        if (event.target === modal) {
            closeCustomLayoutModal();
        }
    });
}

// Show spinner
function showSpinner() {
    document.getElementById('spinnerOverlay').style.visibility = 'visible';
}

// Hide spinner
function hideSpinner() {
    document.getElementById('spinnerOverlay').style.visibility = 'hidden';
}

// Show error message
function showError(message) {
    alert(message);
}
