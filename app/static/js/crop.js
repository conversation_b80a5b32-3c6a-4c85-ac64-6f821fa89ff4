/**
 * Crop component for the optimum project
 * This handles the image cropping functionality
 */

// Global variables
let originalImage = null;
let crownPoint = { x: 0, y: 0 };
let chinPoint = { x: 0, y: 0 };
let selectedStandard = null;
let cropBox = { x: 0, y: 0, width: 0, height: 0 };
let imageScale = 1;
let isDragging = false;
let dragStartX = 0;
let dragStartY = 0;
let dragElement = null;

// Initialize the crop component
function initCrop() {
    const fileInput = document.getElementById('photo-upload');
    const cropContainer = document.getElementById('crop-container');
    const previewContainer = document.getElementById('preview-container');
    const standardSelector = document.getElementById('standard-selector');

    // Event listeners
    fileInput.addEventListener('change', handleFileUpload);
    standardSelector.addEventListener('change', handleStandardChange);

    // Initialize the crop box
    initCropBox();

    // Make the crop box resizable and draggable
    makeCropBoxInteractive();
}

// Handle file upload
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Display loading indicator
    document.getElementById('loading-indicator').style.display = 'block';

    try {
        // Upload the image to the server
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/landmarks/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('Failed to upload image');
        }

        const data = await response.json();
        const imageId = data.image_id;

        // Detect landmarks
        const landmarksResponse = await fetch(`/api/landmarks/detect/${imageId}`);
        if (!landmarksResponse.ok) {
            throw new Error('Failed to detect landmarks');
        }

        const landmarksData = await landmarksResponse.json();

        // Set the crown and chin points
        if (landmarksData.landmarks && landmarksData.landmarks.crown_chin) {
            crownPoint = landmarksData.landmarks.crown_chin.crownPoint;
            chinPoint = landmarksData.landmarks.crown_chin.chinPoint;
        }

        // Load and display the image
        originalImage = new Image();
        originalImage.onload = function() {
            // Display the image
            const canvas = document.getElementById('crop-canvas');
            const ctx = canvas.getContext('2d');

            // Set canvas dimensions to match image
            canvas.width = originalImage.width;
            canvas.height = originalImage.height;

            // Draw the image
            ctx.drawImage(originalImage, 0, 0);

            // Draw landmarks
            drawLandmarks(ctx, crownPoint, chinPoint);

            // Update crop box based on landmarks and selected standard
            updateCropBox();

            // Hide loading indicator
            document.getElementById('loading-indicator').style.display = 'none';

            // Show the crop container
            document.getElementById('crop-container').style.display = 'block';
        };

        // Set the image source
        originalImage.src = `/api/landmarks/aligned-image/${imageId}`;

        // Store the image ID
        document.getElementById('image-id').value = imageId;

    } catch (error) {
        console.error('Error:', error);
        document.getElementById('loading-indicator').style.display = 'none';
        alert('Error uploading image: ' + error.message);
    }
}

// Handle standard change
function handleStandardChange(event) {
    const standardId = event.target.value;

    if (!standardId) {
        selectedStandard = null;
        localStorage.removeItem('currentStandardId');
        return;
    }

    // Store the selected standard ID for payment system
    localStorage.setItem('currentStandardId', standardId);

    // Fetch the standard details
    fetch(`/api/photostandards/${standardId}`)
        .then(response => response.json())
        .then(data => {
            // The API returns a wrapper object with a 'standard' property
            if (data.standard) {
                selectedStandard = data.standard;
                console.log("Loaded standard:", selectedStandard);
                updateCropBox();
                updatePreview();
            } else {
                console.error('Invalid standard data format:', data);
                alert('Error: Invalid standard data format');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading photo standard: ' + error.message);
        });
}

// Initialize the crop box
function initCropBox() {
    const cropBox = document.createElement('div');
    cropBox.id = 'crop-box';
    cropBox.className = 'crop-box';

    // Add resize handles
    const handles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];
    handles.forEach(handle => {
        const div = document.createElement('div');
        div.className = `resize-handle ${handle}`;
        div.dataset.handle = handle;
        cropBox.appendChild(div);
    });

    document.getElementById('crop-container').appendChild(cropBox);
}

// Make the crop box interactive (draggable and resizable)
function makeCropBoxInteractive() {
    const cropBox = document.getElementById('crop-box');

    // Mouse down event for dragging
    cropBox.addEventListener('mousedown', startDrag);

    // Mouse events for document to handle dragging
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);

    // Add event listeners for resize handles
    const handles = cropBox.querySelectorAll('.resize-handle');
    handles.forEach(handle => {
        handle.addEventListener('mousedown', startResize);
    });
}

// Draw landmarks on the canvas
function drawLandmarks(ctx, crown, chin) {
    // Draw crown point
    ctx.beginPath();
    ctx.arc(crown.x, crown.y, 5, 0, 2 * Math.PI);
    ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
    ctx.fill();

    // Draw chin point
    ctx.beginPath();
    ctx.arc(chin.x, chin.y, 5, 0, 2 * Math.PI);
    ctx.fillStyle = 'rgba(0, 0, 255, 0.7)';
    ctx.fill();

    // Draw line connecting crown and chin
    ctx.beginPath();
    ctx.moveTo(crown.x, crown.y);
    ctx.lineTo(chin.x, chin.y);
    ctx.strokeStyle = 'rgba(255, 255, 0, 0.7)';
    ctx.lineWidth = 2;
    ctx.stroke();
}

// Check if the crop box is outside the image boundaries
function isCropBoxOutsideBounds() {
    if (!originalImage || !cropBox) return false;

    // Check if any part of the crop box is outside the image
    return (
        cropBox.x < 0 ||
        cropBox.y < 0 ||
        cropBox.x + cropBox.width > originalImage.width ||
        cropBox.y + cropBox.height > originalImage.height
    );
}

// Update the crop box based on landmarks and selected standard
function updateCropBox() {
    if (!originalImage || !selectedStandard || !crownPoint || !chinPoint) {
        console.log("Missing data for crop box:", {
            originalImage: !!originalImage,
            selectedStandard: !!selectedStandard,
            crownPoint: !!crownPoint,
            chinPoint: !!chinPoint
        });

        if (selectedStandard) {
            console.log("Selected standard dimensions:", selectedStandard.dimensions);
        }
        return;
    }

    // Calculate face height in pixels
    const faceHeight = Math.sqrt(
        Math.pow(crownPoint.x - chinPoint.x, 2) +
        Math.pow(crownPoint.y - chinPoint.y, 2)
    );

    // Calculate scale factor
    const stdFaceHeight = convertToPixels(selectedStandard.dimensions.faceHeight,
                                         selectedStandard.dimensions.units,
                                         selectedStandard.dimensions.dpi);
    imageScale = stdFaceHeight / faceHeight;

    // Calculate crop dimensions
    const cropWidth = convertToPixels(selectedStandard.dimensions.pictureWidth,
                                     selectedStandard.dimensions.units,
                                     selectedStandard.dimensions.dpi) / imageScale;
    const cropHeight = convertToPixels(selectedStandard.dimensions.pictureHeight,
                                      selectedStandard.dimensions.units,
                                      selectedStandard.dimensions.dpi) / imageScale;

    // Calculate center point based on crown top specification
    let centerX, centerY;

    if (selectedStandard.dimensions.crownTop > 0) {
        // Calculate the ratio between image pixels and photo dimensions
        const mmToPixRatio = faceHeight / selectedStandard.dimensions.faceHeight;

        // Calculate crown top in pixels
        const crownTopPx = selectedStandard.dimensions.crownTop * mmToPixRatio;

        // Calculate the distance from crown to center of the photo
        const crownToCenterPx = (selectedStandard.dimensions.pictureHeight / 2 - selectedStandard.dimensions.crownTop) * mmToPixRatio;

        // Calculate the vector from crown to chin
        const crownToChinX = chinPoint.x - crownPoint.x;
        const crownToChinY = chinPoint.y - crownPoint.y;

        // Calculate the length of the crown-to-chin vector
        const crownToChinLength = Math.sqrt(crownToChinX * crownToChinX + crownToChinY * crownToChinY);

        // Calculate the unit vector from crown to chin
        const unitX = crownToChinX / crownToChinLength;
        const unitY = crownToChinY / crownToChinLength;

        // Calculate the center point by moving from crown towards chin
        centerX = crownPoint.x + unitX * crownToCenterPx;
        centerY = crownPoint.y + unitY * crownToCenterPx;

        console.log("Calculated center using crownTop:", {
            mmToPixRatio,
            crownTopPx,
            crownToCenterPx,
            unitX,
            unitY,
            centerX,
            centerY
        });
    } else {
        // Use the middle point between crown and chin
        centerX = (crownPoint.x + chinPoint.x) / 2;
        centerY = (crownPoint.y + chinPoint.y) / 2;
        console.log("Using middle point as center:", centerX, centerY);
    }

    // Set crop box position and dimensions
    cropBox = {
        x: centerX - cropWidth / 2,
        y: centerY - cropHeight / 2,
        width: cropWidth,
        height: cropHeight
    };

    console.log("Crop box dimensions:", cropBox);

    // Update the crop box element
    const cropBoxElement = document.getElementById('crop-box');
    cropBoxElement.style.left = cropBox.x + 'px';
    cropBoxElement.style.top = cropBox.y + 'px';
    cropBoxElement.style.width = cropBox.width + 'px';
    cropBoxElement.style.height = cropBox.height + 'px';

    // Check if crop box is outside image boundaries and update UI
    updateCropBoxBoundaryWarning();

    // Update preview
    updatePreview();
}

// Update the UI to show a warning if the crop box is outside the image boundaries
function updateCropBoxBoundaryWarning() {
    const cropBoxElement = document.getElementById('crop-box');
    const warningElement = document.getElementById('crop-warning');

    if (isCropBoxOutsideBounds()) {
        // Show warning
        cropBoxElement.classList.add('outside-bounds');
        warningElement.style.display = 'block';
    } else {
        // Hide warning
        cropBoxElement.classList.remove('outside-bounds');
        warningElement.style.display = 'none';
    }
}

// Convert units to pixels
function convertToPixels(value, units, dpi) {
    switch (units) {
        case 'pixel':
            return value;
        case 'inch':
            return value * dpi;
        case 'mm':
            return (value * dpi) / 25.4;
        case 'cm':
            return (value * dpi) / 2.54;
        default:
            throw new Error('Unknown units: ' + units);
    }
}

// Update the preview
function updatePreview() {
    if (!originalImage || !cropBox) return;

    const previewCanvas = document.getElementById('preview-canvas');
    const ctx = previewCanvas.getContext('2d');

    // Set preview canvas dimensions
    if (selectedStandard) {
        previewCanvas.width = convertToPixels(selectedStandard.dimensions.pictureWidth,
                                             selectedStandard.dimensions.units,
                                             selectedStandard.dimensions.dpi);
        previewCanvas.height = convertToPixels(selectedStandard.dimensions.pictureHeight,
                                              selectedStandard.dimensions.units,
                                              selectedStandard.dimensions.dpi);
    } else {
        previewCanvas.width = cropBox.width * imageScale;
        previewCanvas.height = cropBox.height * imageScale;
    }

    // Clear the canvas with white background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, previewCanvas.width, previewCanvas.height);

    // Log preview information for debugging
    console.log("Preview update:", {
        cropBox,
        previewWidth: previewCanvas.width,
        previewHeight: previewCanvas.height,
        imageScale
    });

    try {
        // Draw the cropped image
        ctx.drawImage(
            originalImage,
            cropBox.x, cropBox.y, cropBox.width, cropBox.height,
            0, 0, previewCanvas.width, previewCanvas.height
        );
        console.log("Preview drawn successfully");
    } catch (error) {
        console.error("Error drawing preview:", error);
        // Draw error message on canvas
        ctx.fillStyle = 'red';
        ctx.font = '14px Arial';
        ctx.fillText('Error drawing preview', 10, 30);
    }

    // Show the preview container
    document.getElementById('preview-container').style.display = 'block';
}

// Start dragging the crop box
function startDrag(event) {
    if (event.target.classList.contains('resize-handle')) return;

    isDragging = true;
    dragElement = 'box';
    dragStartX = event.clientX;
    dragStartY = event.clientY;

    event.preventDefault();
}

// Start resizing the crop box
function startResize(event) {
    isDragging = true;
    dragElement = event.target.dataset.handle;
    dragStartX = event.clientX;
    dragStartY = event.clientY;

    event.preventDefault();
    event.stopPropagation();
}

// Drag the crop box or resize it
function drag(event) {
    if (!isDragging) return;

    const cropBoxElement = document.getElementById('crop-box');
    const deltaX = event.clientX - dragStartX;
    const deltaY = event.clientY - dragStartY;

    if (dragElement === 'box') {
        // Move the crop box
        cropBox.x += deltaX;
        cropBox.y += deltaY;
    } else {
        // Resize the crop box
        resizeCropBox(dragElement, deltaX, deltaY);
    }

    // Update the crop box element
    cropBoxElement.style.left = cropBox.x + 'px';
    cropBoxElement.style.top = cropBox.y + 'px';
    cropBoxElement.style.width = cropBox.width + 'px';
    cropBoxElement.style.height = cropBox.height + 'px';

    // Check if crop box is outside image boundaries and update UI
    updateCropBoxBoundaryWarning();

    // Update preview
    updatePreview();

    dragStartX = event.clientX;
    dragStartY = event.clientY;
}

// Resize the crop box
function resizeCropBox(handle, deltaX, deltaY) {
    // Implement resizing logic based on the handle
    if (handle.includes('n')) {
        cropBox.y += deltaY;
        cropBox.height -= deltaY;
    }
    if (handle.includes('s')) {
        cropBox.height += deltaY;
    }
    if (handle.includes('w')) {
        cropBox.x += deltaX;
        cropBox.width -= deltaX;
    }
    if (handle.includes('e')) {
        cropBox.width += deltaX;
    }

    // Ensure minimum size
    if (cropBox.width < 50) cropBox.width = 50;
    if (cropBox.height < 50) cropBox.height = 50;
}

// Stop dragging
function stopDrag() {
    isDragging = false;
}

// Save the cropped image
function saveCroppedImage() {
    if (!originalImage || !cropBox) return;

    const imageId = document.getElementById('image-id').value;
    const standardId = document.getElementById('standard-selector').value;

    // Create a canvas to hold the cropped image
    const canvas = document.createElement('canvas');
    canvas.width = cropBox.width * imageScale;
    canvas.height = cropBox.height * imageScale;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(
        originalImage,
        cropBox.x, cropBox.y, cropBox.width, cropBox.height,
        0, 0, canvas.width, canvas.height
    );

    // Convert canvas to blob
    canvas.toBlob(blob => {
        // Create form data
        const formData = new FormData();
        formData.append('image', blob, 'cropped.jpg');
        formData.append('image_id', imageId);
        formData.append('standard_id', standardId);

        // Send to server
        fetch('/api/photo/crop', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Show success message
            alert('Image cropped successfully!');

            // Store the processed image ID
            if (data.processed_image_id) {
                // Show the print layout button
                const printLayoutBtn = document.getElementById('print-layout-btn');
                printLayoutBtn.dataset.imageId = data.processed_image_id;
                printLayoutBtn.style.display = 'inline-block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving cropped image: ' + error.message);
        });
    }, 'image/jpeg');
}

// Go to print layout page
function goToPrintLayout() {
    const printLayoutBtn = document.getElementById('print-layout-btn');
    const imageId = printLayoutBtn.dataset.imageId;

    if (imageId) {
        window.location.href = `/print?image_id=${imageId}`;
    } else {
        alert('No processed image available. Please crop the image first.');
    }
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initCrop);
