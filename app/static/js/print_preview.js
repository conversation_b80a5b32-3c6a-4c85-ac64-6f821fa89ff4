// Global variables
let imageId = null;
let printDefinition = null;

// Get the image ID from URL parameters
function getImageIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('image_id');
}

// Initialize the page
document.addEventListener('DOMContentLoaded', async () => {
    // Get the image ID from URL
    imageId = getImageIdFromUrl();
    if (!imageId) {
        showError('No image ID provided. Please go back and select a print layout first.');
        return;
    }

    // Load the print preview
    await loadPrintPreview();

    // Set up event listeners
    setupEventListeners();
});

// Load the print preview
async function loadPrintPreview() {
    try {
        showSpinner();

        // Load the image
        const previewImage = document.getElementById('previewImage');
        previewImage.src = `/api/print/image/${imageId}?t=${new Date().getTime()}`;

        // Wait for the image to load
        await new Promise((resolve, reject) => {
            previewImage.onload = resolve;
            previewImage.onerror = () => reject(new Error('Failed to load preview image'));
        });

        // Get the print definition ID from the image metadata
        await loadPrintDefinition();

    } catch (error) {
        showError(`Error loading print preview: ${error.message}`);
    } finally {
        hideSpinner();
    }
}

// Load the print definition for the current image
async function loadPrintDefinition() {
    try {
        // First, we need to get the print definition ID from the image metadata
        // This would typically be stored when the tiled print was created
        // For now, we'll just load all print definitions and use the first one as a placeholder
        const response = await fetch('/api/print/definitions');
        if (!response.ok) {
            throw new Error(`Failed to load print definitions: ${response.statusText}`);
        }

        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }

        if (!data.print_definitions || data.print_definitions.length === 0) {
            throw new Error('No print definitions available');
        }

        // Use the first print definition as a placeholder
        // In a real implementation, you would get the specific print definition used for this image
        printDefinition = data.print_definitions[0];

        // Display the print details
        displayPrintDetails();

    } catch (error) {
        showError(`Error loading print definition: ${error.message}`);
    }
}

// Display the print details
function displayPrintDetails() {
    if (!printDefinition) return;

    const detailsContainer = document.getElementById('printDetails');
    detailsContainer.innerHTML = '';

    // Create details items
    const details = [
        { label: 'Layout Name', value: printDefinition.title },
        { label: 'Dimensions', value: `${printDefinition.width} × ${printDefinition.height} ${printDefinition.units}` },
        { label: 'Resolution', value: `${printDefinition.resolution} DPI` },
        { label: 'Paper Type', value: 'Standard Photo Paper' },
        { label: 'Print Quality', value: 'High' }
    ];

    details.forEach(detail => {
        const detailItem = document.createElement('div');
        detailItem.className = 'print-details-item';

        const label = document.createElement('div');
        label.className = 'print-details-label';
        label.textContent = detail.label;

        const value = document.createElement('div');
        value.className = 'print-details-value';
        value.textContent = detail.value;

        detailItem.appendChild(label);
        detailItem.appendChild(value);
        detailsContainer.appendChild(detailItem);
    });
}

// Download the print image
function downloadPrintImage() {
    if (!imageId) {
        showError('No image ID available for download');
        return;
    }

    // Use payment system for print downloads
    if (window.paymentManager) {
        const standardId = localStorage.getItem('currentStandardId') || 'default';
        window.paymentManager.requestPayment(imageId, standardId, 'print');
    } else {
        showError('Payment system not available. Please refresh the page.');
    }
}

// Print the image
function printImage() {
    if (!imageId) {
        showError('No image ID available for printing');
        return;
    }

    // Create a new window with just the image
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
        showError('Pop-up blocked. Please allow pop-ups for this site to print.');
        return;
    }

    // Write the HTML content to the new window
    printWindow.document.write(`
        <html>
        <head>
            <title>Print Passport Photo</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                }
                img {
                    max-width: 100%;
                    max-height: 100vh;
                }
                @media print {
                    body {
                        margin: 0;
                        padding: 0;
                    }
                    img {
                        width: 100%;
                        height: auto;
                    }
                }
            </style>
        </head>
        <body>
            <img src="/api/print/image/${imageId}" alt="Passport Photo Print">
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                        window.close();
                    }, 500);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// Set up event listeners
function setupEventListeners() {
    // Back button
    document.getElementById('backButton').addEventListener('click', () => {
        window.history.back();
    });

    // Download button
    document.getElementById('downloadButton').addEventListener('click', downloadPrintImage);

    // Print button
    document.getElementById('printButton').addEventListener('click', printImage);
}

// Show spinner
function showSpinner() {
    document.getElementById('spinnerOverlay').style.visibility = 'visible';
}

// Hide spinner
function hideSpinner() {
    document.getElementById('spinnerOverlay').style.visibility = 'hidden';
}

// Show error message
function showError(message) {
    alert(message);
}
