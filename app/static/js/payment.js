/**
 * Payment Integration for Passport Photo Maker
 * Handles IntaSend payment flow for digital and print downloads
 */

class PaymentManager {
    constructor() {
        this.currentOrder = null;
        this.paymentWindow = null;
        this.checkStatusInterval = null;
        this.init();
    }

    init() {
        this.createPaymentModal();
        this.setupEventListeners();
    }

    createPaymentModal() {
        // Create payment modal HTML
        const modalHTML = `
            <div id="payment-modal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Complete Your Purchase</h2>
                        <span class="close" id="payment-modal-close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div id="payment-form">
                            <div class="payment-info">
                                <h3>Download High-Quality Passport Photo</h3>
                                <p>Get your professional passport photo without watermarks</p>
                                <div class="pricing-info">
                                    <div class="price-item">
                                        <span class="price-label">Digital Photo:</span>
                                        <span class="price-value" id="price-display">$13.99</span>
                                    </div>
                                    <div class="features">
                                        <ul>
                                            <li>✓ High-quality JPEG format</li>
                                            <li>✓ No watermarks</li>
                                            <li>✓ 5 downloads included</li>
                                            <li>✓ 30-day access</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <form id="user-details-form">
                                <div class="form-group">
                                    <label for="first_name">First Name *</label>
                                    <input type="text" id="first_name" name="first_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="last_name">Last Name *</label>
                                    <input type="text" id="last_name" name="last_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address *</label>
                                    <input type="email" id="email" name="email" required>
                                </div>
                                <div class="form-group">
                                    <label for="phone_number">Phone Number (Optional)</label>
                                    <input type="tel" id="phone_number" name="phone_number" placeholder="+1234567890">
                                </div>
                                <div class="form-group">
                                    <label for="country">Country</label>
                                    <select id="country" name="country">
                                        <option value="US">United States</option>
                                        <option value="KE">Kenya</option>
                                        <option value="GB">United Kingdom</option>
                                        <option value="CA">Canada</option>
                                        <option value="AU">Australia</option>
                                        <option value="">Other</option>
                                    </select>
                                </div>

                                <div class="payment-buttons">
                                    <button type="button" class="button secondary" id="cancel-payment">Cancel</button>
                                    <button type="submit" class="button primary" id="proceed-payment">
                                        Proceed to Payment
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div id="payment-processing" style="display: none;">
                            <div class="processing-info">
                                <div class="loading-spinner"></div>
                                <h3>Processing Payment...</h3>
                                <p>Please complete your payment in the popup window.</p>
                                <p><small>Don't close this window until payment is complete.</small></p>
                            </div>
                        </div>

                        <div id="payment-success" style="display: none;">
                            <div class="success-info">
                                <div class="success-icon">✅</div>
                                <h3>Payment Successful!</h3>
                                <p>Your payment has been processed successfully.</p>
                                <button class="button primary" id="download-now">Download Now</button>
                            </div>
                        </div>

                        <div id="payment-error" style="display: none;">
                            <div class="error-info">
                                <div class="error-icon">❌</div>
                                <h3>Payment Failed</h3>
                                <p id="error-message">Something went wrong with your payment.</p>
                                <button class="button primary" id="retry-payment">Try Again</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    setupEventListeners() {
        // Close modal
        document.getElementById('payment-modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('cancel-payment').addEventListener('click', () => {
            this.closeModal();
        });

        // Form submission
        document.getElementById('user-details-form').addEventListener('submit', (e) => {
            e.preventDefault();
            if (this.currentContext) {
                this.initiatePayment(this.currentContext.imageId, this.currentContext.standardId, this.currentContext.downloadType);
            }
        });

        // Retry payment
        document.getElementById('retry-payment').addEventListener('click', () => {
            this.showPaymentForm();
        });

        // Download now button
        document.getElementById('download-now').addEventListener('click', () => {
            this.downloadFile();
        });

        // Listen for payment window messages
        window.addEventListener('message', (event) => {
            if (event.data.type === 'payment_success') {
                this.handlePaymentSuccess();
            } else if (event.data.type === 'payment_cancelled') {
                this.handlePaymentCancelled();
            }
        });

        // Close modal when clicking outside
        document.getElementById('payment-modal').addEventListener('click', (e) => {
            if (e.target.id === 'payment-modal') {
                this.closeModal();
            }
        });
    }

    async initiatePayment(imageId, standardId, downloadType = 'digital') {
        try {
            this.showProcessingState();

            // Get form data
            const formData = new FormData(document.getElementById('user-details-form'));
            const userData = Object.fromEntries(formData.entries());

            // Determine currency based on country
            const currency = userData.country === 'KE' ? 'KES' : 'USD';

            // Prepare payment request
            const paymentRequest = {
                email: userData.email,
                first_name: userData.first_name,
                last_name: userData.last_name,
                phone_number: userData.phone_number || null,
                country: userData.country || null,
                image_id: imageId,
                standard_id: standardId,
                download_type: downloadType,
                currency: currency
            };

            // Initiate payment
            const response = await fetch('/api/payment/initiate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(paymentRequest)
            });

            const data = await response.json();

            if (!response.ok || !data.success) {
                throw new Error(data.error || 'Payment initiation failed');
            }

            // Store order info
            this.currentOrder = {
                orderId: data.order_id,
                checkoutUrl: data.checkout_url,
                imageId: imageId,
                standardId: standardId,
                downloadType: downloadType
            };

            // Open payment window
            this.openPaymentWindow(data.checkout_url);

            // Start checking payment status
            this.startStatusCheck();

        } catch (error) {
            console.error('Payment initiation error:', error);
            this.showError(error.message);
        }
    }

    openPaymentWindow(checkoutUrl) {
        const width = 600;
        const height = 700;
        const left = (screen.width - width) / 2;
        const top = (screen.height - height) / 2;

        this.paymentWindow = window.open(
            checkoutUrl,
            'payment',
            `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
        );

        // Check if popup was blocked
        if (!this.paymentWindow) {
            this.showError('Popup blocked. Please allow popups for this site and try again.');
            return;
        }

        // Monitor if payment window is closed
        const checkClosed = setInterval(() => {
            if (this.paymentWindow.closed) {
                clearInterval(checkClosed);
                this.stopStatusCheck();
                // Don't show error immediately, user might have completed payment
                setTimeout(() => {
                    if (this.currentOrder && !this.isPaymentComplete) {
                        this.showError('Payment window was closed. Please try again.');
                    }
                }, 2000);
            }
        }, 1000);
    }

    startStatusCheck() {
        if (!this.currentOrder) return;

        this.checkStatusInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/payment/status/${this.currentOrder.orderId}`);
                const status = await response.json();

                if (status.status === 'paid') {
                    this.handlePaymentSuccess();
                } else if (status.status === 'failed') {
                    this.handlePaymentFailed();
                }
            } catch (error) {
                console.error('Status check error:', error);
            }
        }, 3000); // Check every 3 seconds
    }

    stopStatusCheck() {
        if (this.checkStatusInterval) {
            clearInterval(this.checkStatusInterval);
            this.checkStatusInterval = null;
        }
    }

    handlePaymentSuccess() {
        this.isPaymentComplete = true;
        this.stopStatusCheck();
        if (this.paymentWindow) {
            this.paymentWindow.close();
        }
        this.showSuccess();
    }

    handlePaymentCancelled() {
        this.stopStatusCheck();
        this.showError('Payment was cancelled. Please try again.');
    }

    handlePaymentFailed() {
        this.stopStatusCheck();
        if (this.paymentWindow) {
            this.paymentWindow.close();
        }
        this.showError('Payment failed. Please try again.');
    }

    async downloadFile() {
        if (!this.currentOrder) return;

        try {
            // Get download link
            const response = await fetch(`/api/payment/download/${this.currentOrder.orderId}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to get download link');
            }

            // Trigger download
            window.location.href = data.download_url;

            // Close modal after successful download
            setTimeout(() => {
                this.closeModal();
            }, 1000);

        } catch (error) {
            console.error('Download error:', error);
            alert(`Download failed: ${error.message}`);
        }
    }

    // UI State Management
    showModal(imageId, standardId, downloadType = 'digital') {
        // Update pricing based on download type and detect country
        this.updatePricing(downloadType);

        // Store current context
        this.currentContext = { imageId, standardId, downloadType };

        // Show modal
        document.getElementById('payment-modal').style.display = 'block';
        this.showPaymentForm();
    }

    closeModal() {
        document.getElementById('payment-modal').style.display = 'none';
        this.stopStatusCheck();
        if (this.paymentWindow) {
            this.paymentWindow.close();
        }
        this.currentOrder = null;
        this.isPaymentComplete = false;
    }

    showPaymentForm() {
        document.getElementById('payment-form').style.display = 'block';
        document.getElementById('payment-processing').style.display = 'none';
        document.getElementById('payment-success').style.display = 'none';
        document.getElementById('payment-error').style.display = 'none';
    }

    showProcessingState() {
        document.getElementById('payment-form').style.display = 'none';
        document.getElementById('payment-processing').style.display = 'block';
        document.getElementById('payment-success').style.display = 'none';
        document.getElementById('payment-error').style.display = 'none';
    }

    showSuccess() {
        document.getElementById('payment-form').style.display = 'none';
        document.getElementById('payment-processing').style.display = 'none';
        document.getElementById('payment-success').style.display = 'block';
        document.getElementById('payment-error').style.display = 'none';
    }

    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('payment-form').style.display = 'none';
        document.getElementById('payment-processing').style.display = 'none';
        document.getElementById('payment-success').style.display = 'none';
        document.getElementById('payment-error').style.display = 'block';
    }

    updatePricing(downloadType) {
        // This would be updated based on user's country detection
        const priceDisplay = document.getElementById('price-display');
        if (downloadType === 'digital') {
            priceDisplay.textContent = '$13.99';
        } else if (downloadType === 'print') {
            priceDisplay.textContent = '$13.99';
        }
    }

    // Public method to trigger payment for downloads
    async requestPayment(imageId, standardId, downloadType = 'digital') {
        this.showModal(imageId, standardId, downloadType);
    }
}

// Payment manager will be initialized in the main HTML file
