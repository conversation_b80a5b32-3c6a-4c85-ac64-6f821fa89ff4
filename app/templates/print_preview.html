{% extends "base.html" %}

{% block title %}Print Preview{% endblock %}

{% block head %}
<style>
    .print-preview-container {
        margin-top: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .print-preview-title {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 15px;
        color: #3498db;
    }

    .print-preview-image {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
    }

    .print-preview-image img {
        max-width: 100%;
        max-height: 600px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    .print-details {
        margin-top: 20px;
        padding: 15px;
        background-color: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
    }

    .print-details-title {
        font-weight: 500;
        margin-bottom: 10px;
        color: #2c3e50;
    }

    .print-details-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f1f1;
    }

    .print-details-item:last-child {
        border-bottom: none;
    }

    .print-details-label {
        font-weight: 500;
        color: #6c757d;
    }

    .print-details-value {
        color: #2c3e50;
    }

    .button-container {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .spinner-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        visibility: hidden;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1>Print Preview</h1>
    <p>Preview your passport photo print layout before downloading or printing.</p>

    <div class="spinner-overlay" id="spinnerOverlay">
        <div class="spinner"></div>
    </div>

    <div class="print-preview-container">
        <div class="print-preview-title">Print Layout Preview</div>
        <div class="print-preview-image">
            <img id="previewImage" src="" alt="Print Layout Preview">
        </div>

        <div class="print-details">
            <div class="print-details-title">Print Details</div>
            <div id="printDetails">
                <!-- Print details will be loaded here dynamically -->
            </div>
        </div>
    </div>

    <div class="button-container">
        <button class="btn btn-secondary" id="backButton">
            <i class="fas fa-arrow-left"></i> Back to Layout Selection
        </button>
        <div class="action-buttons">
            <button class="btn btn-success" id="downloadButton">
                <i class="fas fa-download"></i> Download
            </button>
            <button class="btn btn-primary" id="printButton">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<script src="/static/js/print_preview.js"></script>
{% endblock %}
