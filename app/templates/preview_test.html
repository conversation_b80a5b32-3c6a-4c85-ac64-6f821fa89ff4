<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passport Photo Preview Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .panel {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 300px;
        }
        .preview-panel {
            flex: 2;
            min-width: 600px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1, h2 {
            color: #0EAAC5;
        }
        button, select, input {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        button {
            background-color: #0EAAC5;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0c8fa6;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        #preview-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f9f9f9;
        }
        #preview-container svg {
            max-width: 100%;
            max-height: 100%;
        }
        #status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        #original-image {
            max-width: 100%;
            max-height: 300px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Passport Photo Preview Test</h1>
    
    <div class="container">
        <div class="panel">
            <h2>Upload & Process</h2>
            
            <div class="form-group">
                <label for="photo-upload">Upload Photo:</label>
                <input type="file" id="photo-upload" accept="image/*">
                <button id="upload-btn" onclick="uploadImage()">Upload</button>
            </div>
            
            <div id="image-preview" class="hidden">
                <h3>Original Image:</h3>
                <img id="original-image" src="" alt="Original image">
            </div>
            
            <div class="form-group">
                <label for="standard-select">Select Photo Standard:</label>
                <select id="standard-select" disabled>
                    <option value="">Loading standards...</option>
                </select>
            </div>
            
            <div class="form-group">
                <button id="detect-btn" disabled onclick="detectLandmarks()">Detect Landmarks</button>
                <button id="preview-btn" disabled onclick="generatePreview()">Generate Preview</button>
            </div>
            
            <div id="status" class="hidden"></div>
        </div>
        
        <div class="panel preview-panel">
            <h2>Preview</h2>
            <div id="preview-container">
                <p>Upload a photo and generate a preview to see the result here.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables to store state
        let currentImageId = null;
        let standards = [];
        let landmarks = null;
        
        // Load photo standards when the page loads
        window.onload = function() {
            fetchPhotoStandards();
        };
        
        // Fetch photo standards from the API
        async function fetchPhotoStandards() {
            try {
                const response = await fetch('/api/photostandards');
                const data = await response.json();
                
                standards = data.standards;
                
                // Populate the select dropdown
                const select = document.getElementById('standard-select');
                select.innerHTML = '';
                
                standards.forEach(standard => {
                    const option = document.createElement('option');
                    option.value = standard.id;
                    option.textContent = `${standard.country} - ${standard.docType} (${standard.text})`;
                    select.appendChild(option);
                });
                
                select.disabled = false;
            } catch (error) {
                showStatus('Error loading photo standards: ' + error.message, 'error');
            }
        }
        
        // Upload an image
        async function uploadImage() {
            const fileInput = document.getElementById('photo-upload');
            const file = fileInput.files[0];
            
            if (!file) {
                showStatus('Please select a file to upload', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showStatus('Uploading image...', 'info');
                
                const response = await fetch('/api/landmarks/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                currentImageId = data.image_id;
                
                // Show the uploaded image
                const img = document.getElementById('original-image');
                img.src = `/api/photo/image/${currentImageId}`;
                document.getElementById('image-preview').classList.remove('hidden');
                
                // Enable the detect button
                document.getElementById('detect-btn').disabled = false;
                
                showStatus('Image uploaded successfully!', 'success');
            } catch (error) {
                showStatus('Error uploading image: ' + error.message, 'error');
            }
        }
        
        // Detect landmarks in the uploaded image
        async function detectLandmarks() {
            if (!currentImageId) {
                showStatus('Please upload an image first', 'error');
                return;
            }
            
            try {
                showStatus('Detecting landmarks...', 'info');
                
                const response = await fetch(`/api/landmarks/detect/${currentImageId}`);
                const data = await response.json();
                
                if (data.error) {
                    showStatus('Error detecting landmarks: ' + data.error, 'error');
                    return;
                }
                
                landmarks = data.landmarks;
                
                // Enable the preview button
                document.getElementById('preview-btn').disabled = false;
                
                showStatus('Landmarks detected successfully!', 'success');
            } catch (error) {
                showStatus('Error detecting landmarks: ' + error.message, 'error');
            }
        }
        
        // Generate the preview SVG
        async function generatePreview() {
            if (!currentImageId) {
                showStatus('Please upload an image first', 'error');
                return;
            }
            
            if (!landmarks) {
                showStatus('Please detect landmarks first', 'error');
                return;
            }
            
            const standardId = document.getElementById('standard-select').value;
            if (!standardId) {
                showStatus('Please select a photo standard', 'error');
                return;
            }
            
            try {
                showStatus('Generating preview...', 'info');
                
                const response = await fetch('/api/photo/preview-svg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_id: currentImageId,
                        standard_id: standardId
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || 'Failed to generate preview');
                }
                
                // Get the SVG content
                const svgContent = await response.text();
                
                // Display the SVG
                const previewContainer = document.getElementById('preview-container');
                previewContainer.innerHTML = svgContent;
                
                showStatus('Preview generated successfully!', 'success');
            } catch (error) {
                showStatus('Error generating preview: ' + error.message, 'error');
            }
        }
        
        // Show status message
        function showStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = type;
            statusElement.classList.remove('hidden');
        }
    </script>
</body>
</html>
