{% extends "base.html" %}

{% block title %}Print Layout Selection{% endblock %}

{% block head %}
<style>
    .print-layout-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 20px;
    }

    .print-layout-item {
        width: calc(33.33% - 20px);
        min-width: 200px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .print-layout-item:hover {
        border-color: #adb5bd;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .print-layout-item.selected {
        border-color: #3498db;
        background-color: rgba(52, 152, 219, 0.05);
    }

    .print-layout-thumbnail {
        width: 100%;
        height: 150px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        border-radius: 4px;
        overflow: hidden;
    }

    .print-layout-thumbnail img {
        max-width: 100%;
        max-height: 100%;
    }

    .print-layout-title {
        font-weight: 500;
        text-align: center;
        margin-bottom: 5px;
    }

    .print-layout-details {
        font-size: 0.9rem;
        color: #6c757d;
        text-align: center;
    }

    .custom-controls {
        position: absolute;
        top: 5px;
        right: 5px;
        display: flex;
        gap: 5px;
    }

    .custom-controls button {
        width: 30px;
        height: 30px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .add-custom-layout {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        border: 2px dashed #ced4da;
        border-radius: 8px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .add-custom-layout:hover {
        border-color: #3498db;
        background-color: rgba(52, 152, 219, 0.05);
    }

    .add-custom-layout i {
        font-size: 2rem;
        color: #3498db;
        margin-bottom: 10px;
    }

    .add-custom-layout span {
        color: #6c757d;
        text-align: center;
    }

    .button-container {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }

    .preview-container {
        margin-top: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .preview-title {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 15px;
        color: #3498db;
    }

    .preview-image {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
    }

    .preview-image img {
        max-width: 100%;
        max-height: 400px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .modal-title {
        font-size: 1.5rem;
        font-weight: 500;
        color: #2c3e50;
    }

    .close-modal {
        font-size: 1.5rem;
        font-weight: bold;
        cursor: pointer;
        color: #aaa;
    }

    .close-modal:hover {
        color: #333;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .form-group input, .form-group select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 1rem;
    }

    .form-row {
        display: flex;
        gap: 15px;
    }

    .form-row .form-group {
        flex: 1;
    }

    .modal-footer {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .spinner-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        visibility: hidden;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1>Print Layout Selection</h1>
    <p>Select a print layout for your passport photo or create a custom layout.</p>

    <div class="spinner-overlay" id="spinnerOverlay">
        <div class="spinner"></div>
    </div>

    <div class="print-layout-container" id="printLayoutContainer">
        <!-- Print layouts will be loaded here dynamically -->
        <div class="print-layout-item" id="addCustomLayout">
            <div class="add-custom-layout">
                <i class="fas fa-plus-circle"></i>
                <span>Add Custom Layout</span>
            </div>
        </div>
    </div>

    <div class="preview-container" id="previewContainer" style="display: none;">
        <div class="preview-title">Preview</div>
        <div class="preview-image">
            <img id="previewImage" src="" alt="Print Layout Preview">
        </div>
    </div>

    <div class="button-container">
        <button class="btn btn-secondary" id="backButton">
            <i class="fas fa-arrow-left"></i> Back to Crop
        </button>
        <button class="btn btn-primary" id="nextButton" disabled>
            Next <i class="fas fa-arrow-right"></i>
        </button>
    </div>
</div>

<!-- Custom Layout Modal -->
<div id="customLayoutModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Custom Print Layout</h2>
            <span class="close-modal">&times;</span>
        </div>
        <form id="customLayoutForm">
            <div class="form-group">
                <label for="layoutName">Layout Name</label>
                <input type="text" id="layoutName" name="title" required>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="layoutWidth">Width</label>
                    <input type="number" id="layoutWidth" name="width" step="0.1" min="0.1" required>
                </div>
                <div class="form-group">
                    <label for="layoutHeight">Height</label>
                    <input type="number" id="layoutHeight" name="height" step="0.1" min="0.1" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="layoutGutter">Gutter</label>
                    <input type="number" id="layoutGutter" name="gutter" step="0.01" min="0" value="0.1">
                </div>
                <div class="form-group">
                    <label for="layoutPadding">Padding</label>
                    <input type="number" id="layoutPadding" name="padding" step="0.01" min="0" value="0.06">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="layoutUnits">Units</label>
                    <select id="layoutUnits" name="units">
                        <option value="inch">inch</option>
                        <option value="cm">cm</option>
                        <option value="mm">mm</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="layoutResolution">Resolution (DPI)</label>
                    <select id="layoutResolution" name="resolution">
                        <option value="150">150</option>
                        <option value="200">200</option>
                        <option value="300" selected>300</option>
                        <option value="400">400</option>
                        <option value="600">600</option>
                        <option value="800">800</option>
                        <option value="1200">1200</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelButton">Cancel</button>
                <button type="submit" class="btn btn-primary">Save</button>
            </div>
        </form>
    </div>
</div>

<script src="/static/js/print_layout.js"></script>
{% endblock %}
