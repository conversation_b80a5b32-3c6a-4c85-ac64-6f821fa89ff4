{% extends "base.html" %}

{% block title %}Crop Photo{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', path='/css/crop.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="text-center mb-3">Crop Photo</h1>

    <div class="row">
        <div class="col">
            <div class="form-group">
                <label for="photo-upload">Upload Photo</label>
                <input type="file" id="photo-upload" class="form-control" accept="image/*">
            </div>
        </div>

        <div class="col">
            <div class="form-group">
                <label for="standard-selector">Select Document Type</label>
                <select id="standard-selector" class="form-control">
                    <option value="">Select a document type</option>
                    {% for standard in standards %}
                    <option value="{{ standard.id }}">{{ standard.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>

    <div class="loading-indicator" id="loading-indicator">
        <p>Processing image...</p>
    </div>

    <div class="crop-container" id="crop-container">
        <canvas id="crop-canvas" class="crop-canvas"></canvas>
        <!-- Crop box will be added by JavaScript -->
    </div>

    <div class="preview-container" id="preview-container">
        <h3 class="text-center">Preview</h3>
        <canvas id="preview-canvas" class="preview-canvas"></canvas>
    </div>

    <div class="row mt-3">
        <div class="col text-center">
            <button id="save-crop-btn" class="btn" onclick="saveCroppedImage()">Save and Continue</button>
            <button id="print-layout-btn" class="btn" onclick="goToPrintLayout()" style="display: none;">Go to Print Layout</button>
        </div>
    </div>

    <!-- Hidden input to store the image ID -->
    <input type="hidden" id="image-id" value="">
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/crop.js') }}"></script>
{% endblock %}
