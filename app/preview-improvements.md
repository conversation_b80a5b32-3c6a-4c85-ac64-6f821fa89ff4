# Preview Functionality Improvements

This document tracks the improvements to the photo preview functionality, which shows users how their photos will be processed according to passport/ID standards before purchase.

## Improvement Tasks

### 1. Change the image quality in the preview
- [x] Further reduce image quality/resolution in the preview
- [x] Implement stronger JPEG compression (40-50% quality)
- [x] Add a subtle watermark or blur effect to discourage direct use

### 2. Use the processed image instead of the original
- [x] Modify the preview endpoint to use the cropped/processed image
- [x] Ensure the preview shows exactly how the final photo will look (but at lower quality)
- [x] Implement proper workflow: select standard → upload image → make edits → see preview → pay → download

### 3. Change the output format
- [x] Change from SVG with embedded image to JPEG with rendered markers
- [x] Render compliance markers, measurements, and guidelines directly onto the JPEG
- [x] Ensure the JPEG shows all the same information that was in the SVG

### 4. Rename the endpoint
- [x] Change endpoint name from "preview-svg" to "preview-photo"
- [x] Update all references in backend code
- [x] Update all references in frontend code

### 5. Ensure accurate positioning
- [x] Verify that crown and chin lines are positioned accurately on the preview

## Implementation Details

### Technical Approach

1. **Image Quality Reduction**:
   - Reduce resolution to a maximum width/height (e.g., 400px)
   - Apply stronger JPEG compression with cv2.imencode
   - Add a semi-transparent watermark overlay

2. **Using Processed Image**:
   - Process the image according to the standard before generating the preview
   - Apply the same cropping and positioning that would be used for the final image
   - Show the exact result but at lower quality

3. **Output Format Change**:
   - Generate a JPEG instead of SVG
   - Draw all compliance markers directly on the image using OpenCV
   - Include the same measurements and guidelines

4. **Endpoint Renaming**:
   - Update the endpoint URL
   - Update all references in code
   - Ensure backward compatibility if needed

5. **Accurate Positioning**:
   - Verify that crown and chin points are correctly mapped from the original image to the processed image
   - Ensure all measurements are accurate and properly scaled

## Progress Tracking

- Start Date: May 16, 2024
- Completion Date: May 16, 2024
