import logging
from fastapi import APIRouter, HTTPException, Response
from typing import Optional

from app.models.print_definition import (
    PrintDefinitionCreate,
    PrintDefinitionUpdate,
    PrintDefinitionResponse
)
from app.services import print_definition_service, photo_processing
from app.services.landmark_detection import image_store

router = APIRouter(prefix="/api/print", tags=["print"])
logger = logging.getLogger(__name__)


@router.get("/definitions", response_model=PrintDefinitionResponse)
async def get_print_definitions():
    """
    Get all available print definitions.
    """
    try:
        print_definitions = print_definition_service.get_all_print_definitions()
        return {
            "print_definitions": print_definitions,
            "error": None
        }
    except Exception as e:
        logger.error(f"Error getting print definitions: {str(e)}")
        return {
            "print_definitions": None,
            "error": f"Error getting print definitions: {str(e)}"
        }


@router.get("/definitions/{print_definition_id}", response_model=PrintDefinitionResponse)
async def get_print_definition(print_definition_id: str):
    """
    Get a print definition by ID.
    """
    try:
        print_definition = print_definition_service.get_print_definition(print_definition_id)
        if not print_definition:
            return {
                "print_definition": None,
                "error": f"Print definition with ID {print_definition_id} not found"
            }

        return {
            "print_definition": print_definition,
            "error": None
        }
    except Exception as e:
        logger.error(f"Error getting print definition: {str(e)}")
        return {
            "print_definition": None,
            "error": f"Error getting print definition: {str(e)}"
        }


@router.post("/definitions", response_model=PrintDefinitionResponse)
async def create_print_definition(print_definition: PrintDefinitionCreate):
    """
    Create a new custom print definition.
    """
    try:
        print_def, error = print_definition_service.create_print_definition(print_definition.model_dump())
        if error:
            return {
                "print_definition": None,
                "error": error
            }

        return {
            "print_definition": print_def,
            "error": None
        }
    except Exception as e:
        logger.error(f"Error creating print definition: {str(e)}")
        return {
            "print_definition": None,
            "error": f"Error creating print definition: {str(e)}"
        }


@router.put("/definitions/{print_definition_id}", response_model=PrintDefinitionResponse)
async def update_print_definition(print_definition_id: str, print_definition: PrintDefinitionUpdate):
    """
    Update an existing print definition.
    """
    try:
        print_def, error = print_definition_service.update_print_definition(
            print_definition_id,
            print_definition.model_dump(exclude_unset=True)
        )
        if error:
            return {
                "print_definition": None,
                "error": error
            }

        return {
            "print_definition": print_def,
            "error": None
        }
    except Exception as e:
        logger.error(f"Error updating print definition: {str(e)}")
        return {
            "print_definition": None,
            "error": f"Error updating print definition: {str(e)}"
        }


@router.delete("/definitions/{print_definition_id}", response_model=PrintDefinitionResponse)
async def delete_print_definition(print_definition_id: str):
    """
    Delete a print definition.
    """
    try:
        _, error = print_definition_service.delete_print_definition(print_definition_id)
        if error:
            return {
                "print_definition": None,
                "error": error
            }

        return {
            "print_definition": None,
            "error": None
        }
    except Exception as e:
        logger.error(f"Error deleting print definition: {str(e)}")
        return {
            "print_definition": None,
            "error": f"Error deleting print definition: {str(e)}"
        }


@router.post("/create-tiled-print")
async def create_tiled_print(
    image_id: str,
    print_definition_id: Optional[str] = None,
    num_rows: int = 2,
    num_cols: int = 2,
    spacing_mm: float = 2.0
):
    """
    Create a tiled print with multiple copies of the processed photo.

    Args:
        image_id: ID of the processed image
        print_definition_id: Optional ID of a print definition to use
        num_rows: Number of rows in the tiled print (used if print_definition_id is None)
        num_cols: Number of columns in the tiled print (used if print_definition_id is None)
        spacing_mm: Spacing between photos in mm (used if print_definition_id is None)

    Returns:
        JSON with the tiled image ID or error message
    """
    try:
        # Create the tiled print
        tiled_image_id, error = photo_processing.create_tiled_print(
            image_id,
            num_rows,
            num_cols,
            spacing_mm,
            print_definition_id
        )

        if error:
            raise HTTPException(status_code=400, detail=error)

        # Get the tiled image info
        width = image_store[tiled_image_id]['width']
        height = image_store[tiled_image_id]['height']

        return {
            "tiled_image_id": tiled_image_id,
            "width": width,
            "height": height,
            "format": "JPEG",
            "error": None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating tiled print: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating tiled print: {str(e)}")


@router.get("/image/{image_id}")
async def get_print_image(image_id: str, download: bool = False):
    """
    Get a print image by ID.

    Args:
        image_id: ID of the print image
        download: Whether to download the image

    Returns:
        The print image as JPEG
    """
    try:
        # Check if image exists
        if image_id not in image_store:
            raise HTTPException(status_code=404, detail=f"Image with ID {image_id} not found")

        # Get the image
        img = image_store[image_id]['image']

        # Get the print definition ID if available to determine DPI
        print_definition_id = image_store[image_id].get('print_definition_id')
        dpi = 300.0  # Default DPI

        # If we have a print definition ID, get the print definition's DPI
        if print_definition_id:
            from app.services.print_definition_service import get_print_definition
            print_def = get_print_definition(print_definition_id)
            if print_def and hasattr(print_def, 'resolution') and print_def.resolution:
                dpi = print_def.resolution

        # Convert the image to JPEG format with maximum quality
        import cv2
        success, buffer = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
        if not success:
            raise HTTPException(status_code=500, detail="Failed to encode the image")

        # Embed DPI information in the image
        from app.services.dpi_service import DpiService
        img_bytes_with_dpi = DpiService.embed_dpi_in_image(buffer.tobytes(), dpi, "JPEG")

        # Set headers for download if requested
        headers = {}
        if download:
            headers["Content-Disposition"] = f"attachment; filename=print_{image_id}.jpg"

        # Add DPI header for debugging
        headers["X-DPI"] = str(dpi)

        # Return the image as a response
        return Response(
            content=img_bytes_with_dpi,
            media_type="image/jpeg",
            headers=headers
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting print image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting print image: {str(e)}")
