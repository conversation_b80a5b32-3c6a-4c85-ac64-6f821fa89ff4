import logging
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Response
from typing import Optional
from io import BytesIO

from app.services.photo_processing import crop_photo_to_standard, create_tiled_print
from app.services.landmark_detection import image_store
from app.services.photostandard_service import get_photo_standard
from app.models.landmarks import Point

router = APIRouter(prefix="/api/photo", tags=["photo"])
logger = logging.getLogger(__name__)

@router.post("/crop")
async def crop_photo(
    image_id: str = Form(...),
    standard_id: str = Form(...),
    crown_x: Optional[float] = Form(None),
    crown_y: Optional[float] = Form(None),
    chin_x: Optional[float] = Form(None),
    chin_y: Optional[float] = Form(None)
):
    """
    Crop a photo according to a specific standard.

    Args:
        image_id: ID of the image to process
        standard_id: ID of the photo standard to use
        crown_x: Optional X coordinate of the crown point
        crown_y: Optional Y coordinate of the crown point
        chin_x: Optional X coordinate of the chin point
        chin_y: Optional Y coordinate of the chin point

    Returns:
        JSON with the processed image ID or error message
    """
    try:
        # Check if custom crown/chin points were provided
        crown_point = None
        chin_point = None

        if crown_x is not None and crown_y is not None:
            crown_point = Point(x=crown_x, y=crown_y)

        if chin_x is not None and chin_y is not None:
            chin_point = Point(x=chin_x, y=chin_y)

        # Process the photo
        processed_image_id, error = crop_photo_to_standard(
            image_id,
            standard_id,
            crown_point,
            chin_point
        )

        if error:
            raise HTTPException(status_code=400, detail=error)

        return {
            "processed_image_id": processed_image_id,
            "error": None
        }

    except Exception as e:
        logger.error(f"Error cropping photo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error cropping photo: {str(e)}")

@router.get("/processed/{image_id}")
async def get_processed_photo(image_id: str, download: bool = False):
    """
    Get a processed photo by its ID.

    Args:
        image_id: ID of the processed image
        download: Whether to download the image or display it in the browser

    Returns:
        The processed image as JPEG
    """
    try:
        # Check if image exists
        if image_id not in image_store:
            raise HTTPException(status_code=404, detail=f"Image with ID {image_id} not found")

        # Get the image
        img = image_store[image_id]['image']

        # Get the standard ID if available to determine DPI
        standard_id = image_store[image_id].get('standard_id')
        dpi = 300.0  # Default DPI

        # If we have a standard ID, get the standard's DPI
        if standard_id:
            standard = get_photo_standard(standard_id)
            if standard and hasattr(standard, 'dimensions') and hasattr(standard.dimensions, 'dpi'):
                dpi = standard.dimensions.dpi

        # Convert the image to JPEG format with maximum quality
        import cv2
        success, buffer = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
        if not success:
            raise HTTPException(status_code=500, detail="Failed to encode the image")

        # Embed DPI information in the image
        from app.services.dpi_service import DpiService
        img_bytes_with_dpi = DpiService.embed_dpi_in_image(buffer.tobytes(), dpi, "JPEG")

        # Set headers for download if requested
        headers = {}
        if download:
            headers["Content-Disposition"] = f"attachment; filename=passport_photo_{image_id}.jpg"

        # Add DPI header for debugging
        headers["X-DPI"] = str(dpi)

        # Return the image as a response
        return Response(
            content=img_bytes_with_dpi,
            media_type="image/jpeg",
            headers=headers
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting processed photo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting processed photo: {str(e)}")

@router.post("/print")
async def create_print(
    image_id: str = Form(...),
    rows: int = Form(2),
    cols: int = Form(2),
    spacing: float = Form(2.0)
):
    """
    Create a tiled print with multiple copies of the processed photo.

    Args:
        image_id: ID of the processed image
        rows: Number of rows in the tiled print
        cols: Number of columns in the tiled print
        spacing: Spacing between photos in mm

    Returns:
        JSON with the tiled image ID or error message
    """
    try:
        # Create the tiled print
        tiled_image_id, error = create_tiled_print(
            image_id,
            rows,
            cols,
            spacing
        )

        if error:
            raise HTTPException(status_code=400, detail=error)

        return {
            "tiled_image_id": tiled_image_id,
            "error": None
        }

    except Exception as e:
        logger.error(f"Error creating tiled print: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating tiled print: {str(e)}")
