import logging
import uuid
import cv2
import numpy as np
from fastapi import APIRouter, HTTPException, Query
from typing import Optional, List

from app.models.photostandard import (
    PhotoStandard,
    PhotoStandardResponse,
    PhotoStandardDetailResponse,
    PhotoProcessingRequest,
    PhotoProcessingResponse
)
from app.models.landmarks import Point
from app.services import photostandard_service
from app.services.landmark_detection import image_store

router = APIRouter(prefix="/api/photostandards", tags=["photostandards"])
logger = logging.getLogger(__name__)


@router.get("", response_model=PhotoStandardResponse)
async def get_photo_standards(
    country: Optional[str] = None,
    doc_type: Optional[str] = None
):
    """
    Get all photo standards, optionally filtered by country or document type.
    """
    try:
        if country:
            standards = photostandard_service.get_photo_standards_by_country(country)
        elif doc_type:
            standards = photostandard_service.get_photo_standards_by_doc_type(doc_type)
        else:
            standards = photostandard_service.load_photo_standards()
        
        return {
            "standards": standards,
            "count": len(standards)
        }
    except Exception as e:
        logger.error(f"Error getting photo standards: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting photo standards: {str(e)}")


@router.get("/{standard_id}", response_model=PhotoStandardDetailResponse)
async def get_photo_standard(standard_id: str):
    """
    Get a specific photo standard by ID.
    """
    try:
        standard = photostandard_service.get_photo_standard(standard_id)
        if standard is None:
            raise HTTPException(status_code=404, detail=f"Photo standard with ID {standard_id} not found")
        
        return {
            "standard": standard
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting photo standard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting photo standard: {str(e)}")


@router.post("/process", response_model=PhotoProcessingResponse)
async def process_photo(request: PhotoProcessingRequest):
    """
    Process a photo according to a specific standard.
    """
    try:
        # Convert crown and chin points if provided
        crown_point = None
        chin_point = None
        
        if request.crown_point:
            crown_point = Point(
                x=request.crown_point.get("x"),
                y=request.crown_point.get("y")
            )
        
        if request.chin_point:
            chin_point = Point(
                x=request.chin_point.get("x"),
                y=request.chin_point.get("y")
            )
        
        # Process the photo
        processed_img, error = photostandard_service.process_photo(
            request.image_id,
            request.standard_id,
            crown_point,
            chin_point
        )
        
        if error:
            return {
                "image_id": request.image_id,
                "processed_image_id": "",
                "standard_id": request.standard_id,
                "width": 0,
                "height": 0,
                "format": "",
                "error": error
            }
        
        # Generate a new image ID for the processed image
        processed_image_id = str(uuid.uuid4())
        
        # Store the processed image
        height, width = processed_img.shape[:2]
        image_store[processed_image_id] = {
            "image": processed_img,
            "width": width,
            "height": height,
            "format": "JPEG",
            "landmarks": None
        }
        
        return {
            "image_id": request.image_id,
            "processed_image_id": processed_image_id,
            "standard_id": request.standard_id,
            "width": width,
            "height": height,
            "format": "JPEG",
            "error": None
        }
    except Exception as e:
        logger.error(f"Error processing photo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing photo: {str(e)}")


@router.get("/countries", response_model=List[str])
async def get_countries():
    """
    Get a list of all countries with photo standards.
    """
    try:
        standards = photostandard_service.load_photo_standards()
        countries = sorted(list(set(standard.country for standard in standards)))
        return countries
    except Exception as e:
        logger.error(f"Error getting countries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting countries: {str(e)}")


@router.get("/doctypes", response_model=List[str])
async def get_doc_types():
    """
    Get a list of all document types with photo standards.
    """
    try:
        standards = photostandard_service.load_photo_standards()
        doc_types = sorted(list(set(standard.docType for standard in standards if standard.docType)))
        return doc_types
    except Exception as e:
        logger.error(f"Error getting document types: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document types: {str(e)}")
