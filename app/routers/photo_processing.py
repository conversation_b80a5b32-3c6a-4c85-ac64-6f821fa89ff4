import logging
import cv2
from fastapi import APIRouter, HTTPException, Response
from typing import Optional, Dict
from pydantic import BaseModel

from app.models.landmarks import Point
from app.models.photostandard import PhotoPreviewRequest
from app.services import photo_processing
from app.services.landmark_detection import image_store

router = APIRouter(prefix="/api/photo", tags=["photo"])
logger = logging.getLogger(__name__)


class PhotoProcessingRequest(BaseModel):
    """
    Request model for photo processing.
    """
    image_id: str
    standard_id: str
    crown_point: Optional[Dict[str, float]] = None
    chin_point: Optional[Dict[str, float]] = None


class PhotoProcessingResponse(BaseModel):
    """
    Response model for photo processing.
    """
    image_id: str
    processed_image_id: str
    standard_id: str
    width: int
    height: int
    format: str
    background_color: str = "white"  # Default to white background
    error: Optional[str] = None


class TiledPrintRequest(BaseModel):
    """
    Request model for creating a tiled print.
    """
    image_id: str
    num_rows: int = 2
    num_cols: int = 2
    spacing_mm: float = 2.0


class TiledPrintResponse(BaseModel):
    """
    Response model for tiled print creation.
    """
    image_id: str
    tiled_image_id: str
    width: int
    height: int
    format: str
    error: Optional[str] = None


@router.post("/process", response_model=PhotoProcessingResponse)
async def process_photo(request: PhotoProcessingRequest):
    """
    Process a photo according to a specific standard.
    """
    try:
        # Convert crown and chin points if provided
        crown_point = None
        chin_point = None

        if request.crown_point:
            crown_point = Point(
                x=request.crown_point.get("x"),
                y=request.crown_point.get("y")
            )

        if request.chin_point:
            chin_point = Point(
                x=request.chin_point.get("x"),
                y=request.chin_point.get("y")
            )

        # Process the photo
        processed_image_id, error = photo_processing.crop_photo_to_standard(
            request.image_id,
            request.standard_id,
            crown_point,
            chin_point
        )

        if error:
            return {
                "image_id": request.image_id,
                "processed_image_id": "",
                "standard_id": request.standard_id,
                "width": 0,
                "height": 0,
                "format": "",
                "background_color": "white",  # Default to white background
                "error": error
            }

        # Get the processed image info
        width = image_store[processed_image_id]['width']
        height = image_store[processed_image_id]['height']

        return {
            "image_id": request.image_id,
            "processed_image_id": processed_image_id,
            "standard_id": request.standard_id,
            "width": width,
            "height": height,
            "format": "JPEG",
            "background_color": "white",  # Explicitly indicate white background
            "error": None
        }
    except Exception as e:
        logger.error(f"Error processing photo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing photo: {str(e)}")


@router.post("/tiled-print", response_model=TiledPrintResponse)
async def create_tiled_print(request: TiledPrintRequest):
    """
    Create a tiled print with multiple copies of the processed photo.
    """
    try:
        # Create the tiled print
        tiled_image_id, error = photo_processing.create_tiled_print(
            request.image_id,
            request.num_rows,
            request.num_cols,
            request.spacing_mm
        )

        if error:
            return {
                "image_id": request.image_id,
                "tiled_image_id": "",
                "width": 0,
                "height": 0,
                "format": "",
                "error": error
            }

        # Get the tiled image info
        width = image_store[tiled_image_id]['width']
        height = image_store[tiled_image_id]['height']

        return {
            "image_id": request.image_id,
            "tiled_image_id": tiled_image_id,
            "width": width,
            "height": height,
            "format": "JPEG",
            "error": None
        }
    except Exception as e:
        logger.error(f"Error creating tiled print: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating tiled print: {str(e)}")


@router.get("/image/{image_id}")
async def get_image(image_id: str, download: bool = False):
    """
    Get an image by ID.
    """
    try:
        if image_id not in image_store:
            raise HTTPException(status_code=404, detail=f"Image with ID {image_id} not found")

        img = image_store[image_id]['image']

        # Get the standard ID if available to determine DPI
        standard_id = image_store[image_id].get('standard_id')
        dpi = 300.0  # Default DPI

        # If we have a standard ID, get the standard's DPI
        if standard_id:
            from app.services.photostandard_service import get_photo_standard
            standard = get_photo_standard(standard_id)
            if standard and hasattr(standard, 'dimensions') and hasattr(standard.dimensions, 'dpi'):
                dpi = standard.dimensions.dpi

        # Encode the image as JPEG with maximum quality
        _, img_encoded = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
        img_bytes = img_encoded.tobytes()

        # Embed DPI information in the image
        from app.services.dpi_service import DpiService
        img_bytes_with_dpi = DpiService.embed_dpi_in_image(img_bytes, dpi, "JPEG")

        # Set the content disposition header for download if requested
        headers = {}
        if download:
            headers["Content-Disposition"] = f"attachment; filename={image_id}.jpg"

        # Add white background header to indicate the image has a white background
        headers["X-Background-Color"] = "white"

        # Add DPI header for debugging
        headers["X-DPI"] = str(dpi)

        return Response(
            content=img_bytes_with_dpi,
            media_type="image/jpeg",
            headers=headers
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting image: {str(e)}")


@router.post("/preview-svg")
async def generate_preview_svg(request: PhotoPreviewRequest):
    """
    Generate a preview SVG with compliance markers for a photo.

    This endpoint accepts a user's photo and selected photo standard,
    and returns an SVG that shows how the photo will be cropped and
    positioned to meet the standard requirements.

    The SVG includes:
    - The user's photo (in low resolution/quality)
    - Compliance markers showing the required dimensions
    - Crown and chin lines positioned at the detected facial landmarks
    - Measurements and guidelines

    This preview allows users to see the value of the service before purchasing.
    """
    try:
        # Convert crown and chin points if provided
        crown_point = None
        chin_point = None

        if request.crown_point:
            crown_point = Point(
                x=request.crown_point.get("x"),
                y=request.crown_point.get("y")
            )

        if request.chin_point:
            chin_point = Point(
                x=request.chin_point.get("x"),
                y=request.chin_point.get("y")
            )

        # Generate the preview SVG
        svg_content, error = photo_processing.generate_preview_svg(
            request.image_id,
            request.standard_id,
            crown_point,
            chin_point
        )

        if error:
            raise HTTPException(status_code=400, detail=error)

        # Return the SVG as a response
        return Response(
            content=svg_content,
            media_type="image/svg+xml"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating preview SVG: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating preview SVG: {str(e)}")


@router.post("/preview-photo")
async def generate_preview_photo(request: PhotoPreviewRequest):
    """
    Generate a preview JPEG of the cropped photo with compliance markers.

    This endpoint accepts a user's photo and selected photo standard,
    and returns a JPEG preview of how the photo will look after cropping
    according to the standard requirements, including compliance markers.

    The preview includes:
    - The user's photo (in low resolution/quality)
    - Compliance markers showing the required dimensions
    - Crown and chin lines positioned at the detected facial landmarks
    - Measurements and guidelines
    - A watermark to prevent direct use

    This preview allows users to see the value of the service before purchasing.
    """
    try:
        # Convert crown and chin points if provided
        crown_point = None
        chin_point = None

        if request.crown_point:
            crown_point = Point(
                x=request.crown_point.get("x"),
                y=request.crown_point.get("y")
            )

        if request.chin_point:
            chin_point = Point(
                x=request.chin_point.get("x"),
                y=request.chin_point.get("y")
            )

        # Generate the preview JPEG by converting SVG to JPG
        jpg_data, error = photo_processing.generate_preview_jpeg(
            request.image_id,
            request.standard_id,
            crown_point,
            chin_point
        )

        if error:
            raise HTTPException(status_code=400, detail=error)

        # Get the standard to determine DPI
        from app.services.photostandard_service import get_photo_standard
        standard = get_photo_standard(request.standard_id)
        dpi = 300.0  # Default DPI

        if standard and hasattr(standard, 'dimensions') and hasattr(standard.dimensions, 'dpi'):
            dpi = standard.dimensions.dpi

        # Embed DPI information in the image
        from app.services.dpi_service import DpiService
        jpg_data_with_dpi = DpiService.embed_dpi_in_image(jpg_data, dpi, "JPEG")

        # Return the JPEG as a response with white background
        return Response(
            content=jpg_data_with_dpi,
            media_type="image/jpeg",
            headers={
                "X-Background-Color": "white",  # Add header to indicate white background
                "X-DPI": str(dpi)  # Add DPI header for debugging
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating preview photo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating preview photo: {str(e)}")
