import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import RedirectResponse, HTMLResponse
from app.models.payment import (
    PaymentInitiateRequest, PaymentResponse, OrderStatusResponse,
    DownloadLinkResponse, WebhookPayload
)
from app.services.payment_service import payment_service
from app.services.database import db_service
from app.services.download_service import download_service

router = APIRouter(prefix="/api/payment", tags=["payment"])
logger = logging.getLogger(__name__)


@router.post("/initiate", response_model=PaymentResponse)
async def initiate_payment(request: PaymentInitiateRequest):
    """
    Initiate payment process for passport photo download.

    This endpoint:
    1. Creates or gets user by email
    2. Creates a new order
    3. Generates IntaSend checkout link
    4. Returns checkout URL for user to complete payment
    """
    try:
        response = await payment_service.initiate_payment(request)
        return response

    except Exception as e:
        logger.error(f"Error initiating payment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Payment initiation failed: {str(e)}")


@router.get("/status/{order_id}", response_model=OrderStatusResponse)
async def get_payment_status(order_id: str):
    """
    Get payment status for an order.

    This endpoint checks the current status of a payment order
    and returns information about downloads remaining.
    """
    try:
        status = await payment_service.check_payment_status(order_id)
        return status

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error checking payment status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@router.post("/webhook")
async def handle_webhook(request: Request):
    """
    Handle IntaSend webhook notifications.

    This endpoint receives payment status updates from IntaSend
    and updates the order status accordingly.
    """
    try:
        # Get webhook data
        webhook_data = await request.json()
        logger.info(f"Received webhook: {webhook_data}")

        # Validate webhook (you might want to add signature verification here)
        if not webhook_data.get("invoice_id"):
            raise HTTPException(status_code=400, detail="Invalid webhook data")

        # Process webhook
        success = await payment_service.handle_webhook(webhook_data)

        if success:
            return {"status": "success", "message": "Webhook processed"}
        else:
            raise HTTPException(status_code=400, detail="Webhook processing failed")

    except Exception as e:
        logger.error(f"Error handling webhook: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Webhook handling failed: {str(e)}")


@router.get("/success")
async def payment_success(request: Request):
    """
    Payment success redirect page.

    Users are redirected here after successful payment.
    """
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Successful</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #28a745; }
            .container { max-width: 600px; margin: 0 auto; }
            .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="success">✅ Payment Successful!</h1>
            <p>Thank you for your purchase. Your payment has been processed successfully.</p>
            <p>You can now download your high-quality passport photo.</p>
            <a href="/" class="btn">Return to App</a>
        </div>
        <script>
            // Close this window and notify parent
            if (window.opener) {
                window.opener.postMessage({type: 'payment_success'}, '*');
                window.close();
            }
        </script>
    </body>
    </html>
    """)


@router.get("/cancel")
async def payment_cancel(request: Request):
    """
    Payment cancellation page.

    Users are redirected here if they cancel payment.
    """
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Cancelled</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .cancel { color: #dc3545; }
            .container { max-width: 600px; margin: 0 auto; }
            .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="cancel">❌ Payment Cancelled</h1>
            <p>Your payment was cancelled. No charges have been made.</p>
            <p>You can try again or contact support if you need assistance.</p>
            <a href="/" class="btn">Return to App</a>
        </div>
        <script>
            // Close this window and notify parent
            if (window.opener) {
                window.opener.postMessage({type: 'payment_cancelled'}, '*');
                window.close();
            }
        </script>
    </body>
    </html>
    """)


@router.get("/download/{order_id}")
async def get_download_link(order_id: str):
    """
    Get secure download link for paid order.

    This endpoint verifies payment and provides a secure download link
    that expires after a certain time.
    """
    try:
        # Verify payment status
        status = await payment_service.check_payment_status(order_id)

        if status.status != "paid":
            raise HTTPException(status_code=403, detail="Payment not completed")

        if status.downloads_remaining <= 0:
            raise HTTPException(status_code=403, detail="Download limit exceeded")

        # Generate secure download link
        download_response = await download_service.generate_download_link(order_id)

        return download_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating download link: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Download link generation failed: {str(e)}")


@router.get("/orders/{user_email}")
async def get_user_orders(user_email: str):
    """
    Get all orders for a user by email.

    This endpoint allows users to see their purchase history.
    """
    try:
        # Get user by email
        user = await db_service.get_user_by_email(user_email)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Get user orders
        orders = await db_service.get_user_orders(user.id)

        return {
            "user": {
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name
            },
            "orders": orders
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user orders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get orders: {str(e)}")


@router.get("/download/{order_id}")
async def download_by_order(order_id: str):
    """
    Generate download link for a paid order.
    """
    try:
        # Check if order exists and is paid
        order = await db_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")

        if order.status != PaymentStatus.PAID:
            raise HTTPException(status_code=403, detail="Order not paid")

        # Check download permission
        permission = await db_service.get_download_permission(order_id)
        if not permission:
            raise HTTPException(status_code=403, detail="No download permission found")

        if permission.download_count >= permission.max_downloads:
            raise HTTPException(status_code=403, detail="Download limit exceeded")

        # Generate download token
        download_token = await download_service.generate_download_token(
            order.image_id,
            order.standard_id,
            order.download_type,
            order_id
        )

        if not download_token:
            raise HTTPException(status_code=500, detail="Failed to generate download token")

        return {
            "success": True,
            "download_url": f"/api/payment/secure-download/{download_token}",
            "downloads_remaining": permission.max_downloads - permission.download_count - 1,
            "expires_in_minutes": 15
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating download link: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate download link: {str(e)}")


@router.get("/secure-download/{download_token}")
async def secure_download(download_token: str):
    """
    Secure download endpoint using temporary tokens.

    This endpoint processes downloads using time-limited tokens
    and tracks download counts.
    """
    return await download_service.process_secure_download(download_token)


@router.get("/pricing")
async def get_pricing():
    """
    Get current pricing for different download types and currencies.
    """
    from app.models.payment import PricingConfig

    return {
        "pricing": PricingConfig.PRICING,
        "currencies": ["USD", "KES"],
        "download_types": ["digital", "print", "bundle"],
        "max_downloads": 5,
        "download_expiry_days": 30
    }


@router.get("/success")
async def payment_success(tracking_id: str = None, signature: str = None):
    """
    Handle successful payment redirect from IntaSend.
    """
    try:
        if tracking_id:
            # Update order status based on tracking ID
            logger.info(f"Payment success callback received for tracking_id: {tracking_id}")

            # You could verify the payment status here
            # For now, we'll redirect to a success page

        return {
            "success": True,
            "message": "Payment completed successfully!",
            "tracking_id": tracking_id
        }

    except Exception as e:
        logger.error(f"Error handling payment success: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/cancel")
async def payment_cancel():
    """
    Handle cancelled payment redirect from IntaSend.
    """
    return {
        "success": False,
        "message": "Payment was cancelled",
        "redirect": "/"
    }


@router.get("/failed")
async def payment_failed():
    """
    Handle failed payment redirect from IntaSend.
    """
    return {
        "success": False,
        "message": "Payment failed. Please try again.",
        "redirect": "/"
    }
