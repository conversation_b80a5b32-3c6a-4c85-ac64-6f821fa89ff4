import logging
import cv2
import numpy as np
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Body, Response
from typing import Optional
from io import BytesIO

from app.models.landmarks import (
    ImageUploadResponse,
    LandmarkDetectionResponse,
    LandmarkUpdateRequest,
    CrownChinPointPair
)
from app.services.landmark_detection import (
    save_uploaded_image,
    detect_landmarks,
    update_crown_chin_points,
    get_landmarks_visualization,
    image_store
)

router = APIRouter(prefix="/api/landmarks", tags=["landmarks"])
logger = logging.getLogger(__name__)

@router.post("/upload", response_model=ImageUploadResponse)
async def upload_image(file: UploadFile = File(...)):
    """
    Upload an image for facial landmark detection.
    """
    try:
        # Read the file content
        contents = await file.read()

        # Save the image and get its ID and dimensions
        image_id, width, height, format = save_uploaded_image(contents)

        return {
            "image_id": image_id,
            "width": width,
            "height": height,
            "format": format
        }

    except Exception as e:
        logger.error(f"Error uploading image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading image: {str(e)}")

@router.get("/detect/{image_id}", response_model=LandmarkDetectionResponse)
async def detect_facial_landmarks(image_id: str):
    """
    Detect facial landmarks in an uploaded image.
    """
    try:
        # Detect landmarks
        landmarks = detect_landmarks(image_id)

        if landmarks is None:
            return {
                "image_id": image_id,
                "landmarks": None,
                "error": "No face detected in the image"
            }

        return {
            "image_id": image_id,
            "landmarks": landmarks,
            "error": None
        }

    except Exception as e:
        logger.error(f"Error detecting landmarks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error detecting landmarks: {str(e)}")

@router.post("/update-crown-chin", response_model=CrownChinPointPair)
async def update_crown_chin(request: LandmarkUpdateRequest):
    """
    Update the crown and chin points for an image.
    """
    try:
        # Update the crown and chin points
        crown_chin = update_crown_chin_points(
            request.image_id,
            request.crown_point,
            request.chin_point
        )

        if crown_chin is None:
            raise HTTPException(
                status_code=404,
                detail=f"Image with ID {request.image_id} not found or landmarks not detected"
            )

        return crown_chin

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating crown-chin points: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating crown-chin points: {str(e)}")

@router.get("/aligned-image/{image_id}")
async def get_aligned_face_image(image_id: str):
    """
    Get the image with landmarks for an image ID.

    This endpoint now returns the original image without rotation.
    The name is kept for compatibility with the frontend.
    """
    try:
        # Get the image from the image store
        if image_id not in image_store:
            raise HTTPException(
                status_code=404,
                detail=f"Image with ID {image_id} not found"
            )

        # Get the original image (make a copy to avoid modifying the original)
        img = image_store[image_id]['image'].copy()

        # Convert the image to JPEG format
        success, buffer = cv2.imencode('.jpg', img)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to encode the image"
            )

        # Return the image as a response
        return Response(
            content=buffer.tobytes(),
            media_type="image/jpeg"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting image: {str(e)}")

@router.get("/visualization/{image_id}")
async def get_landmarks_visualization_image(image_id: str):
    """
    Get an image with landmarks visualized for debugging and verification.

    Returns an image with all detected landmarks drawn on it, including the crown and chin points.
    This is useful for debugging and verifying the landmark detection.
    The image is not rotated, and the landmarks are shown on the original image.
    """
    try:
        # Get the visualization image
        vis_img = get_landmarks_visualization(image_id)

        if vis_img is None:
            raise HTTPException(
                status_code=404,
                detail=f"Image with ID {image_id} not found or landmarks not detected"
            )

        # Convert the image to JPEG format
        success, buffer = cv2.imencode('.jpg', vis_img)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to encode the visualization image"
            )

        # Return the image as a response
        return Response(
            content=buffer.tobytes(),
            media_type="image/jpeg"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting landmarks visualization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting landmarks visualization: {str(e)}")
