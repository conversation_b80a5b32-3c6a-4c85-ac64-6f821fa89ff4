import logging
import requests
from fastapi import APIRouter, Request
from app.models.location import LocationResponse

router = APIRouter(prefix="/api", tags=["api"])
logger = logging.getLogger(__name__)

@router.get("/ping")
async def ping():
    """
    Simple ping endpoint to check if the API is running.
    """
    return {"ping": "success"}

@router.get("/location", response_model=LocationResponse)
async def get_location(request: Request):
    """
    Get the user's location based on IP geolocation.
    Uses multiple reliable geolocation APIs with proper error handling.
    """
    # Get the client's IP address
    ip = request.headers.get("x-forwarded-for")
    if not ip:
        ip = request.client.host

    # For local development, try to get the actual external IP
    if ip == "127.0.0.1" or ip.startswith("192.168.") or ip.startswith("10."):
        try:
            # Try to get the external IP using a reliable service
            logger.info("Local IP detected, attempting to get external IP")
            response = requests.get("https://api.ipify.org", timeout=5)
            if response.status_code == 200:
                ip = response.text.strip()
                logger.info(f"Using external IP from ipify: {ip}")
            else:
                # Try another service
                response = requests.get("https://ifconfig.me", timeout=5)
                if response.status_code == 200:
                    ip = response.text.strip()
                    logger.info(f"Using external IP from ifconfig.me: {ip}")
        except Exception as e:
            logger.warning(f"Could not determine external IP: {e}")
            # Don't use a default IP, just continue with the local IP

    logger.info(f"Attempting to geolocate IP: {ip}")

    # Try multiple geolocation APIs in order of reliability

    # 1. Try ipinfo.io (very reliable)
    try:
        url = f"https://ipinfo.io/{ip}/json"
        logger.info(f"Requesting {url}")
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        data = response.json()

        if "country" in data:
            # Map country code to continent
            country_to_continent = {
                # Europe
                "AD": "Europe", "AL": "Europe", "AT": "Europe", "BA": "Europe", "BE": "Europe",
                "BG": "Europe", "BY": "Europe", "CH": "Europe", "CY": "Europe", "CZ": "Europe",
                "DE": "Europe", "DK": "Europe", "EE": "Europe", "ES": "Europe", "FI": "Europe",
                "FR": "Europe", "GB": "Europe", "GR": "Europe", "HR": "Europe", "HU": "Europe",
                "IE": "Europe", "IS": "Europe", "IT": "Europe", "LI": "Europe", "LT": "Europe",
                "LU": "Europe", "LV": "Europe", "MC": "Europe", "MD": "Europe", "ME": "Europe",
                "MK": "Europe", "MT": "Europe", "NL": "Europe", "NO": "Europe", "PL": "Europe",
                "PT": "Europe", "RO": "Europe", "RS": "Europe", "RU": "Europe", "SE": "Europe",
                "SI": "Europe", "SK": "Europe", "SM": "Europe", "UA": "Europe", "VA": "Europe",
                "XK": "Europe",

                # Asia
                "AE": "Asia", "AF": "Asia", "AM": "Asia", "AZ": "Asia", "BD": "Asia",
                "BH": "Asia", "BN": "Asia", "BT": "Asia", "CN": "Asia", "GE": "Asia",
                "HK": "Asia", "ID": "Asia", "IL": "Asia", "IN": "Asia", "IQ": "Asia",
                "IR": "Asia", "JO": "Asia", "JP": "Asia", "KG": "Asia", "KH": "Asia",
                "KP": "Asia", "KR": "Asia", "KW": "Asia", "KZ": "Asia", "LA": "Asia",
                "LB": "Asia", "LK": "Asia", "MM": "Asia", "MN": "Asia", "MO": "Asia",
                "MV": "Asia", "MY": "Asia", "NP": "Asia", "OM": "Asia", "PH": "Asia",
                "PK": "Asia", "PS": "Asia", "QA": "Asia", "SA": "Asia", "SG": "Asia",
                "SY": "Asia", "TH": "Asia", "TJ": "Asia", "TL": "Asia", "TM": "Asia",
                "TR": "Asia", "TW": "Asia", "UZ": "Asia", "VN": "Asia", "YE": "Asia",

                # Africa
                "AO": "Africa", "BF": "Africa", "BI": "Africa", "BJ": "Africa", "BW": "Africa",
                "CD": "Africa", "CF": "Africa", "CG": "Africa", "CI": "Africa", "CM": "Africa",
                "CV": "Africa", "DJ": "Africa", "DZ": "Africa", "EG": "Africa", "EH": "Africa",
                "ER": "Africa", "ET": "Africa", "GA": "Africa", "GH": "Africa", "GM": "Africa",
                "GN": "Africa", "GQ": "Africa", "GW": "Africa", "KE": "Africa", "KM": "Africa",
                "LR": "Africa", "LS": "Africa", "LY": "Africa", "MA": "Africa", "MG": "Africa",
                "ML": "Africa", "MR": "Africa", "MU": "Africa", "MW": "Africa", "MZ": "Africa",
                "NA": "Africa", "NE": "Africa", "NG": "Africa", "RE": "Africa", "RW": "Africa",
                "SC": "Africa", "SD": "Africa", "SL": "Africa", "SN": "Africa", "SO": "Africa",
                "SS": "Africa", "ST": "Africa", "SZ": "Africa", "TD": "Africa", "TG": "Africa",
                "TN": "Africa", "TZ": "Africa", "UG": "Africa", "ZA": "Africa", "ZM": "Africa",
                "ZW": "Africa",

                # North America
                "AG": "North America", "BB": "North America", "BS": "North America", "BZ": "North America",
                "CA": "North America", "CR": "North America", "CU": "North America", "DM": "North America",
                "DO": "North America", "GD": "North America", "GT": "North America", "HN": "North America",
                "HT": "North America", "JM": "North America", "KN": "North America", "LC": "North America",
                "MX": "North America", "NI": "North America", "PA": "North America", "SV": "North America",
                "TT": "North America", "US": "North America", "VC": "North America",

                # South America
                "AR": "South America", "BO": "South America", "BR": "South America", "CL": "South America",
                "CO": "South America", "EC": "South America", "GY": "South America", "PE": "South America",
                "PY": "South America", "SR": "South America", "UY": "South America", "VE": "South America",

                # Oceania
                "AU": "Oceania", "FJ": "Oceania", "FM": "Oceania", "KI": "Oceania", "MH": "Oceania",
                "NR": "Oceania", "NZ": "Oceania", "PG": "Oceania", "PW": "Oceania", "SB": "Oceania",
                "TO": "Oceania", "TV": "Oceania", "VU": "Oceania", "WS": "Oceania",

                # Antarctica
                "AQ": "Antarctica"
            }

            country_code = data.get("country")
            continent = country_to_continent.get(country_code, "Unknown")

            logger.info(f"Successfully located IP using ipinfo.io: {data.get('country')}, {continent}")

            # Get country name from country code
            country_name = None
            try:
                country_response = requests.get(f"https://restcountries.com/v3.1/alpha/{country_code}", timeout=3)
                if country_response.status_code == 200:
                    country_data = country_response.json()
                    if country_data and len(country_data) > 0:
                        country_name = country_data[0].get("name", {}).get("common")
            except Exception as e:
                logger.warning(f"Could not get country name: {e}")

            return {
                "alpha2": country_code,
                "alpha3": None,  # ipinfo.io doesn't provide ISO3
                "name": country_name or data.get("country"),
                "continent": continent,
                "city": data.get("city"),
                "region": data.get("region")
            }
    except Exception as err:
        logger.error(f"Error with ipinfo.io: {err}")

    # 2. Try ip-api.com as a fallback
    try:
        url = f"http://ip-api.com/json/{ip}"
        logger.info(f"Requesting {url}")
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        data = response.json()

        # Check if we got a valid response
        if data.get("status") == "success":
            logger.info(f"Successfully located IP using ip-api.com: {data.get('country')}, {data.get('continent')}")
            # Map continent code to name
            continent_map = {
                "NA": "North America",
                "SA": "South America",
                "EU": "Europe",
                "AS": "Asia",
                "AF": "Africa",
                "OC": "Oceania",
                "AN": "Antarctica"
            }
            continent = continent_map.get(data.get("continentCode"), "Unknown")

            return {
                "alpha2": data.get("countryCode"),
                "alpha3": None,  # This API doesn't provide ISO3
                "name": data.get("country"),
                "continent": continent,
                "city": data.get("city"),
                "region": data.get("regionName")
            }
    except Exception as err:
        logger.error(f"Error with ip-api.com: {err}")

    # 3. Try ipapi.co as another fallback
    try:
        url = f"https://ipapi.co/{ip}/json/"
        logger.info(f"Requesting {url}")
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        data = response.json()

        # Check if we got a valid response
        if "error" not in data and "country_code" in data:
            logger.info(f"Successfully located IP using ipapi.co: {data.get('country_name')}, {data.get('continent_code')}")
            return {
                "alpha2": data.get("country_code"),
                "alpha3": data.get("country_code_iso3"),
                "name": data.get("country_name"),
                "continent": data.get("continent_name", "Unknown"),
                "city": data.get("city"),
                "region": data.get("region")
            }
    except Exception as err:
        logger.error(f"Error with ipapi.co: {err}")

    # 4. Last resort - try the original API
    try:
        url = f"https://api.ipgeolocationapi.com/geolocate/{ip}"
        logger.info(f"Requesting {url}")
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        data = response.json()
        logger.info(f"Successfully located IP using ipgeolocationapi.com: {data.get('name')}, {data.get('continent')}")
        return data
    except Exception as err:
        logger.error(f"Error with ipgeolocationapi.com: {err}")

    # If all APIs fail, return a response indicating an error
    logger.error("All geolocation APIs failed")
    return {
        "error": 1,
        "alpha2": None,
        "alpha3": None,
        "name": "Unknown",
        "continent": "Unknown",
        "city": None,
        "region": None,
        "message": "Could not determine location"
    }


