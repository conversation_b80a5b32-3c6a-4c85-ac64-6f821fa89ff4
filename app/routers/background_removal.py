import logging
from fastapi import APIRouter, HTTPException, Response
from pydantic import BaseModel
from typing import Optional, Dict, Any

from app.services.background_removal import background_removal_service
from app.services.landmark_detection import image_store

router = APIRouter(prefix="/api/background-removal", tags=["background-removal"])
logger = logging.getLogger(__name__)


class BackgroundRemovalRequest(BaseModel):
    """Request model for background removal."""
    image_id: str


class BackgroundRemovalResponse(BaseModel):
    """Response model for background removal."""
    success: bool
    processed_image_id: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None


class BackgroundRemovalStatusResponse(BaseModel):
    """Response model for background removal status."""
    enabled: bool
    message: Optional[str] = None


@router.get("/status", response_model=BackgroundRemovalStatusResponse)
async def get_background_removal_status():
    """
    Get the status of background removal service.
    """
    enabled = background_removal_service.is_enabled()
    message = "Background removal is available" if enabled else "Background removal is not configured"
    
    return BackgroundRemovalStatusResponse(
        enabled=enabled,
        message=message
    )


@router.post("/remove", response_model=BackgroundRemovalResponse)
async def remove_background(request: BackgroundRemovalRequest):
    """
    Remove background from an uploaded image.
    """
    try:
        # Check if service is enabled
        if not background_removal_service.is_enabled():
            raise HTTPException(
                status_code=503, 
                detail="Background removal service is not available"
            )
        
        # Check if image exists
        if request.image_id not in image_store:
            raise HTTPException(
                status_code=404, 
                detail=f"Image with ID {request.image_id} not found"
            )
        
        # Remove background
        logger.info(f"Processing background removal for image {request.image_id}")
        processed_image_id, error = await background_removal_service.remove_background_async(request.image_id)
        
        if error:
            logger.error(f"Background removal failed for image {request.image_id}: {error}")
            return BackgroundRemovalResponse(
                success=False,
                error=error
            )
        
        logger.info(f"Background removal successful for image {request.image_id}, new ID: {processed_image_id}")
        return BackgroundRemovalResponse(
            success=True,
            processed_image_id=processed_image_id,
            message="Background removed successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in background removal: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/preview/{image_id}")
async def get_background_removed_preview(image_id: str):
    """
    Get a preview of the background-removed image.
    """
    try:
        # Check if image exists
        if image_id not in image_store:
            raise HTTPException(
                status_code=404, 
                detail=f"Image with ID {image_id} not found"
            )
        
        # Get the image from store
        img_data = image_store[image_id]
        img = img_data['image']
        
        # Check if this is a background-removed image
        is_background_removed = img_data.get('background_removed', False)
        
        # Convert the image to JPEG bytes
        import cv2
        success, buffer = cv2.imencode(".jpg", img, [cv2.IMWRITE_JPEG_QUALITY, 95])
        if not success:
            raise HTTPException(status_code=500, detail="Failed to encode image")
        
        # Set headers to indicate background removal status
        headers = {
            "X-Background-Removed": str(is_background_removed).lower(),
            "X-Original-Image-ID": img_data.get('original_image_id', ''),
        }
        
        return Response(
            content=buffer.tobytes(), 
            media_type="image/jpeg",
            headers=headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting preview for image {image_id}: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error getting image preview: {str(e)}"
        )


@router.get("/compare/{original_id}/{processed_id}")
async def compare_images(original_id: str, processed_id: str):
    """
    Get a side-by-side comparison of original and background-removed images.
    """
    try:
        # Check if both images exist
        if original_id not in image_store:
            raise HTTPException(
                status_code=404, 
                detail=f"Original image with ID {original_id} not found"
            )
        
        if processed_id not in image_store:
            raise HTTPException(
                status_code=404, 
                detail=f"Processed image with ID {processed_id} not found"
            )
        
        # Get both images
        original_img = image_store[original_id]['image']
        processed_img = image_store[processed_id]['image']
        
        # Create side-by-side comparison
        import cv2
        import numpy as np
        
        # Resize images to same height for comparison
        h1, w1 = original_img.shape[:2]
        h2, w2 = processed_img.shape[:2]
        
        target_height = min(h1, h2, 400)  # Limit height for web display
        
        # Resize original
        aspect1 = w1 / h1
        new_w1 = int(target_height * aspect1)
        original_resized = cv2.resize(original_img, (new_w1, target_height))
        
        # Resize processed
        aspect2 = w2 / h2
        new_w2 = int(target_height * aspect2)
        processed_resized = cv2.resize(processed_img, (new_w2, target_height))
        
        # Create comparison image
        comparison = np.hstack([original_resized, processed_resized])
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Original", (10, 30), font, 1, (0, 0, 255), 2)
        cv2.putText(comparison, "Background Removed", (new_w1 + 10, 30), font, 1, (0, 255, 0), 2)
        
        # Encode as JPEG
        success, buffer = cv2.imencode(".jpg", comparison, [cv2.IMWRITE_JPEG_QUALITY, 95])
        if not success:
            raise HTTPException(status_code=500, detail="Failed to encode comparison image")
        
        return Response(
            content=buffer.tobytes(), 
            media_type="image/jpeg",
            headers={"X-Comparison": "true"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating comparison for images {original_id} and {processed_id}: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error creating image comparison: {str(e)}"
        )
