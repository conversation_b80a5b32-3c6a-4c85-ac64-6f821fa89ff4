import os
import json
import pytest
import cv2
import numpy as np
from fastapi.testclient import TestClient

from app.main import app
from app.services import photostandard_service, photo_processing
from app.services.landmark_detection import image_store, detect_landmarks

client = TestClient(app)


def create_test_image():
    """Create a test image with a face."""
    # Create a simple test image (white background)
    img = np.ones((500, 400, 3), dtype=np.uint8) * 255
    
    # Draw a simple face (circle for head, dots for eyes, line for mouth)
    cv2.circle(img, (200, 200), 100, (220, 220, 220), -1)  # Head
    cv2.circle(img, (170, 180), 10, (0, 0, 0), -1)  # Left eye
    cv2.circle(img, (230, 180), 10, (0, 0, 0), -1)  # Right eye
    cv2.line(img, (170, 230), (230, 230), (0, 0, 0), 3)  # Mouth
    
    return img


def setup_test_image():
    """Set up a test image with landmarks."""
    # Create a test image
    img = create_test_image()
    
    # Store the image
    image_id = "test_image"
    image_store[image_id] = {
        "image": img,
        "width": img.shape[1],
        "height": img.shape[0],
        "format": "JPEG"
    }
    
    # Add mock landmarks
    landmarks = {
        "crown_chin": {
            "crownPoint": {"x": 200, "y": 100},
            "chinPoint": {"x": 200, "y": 300}
        },
        "key_points": {
            "A": {"x": 170, "y": 180},
            "B": {"x": 230, "y": 180}
        }
    }
    
    # Convert landmarks to the expected format
    from app.models.landmarks import Point, CrownChinPointPair, KeyPoints, Landmarks
    
    crown_point = Point(x=landmarks["crown_chin"]["crownPoint"]["x"], 
                        y=landmarks["crown_chin"]["crownPoint"]["y"])
    chin_point = Point(x=landmarks["crown_chin"]["chinPoint"]["x"], 
                       y=landmarks["crown_chin"]["chinPoint"]["y"])
    crown_chin = CrownChinPointPair(crownPoint=crown_point, chinPoint=chin_point)
    
    key_points = KeyPoints(
        A=Point(x=landmarks["key_points"]["A"]["x"], y=landmarks["key_points"]["A"]["y"]),
        B=Point(x=landmarks["key_points"]["B"]["x"], y=landmarks["key_points"]["B"]["y"])
    )
    
    image_store[image_id]["landmarks"] = Landmarks(crown_chin=crown_chin, key_points=key_points)
    
    return image_id


def test_crop_photo_to_standard():
    """Test cropping a photo to a standard."""
    # Set up a test image
    image_id = setup_test_image()
    
    # Get the first standard from the list
    standards = photostandard_service.load_photo_standards()
    standard_id = standards[0].id
    
    # Process the photo
    processed_image_id, error = photo_processing.crop_photo_to_standard(
        image_id,
        standard_id
    )
    
    assert error is None
    assert processed_image_id is not None
    assert processed_image_id in image_store
    
    # Check the processed image
    processed_img = image_store[processed_image_id]["image"]
    assert processed_img is not None
    assert processed_img.shape[0] > 0
    assert processed_img.shape[1] > 0


def test_create_tiled_print():
    """Test creating a tiled print."""
    # Set up a test image
    image_id = setup_test_image()
    
    # Get the first standard from the list
    standards = photostandard_service.load_photo_standards()
    standard_id = standards[0].id
    
    # Process the photo
    processed_image_id, error = photo_processing.crop_photo_to_standard(
        image_id,
        standard_id
    )
    
    assert error is None
    
    # Create a tiled print
    tiled_image_id, error = photo_processing.create_tiled_print(
        processed_image_id,
        2,
        2,
        2.0
    )
    
    assert error is None
    assert tiled_image_id is not None
    assert tiled_image_id in image_store
    
    # Check the tiled image
    tiled_img = image_store[tiled_image_id]["image"]
    assert tiled_img is not None
    assert tiled_img.shape[0] > image_store[processed_image_id]["image"].shape[0]
    assert tiled_img.shape[1] > image_store[processed_image_id]["image"].shape[1]


def test_process_photo_endpoint():
    """Test the POST /api/photo/process endpoint."""
    # Set up a test image
    image_id = setup_test_image()
    
    # Get the first standard from the list
    standards = photostandard_service.load_photo_standards()
    standard_id = standards[0].id
    
    # Process the photo
    response = client.post(
        "/api/photo/process",
        json={
            "image_id": image_id,
            "standard_id": standard_id
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "processed_image_id" in data
    assert data["processed_image_id"] != ""
    assert data["error"] is None


def test_tiled_print_endpoint():
    """Test the POST /api/photo/tiled-print endpoint."""
    # Set up a test image
    image_id = setup_test_image()
    
    # Get the first standard from the list
    standards = photostandard_service.load_photo_standards()
    standard_id = standards[0].id
    
    # Process the photo
    response = client.post(
        "/api/photo/process",
        json={
            "image_id": image_id,
            "standard_id": standard_id
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    processed_image_id = data["processed_image_id"]
    
    # Create a tiled print
    response = client.post(
        "/api/photo/tiled-print",
        json={
            "image_id": processed_image_id,
            "num_rows": 2,
            "num_cols": 2,
            "spacing_mm": 2.0
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "tiled_image_id" in data
    assert data["tiled_image_id"] != ""
    assert data["error"] is None
