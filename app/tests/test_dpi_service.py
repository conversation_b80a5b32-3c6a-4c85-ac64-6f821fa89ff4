"""
Unit tests for the DPI Service.
"""
import unittest
import os
import tempfile
import numpy as np
import cv2
from app.services.dpi_service import DpiService, PrinterType


class TestDpiService(unittest.TestCase):
    """Test cases for the DPI Service."""

    def test_get_optimal_dpi(self):
        """Test that get_optimal_dpi returns the higher DPI value."""
        self.assertEqual(DpiService.get_optimal_dpi(300, 600), 600)
        self.assertEqual(DpiService.get_optimal_dpi(600, 300), 600)
        self.assertEqual(DpiService.get_optimal_dpi(300, 300), 300)
        self.assertEqual(DpiService.get_optimal_dpi(0, 300), 300)

    def test_get_recommended_dpi(self):
        """Test that get_recommended_dpi returns the correct DPI for printer types."""
        self.assertEqual(DpiService.get_recommended_dpi(PrinterType.INKJET), 300)
        self.assertEqual(DpiService.get_recommended_dpi(PrinterType.LASER), 600)
        self.assertEqual(DpiService.get_recommended_dpi(PrinterType.PHOTO), 300)
        self.assertEqual(DpiService.get_recommended_dpi(PrinterType.PROFESSIONAL), 1200)

    def test_convert_to_pixels(self):
        """Test conversion from physical units to pixels."""
        # Test inch to pixels
        self.assertEqual(DpiService.convert_to_pixels(1, "inch", 300), 300)
        
        # Test mm to pixels
        self.assertAlmostEqual(DpiService.convert_to_pixels(25.4, "mm", 300), 300, delta=0.1)
        
        # Test cm to pixels
        self.assertAlmostEqual(DpiService.convert_to_pixels(2.54, "cm", 300), 300, delta=0.1)
        
        # Test pixels to pixels (no conversion)
        self.assertEqual(DpiService.convert_to_pixels(100, "pixel", 300), 100)
        
        # Test invalid unit
        with self.assertRaises(ValueError):
            DpiService.convert_to_pixels(1, "invalid", 300)

    def test_convert_from_pixels(self):
        """Test conversion from pixels to physical units."""
        # Test pixels to inches
        self.assertEqual(DpiService.convert_from_pixels(300, "inch", 300), 1)
        
        # Test pixels to mm
        self.assertAlmostEqual(DpiService.convert_from_pixels(300, "mm", 300), 25.4, delta=0.1)
        
        # Test pixels to cm
        self.assertAlmostEqual(DpiService.convert_from_pixels(300, "cm", 300), 2.54, delta=0.1)
        
        # Test pixels to pixels (no conversion)
        self.assertEqual(DpiService.convert_from_pixels(100, "pixel", 300), 100)
        
        # Test invalid unit
        with self.assertRaises(ValueError):
            DpiService.convert_from_pixels(300, "invalid", 300)

    def test_convert_units(self):
        """Test conversion between different units."""
        # Test inch to mm
        self.assertAlmostEqual(DpiService.convert_units(1, "inch", "mm", 300), 25.4, delta=0.1)
        
        # Test mm to inch
        self.assertAlmostEqual(DpiService.convert_units(25.4, "mm", "inch", 300), 1, delta=0.01)
        
        # Test cm to mm
        self.assertEqual(DpiService.convert_units(1, "cm", "mm", 300), 10)
        
        # Test same unit (no conversion)
        self.assertEqual(DpiService.convert_units(10, "mm", "mm", 300), 10)

    def test_embed_dpi_in_png(self):
        """Test embedding DPI in PNG images."""
        # Create a test image
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        # Encode as PNG
        _, png_data = cv2.imencode('.png', test_image)
        png_bytes = png_data.tobytes()
        
        # Embed DPI
        dpi = 300
        png_with_dpi = DpiService._embed_dpi_in_png(png_bytes, dpi)
        
        # Verify pHYs chunk exists
        self.assertIn(b'pHYs', png_with_dpi)
        
        # Test with existing pHYs chunk
        png_with_dpi_again = DpiService._embed_dpi_in_png(png_with_dpi, 600)
        self.assertIn(b'pHYs', png_with_dpi_again)
        
        # Verify the two are different (DPI was updated)
        self.assertNotEqual(png_with_dpi, png_with_dpi_again)

    def test_embed_dpi_in_jpeg(self):
        """Test embedding DPI in JPEG images."""
        # Create a test image
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        # Encode as JPEG
        _, jpeg_data = cv2.imencode('.jpg', test_image)
        jpeg_bytes = jpeg_data.tobytes()
        
        # Embed DPI
        dpi = 300
        jpeg_with_dpi = DpiService._embed_dpi_in_jpeg(jpeg_bytes, dpi)
        
        # Verify JFIF marker exists
        self.assertIn(b'JFIF', jpeg_with_dpi)
        
        # Test with different DPI
        jpeg_with_dpi_again = DpiService._embed_dpi_in_jpeg(jpeg_with_dpi, 600)
        
        # Verify the two are different (DPI was updated)
        self.assertNotEqual(jpeg_with_dpi, jpeg_with_dpi_again)

    def test_embed_dpi_in_image(self):
        """Test the main embed_dpi_in_image function."""
        # Create a test image
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        # Test with PNG format
        png_with_dpi = DpiService.embed_dpi_in_image(test_image, 300, "PNG")
        self.assertIn(b'pHYs', png_with_dpi)
        
        # Test with JPEG format
        jpeg_with_dpi = DpiService.embed_dpi_in_image(test_image, 300, "JPEG")
        self.assertIn(b'JFIF', jpeg_with_dpi)
        
        # Test with invalid format
        with self.assertRaises(ValueError):
            DpiService.embed_dpi_in_image(test_image, 300, "INVALID")


if __name__ == '__main__':
    unittest.main()
