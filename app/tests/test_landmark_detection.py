import unittest
import os
import sys
import numpy as np
from PIL import Image, ExifTags
from io import BytesIO

# Add the parent directory to the path so we can import the app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.landmark_detection import save_uploaded_image, detect_landmarks

class TestLandmarkDetection(unittest.TestCase):
    def test_exif_orientation_correction(self):
        """Test that EXIF orientation is correctly handled when saving an image"""
        # Create a simple test image
        img = Image.new('RGB', (100, 200), color='red')
        
        # Add a horizontal line to make orientation clear
        for x in range(100):
            img.putpixel((x, 100), (255, 255, 255))
        
        # Save the image to a BytesIO object
        img_bytes = BytesIO()
        img.save(img_bytes, format='JPEG')
        img_bytes.seek(0)
        
        # Save the image using our function
        image_id, width, height, format = save_uploaded_image(img_bytes.getvalue())
        
        # Check that the dimensions are correct
        self.assertEqual(width, 100)
        self.assertEqual(height, 200)
        
        # Now create a rotated image with EXIF orientation
        img_rotated = img.transpose(Image.Transpose.ROTATE_90)
        img_bytes_rotated = BytesIO()
        
        # Save with EXIF orientation = 6 (90 degrees counter-clockwise)
        exif = img_rotated.getexif()
        for orientation in ExifTags.TAGS.keys():
            if ExifTags.TAGS[orientation] == 'Orientation':
                exif[orientation] = 6
                break
        
        img_rotated.save(img_bytes_rotated, format='JPEG', exif=exif)
        img_bytes_rotated.seek(0)
        
        # Save the rotated image using our function
        image_id_rotated, width_rotated, height_rotated, format_rotated = save_uploaded_image(img_bytes_rotated.getvalue())
        
        # Check that the dimensions are corrected back to the original
        self.assertEqual(width_rotated, 100)
        self.assertEqual(height_rotated, 200)
        
        print("EXIF orientation correction test passed!")

if __name__ == '__main__':
    unittest.main()
