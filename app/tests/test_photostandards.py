import os
import json
import pytest
from fastapi.testclient import Test<PERSON>lient

from app.main import app
from app.services import photostandard_service

client = TestClient(app)


def test_load_photo_standards():
    """Test loading photo standards from the JSON file."""
    standards = photostandard_service.load_photo_standards()
    assert len(standards) > 0
    assert standards[0].id is not None
    assert standards[0].dimensions is not None


def test_get_photo_standard():
    """Test getting a photo standard by ID."""
    # Get the first standard from the list
    standards = photostandard_service.load_photo_standards()
    first_standard_id = standards[0].id
    
    # Get the standard by ID
    standard = photostandard_service.get_photo_standard(first_standard_id)
    assert standard is not None
    assert standard.id == first_standard_id


def test_get_photo_standards_endpoint():
    """Test the GET /api/photostandards endpoint."""
    response = client.get("/api/photostandards")
    assert response.status_code == 200
    data = response.json()
    assert "standards" in data
    assert "count" in data
    assert data["count"] > 0
    assert len(data["standards"]) == data["count"]


def test_get_photo_standard_endpoint():
    """Test the GET /api/photostandards/{standard_id} endpoint."""
    # Get the first standard from the list
    standards = photostandard_service.load_photo_standards()
    first_standard_id = standards[0].id
    
    # Get the standard by ID
    response = client.get(f"/api/photostandards/{first_standard_id}")
    assert response.status_code == 200
    data = response.json()
    assert "standard" in data
    assert data["standard"]["id"] == first_standard_id


def test_get_countries_endpoint():
    """Test the GET /api/photostandards/countries endpoint."""
    response = client.get("/api/photostandards/countries")
    assert response.status_code == 200
    countries = response.json()
    assert len(countries) > 0
    assert isinstance(countries, list)


def test_get_doc_types_endpoint():
    """Test the GET /api/photostandards/doctypes endpoint."""
    response = client.get("/api/photostandards/doctypes")
    assert response.status_code == 200
    doc_types = response.json()
    assert len(doc_types) > 0
    assert isinstance(doc_types, list)
