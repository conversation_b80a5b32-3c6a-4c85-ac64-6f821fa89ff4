import pytest
import uuid
import numpy as np
import cv2
from fastapi.testclient import TestClient

from app.main import app
from app.services.landmark_detection import image_store
from app.services import print_definition_service
from app.models.landmarks import Point, CrownChinPoints
from app.models.print_definition import PrintDefinition

client = TestClient(app)


def setup_test_image():
    """Set up a test image for testing."""
    # Create a simple test image
    img = np.ones((300, 200, 3), dtype=np.uint8) * 255  # White image
    
    # Draw a face-like shape
    cv2.circle(img, (100, 100), 50, (0, 0, 0), 2)  # Head
    cv2.circle(img, (80, 80), 5, (0, 0, 0), -1)    # Left eye
    cv2.circle(img, (120, 80), 5, (0, 0, 0), -1)   # Right eye
    cv2.line(img, (90, 120), (110, 120), (0, 0, 0), 2)  # Mouth
    
    # Generate a unique ID for the image
    image_id = str(uuid.uuid4())
    
    # Store the image
    image_store[image_id] = {
        "image": img,
        "width": 200,
        "height": 300,
        "format": "JPEG",
        "landmarks": None
    }
    
    # Add landmarks
    crown_point = Point(x=100, y=50)
    chin_point = Point(x=100, y=150)
    
    # Create crown-chin points
    crown_chin = CrownChinPoints(
        crownPoint=crown_point,
        chinPoint=chin_point
    )
    
    # Add landmarks to the image
    image_store[image_id]["landmarks"] = type('obj', (object,), {
        'crown_chin': crown_chin,
        'key_points': type('obj', (object,), {
            'A': Point(x=80, y=80),
            'B': Point(x=120, y=80)
        })
    })
    
    return image_id


def test_get_print_definitions():
    """Test getting all print definitions."""
    response = client.get("/api/print/definitions")
    assert response.status_code == 200
    data = response.json()
    assert "print_definitions" in data
    assert data["error"] is None
    assert isinstance(data["print_definitions"], list)


def test_create_print_definition():
    """Test creating a custom print definition."""
    print_def = {
        "title": "Test Print",
        "height": 6,
        "width": 4,
        "resolution": 300,
        "units": "inch",
        "padding": 0.1,
        "gutter": 0.2,
        "paper_color": "#ffffff"
    }
    
    response = client.post("/api/print/definitions", json=print_def)
    assert response.status_code == 200
    data = response.json()
    assert "print_definition" in data
    assert data["error"] is None
    assert data["print_definition"]["title"] == "Test Print"
    assert data["print_definition"]["custom"] is True
    
    # Store the ID for later tests
    print_definition_id = data["print_definition"]["id"]
    return print_definition_id


def test_get_print_definition():
    """Test getting a specific print definition."""
    # First create a print definition
    print_definition_id = test_create_print_definition()
    
    # Then get it
    response = client.get(f"/api/print/definitions/{print_definition_id}")
    assert response.status_code == 200
    data = response.json()
    assert "print_definition" in data
    assert data["error"] is None
    assert data["print_definition"]["id"] == print_definition_id


def test_update_print_definition():
    """Test updating a print definition."""
    # First create a print definition
    print_definition_id = test_create_print_definition()
    
    # Then update it
    update_data = {
        "title": "Updated Test Print",
        "height": 5,
        "width": 7
    }
    
    response = client.put(f"/api/print/definitions/{print_definition_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert "print_definition" in data
    assert data["error"] is None
    assert data["print_definition"]["title"] == "Updated Test Print"
    assert data["print_definition"]["height"] == 5
    assert data["print_definition"]["width"] == 7


def test_create_tiled_print():
    """Test creating a tiled print."""
    # Set up a test image
    image_id = setup_test_image()
    
    # Create a tiled print
    response = client.post(
        "/api/print/create-tiled-print",
        params={
            "image_id": image_id,
            "num_rows": 2,
            "num_cols": 2,
            "spacing_mm": 2.0
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "tiled_image_id" in data
    assert data["error"] is None
    assert "width" in data
    assert "height" in data
    assert data["format"] == "JPEG"
    
    # Get the tiled image
    tiled_image_id = data["tiled_image_id"]
    response = client.get(f"/api/print/image/{tiled_image_id}")
    assert response.status_code == 200
    assert response.headers["content-type"] == "image/jpeg"


def test_create_tiled_print_with_definition():
    """Test creating a tiled print using a print definition."""
    # Set up a test image
    image_id = setup_test_image()
    
    # Create a print definition
    print_definition_id = test_create_print_definition()
    
    # Create a tiled print using the print definition
    response = client.post(
        "/api/print/create-tiled-print",
        params={
            "image_id": image_id,
            "print_definition_id": print_definition_id
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "tiled_image_id" in data
    assert data["error"] is None
    
    # Get the tiled image
    tiled_image_id = data["tiled_image_id"]
    response = client.get(f"/api/print/image/{tiled_image_id}")
    assert response.status_code == 200
    assert response.headers["content-type"] == "image/jpeg"


def test_delete_print_definition():
    """Test deleting a print definition."""
    # First create a print definition
    print_definition_id = test_create_print_definition()
    
    # Then delete it
    response = client.delete(f"/api/print/definitions/{print_definition_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["error"] is None
    
    # Try to get it again - should fail
    response = client.get(f"/api/print/definitions/{print_definition_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["print_definition"] is None
    assert data["error"] is not None
