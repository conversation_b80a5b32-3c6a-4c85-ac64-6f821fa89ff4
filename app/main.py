import os
import sys
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, Response
import cv2

# Import routers

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.routers import api, landmarks, photostandards, photo_processing, photo, print

# Create FastAPI app
app = FastAPI(title="Passport Photo Processor API")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, you should specify the allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(api.router)
app.include_router(landmarks.router)
app.include_router(photostandards.router)
app.include_router(photo_processing.router)
app.include_router(photo.router)
app.include_router(print.router)

# Configure static files
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

# Root route to serve the frontend
from fastapi import Request
from fastapi.templating import Jinja2Templates
from app.services.photostandard_service import get_all_photo_standards

# Set up Jinja2 templates
templates = Jinja2Templates(directory=os.path.join(os.path.dirname(__file__), "templates"))

@app.get("/")
@app.get("/start")
@app.get("/settings")
@app.get("/about")
@app.get("/standards")
@app.get("/prints")
@app.get("/walgreens")
@app.get("/edit")
async def catch_all():
    return FileResponse(os.path.join(os.path.dirname(__file__), "static/index.html"))

@app.get("/crop")
async def crop_page(request: Request):
    """
    Render the crop page with photo standards.
    """
    standards = get_all_photo_standards()
    return templates.TemplateResponse(
        "crop.html",
        {"request": request, "standards": standards}
    )

@app.get("/canvas-crop")
async def canvas_crop_page(request: Request):
    """
    Render the canvas crop page with photo standards.
    """
    standards = get_all_photo_standards()
    return templates.TemplateResponse(
        "canvas_crop.html",
        {"request": request, "standards": standards}
    )

@app.get("/preview-test")
async def preview_test_page(request: Request):
    """
    Render the preview test page for testing the SVG preview functionality.
    """
    return templates.TemplateResponse(
        "preview_test.html",
        {"request": request}
    )

@app.get("/print")
async def print_layout_page(request: Request):
    """
    Render the print layout selection page.
    """
    return templates.TemplateResponse(
        "print_layout.html",
        {"request": request}
    )

@app.get("/print-preview")
async def print_preview_page(request: Request):
    """
    Render the print preview page.
    """
    return templates.TemplateResponse(
        "print_preview.html",
        {"request": request}
    )

# Routes for canvas crop tool
from fastapi import File, UploadFile, Form
from app.services.landmark_detection import detect_landmarks, save_uploaded_image
from app.services.photostandard_service import get_photo_standard

@app.get("/photostandards/{standard_id}")
async def get_photo_standard_direct(standard_id: str):
    """
    Get a specific photo standard by ID.
    """
    standard = get_photo_standard(standard_id)
    if standard:
        return {"standard": standard}
    else:
        raise HTTPException(status_code=404, detail="Photo standard not found")

@app.post("/upload")
async def upload_image(file: UploadFile = File(...)):
    """
    Upload an image for processing.
    """
    print(f"Uploading file: {file.filename}, size: {file.size}")

    # Read the file content
    contents = await file.read()
    print(f"Read file content, size: {len(contents)} bytes")

    # Save the image using the landmark detection service
    try:
        image_id, width, height, format = save_uploaded_image(contents)
        print(f"Image saved with ID: {image_id}, dimensions: {width}x{height}, format: {format}")
        return {"image_id": image_id}
    except Exception as e:
        print(f"Error saving image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error saving image: {str(e)}")

@app.get("/images/{image_id}")
async def get_image_by_id(image_id: str):
    """
    Get an image by ID.
    """
    print(f"Getting image with ID: {image_id}")

    # Import image_store directly
    from app.services.landmark_detection import image_store

    # Check if the image exists in the store
    if image_id not in image_store:
        print(f"Image not found in store: {image_id}")
        raise HTTPException(status_code=404, detail="Image not found")

    # Get the image directly from the store
    img = image_store[image_id]['image']

    # Print image shape for debugging
    print(f"Image shape: {img.shape}")

    # Convert the image to bytes
    success, buffer = cv2.imencode(".jpg", img)
    if not success:
        print(f"Failed to encode image: {image_id}")
        raise HTTPException(status_code=500, detail="Failed to encode image")

    print(f"Successfully encoded image: {image_id}, buffer size: {len(buffer)}")

    # Create a response with the image bytes
    return Response(content=buffer.tobytes(), media_type="image/jpeg")

@app.get("/detect/{image_id}")
async def detect_landmarks_by_id(image_id: str):
    """
    Detect landmarks in an image.
    """
    landmarks = detect_landmarks(image_id)
    return {"landmarks": landmarks}

@app.post("/crop")
async def crop_image(
    image_id: str = Form(...),
    standard_id: str = Form(...),
    crown_x: float = Form(...),
    crown_y: float = Form(...),
    chin_x: float = Form(...),
    chin_y: float = Form(...),
):
    """
    Crop an image according to a specific standard.
    """
    from app.models.landmarks import Point
    from app.services.photostandard_service import process_photo

    crown_point = Point(x=crown_x, y=crown_y)
    chin_point = Point(x=chin_x, y=chin_y)

    processed_img, error = process_photo(
        image_id,
        standard_id,
        crown_point,
        chin_point
    )

    if error:
        return {
            "error": error
        }

    # Generate a new image ID for the processed image
    import uuid
    processed_image_id = str(uuid.uuid4())

    # Store the processed image
    from app.services.landmark_detection import image_store
    height, width = processed_img.shape[:2]
    image_store[processed_image_id] = {
        "image": processed_img,
        "width": width,
        "height": height,
        "format": "JPEG",
        "landmarks": None
    }

    return {
        "processed_image_id": processed_image_id
    }

# Configure logging
if "DYNO" in os.environ:  # For Heroku
    app.logger = logging.getLogger("uvicorn")
    app.logger.setLevel(logging.INFO)
    app.logger.addHandler(logging.StreamHandler(sys.stdout))

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("app.main:app", host="0.0.0.0", port=port, reload=True)
