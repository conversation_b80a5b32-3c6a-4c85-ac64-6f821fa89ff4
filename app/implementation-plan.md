# Passport Photo App Implementation Plan

This document outlines the planned improvements to our passport photo application, inspired by the PPP application. We'll implement these features one by one and track our progress.

## 1. Enhanced Facial Landmark Detection and Visualization

### 1.1 Advanced Landmark Visualization
- [x] Add face ellipse to visualize face proportions
- [x] Implement perpendicular guide lines at crown and chin points
- [x] Add crown-to-top distance visualization
- [x] Add face rotation angle indicator

### 1.2 Improved Landmark Adjustment
- [x] Add smoother dragging behavior for landmarks
- [ ] Implement snapping to detected facial features
- [ ] Add keyboard controls for fine-tuning landmark positions

### 1.3 Face Proportion Indicators
- [x] Display face height percentage relative to photo height
- [x] Show numerical measurements for key distances
- [x] Add visual warnings when proportions don't meet standards

## 2. Enhanced Photo Standard Handling

### 2.1 Comprehensive Standard Support
- [ ] Add support for bottomEyeLine parameter as alternative to crownTop
- [ ] Implement proper conversion between different measurement units
- [ ] Add support for pixel-based standards for digital submissions

### 2.2 Country-Based Standard Selection
- [ ] Implement automatic country detection based on IP address
- [ ] Group standards by country and document type
- [ ] Add search functionality for finding specific standards

### 2.3 Standard Compliance Verification
- [ ] Add visual indicators for compliance with selected standard
- [ ] Implement warnings for non-compliant photos
- [ ] Add detailed explanation of compliance issues

## 3. Advanced Crop Box Calculation and Visualization

### 3.1 Improved Crop Box Calculation
- [x] Fix calculation for crop box center based on crownTop
- [x] Add support for bottomEyeLine parameter
- [ ] Implement proper scaling based on face height

### 3.2 Enhanced Measurement Visualization
- [x] Add clear visualization of crown-to-top distance
- [ ] Display all key measurements with proper units
- [ ] Add face height percentage indicator

### 3.3 Alternative Positioning Methods
- [ ] Add support for eye line positioning
- [ ] Implement automatic face centering for standards without specific positioning
- [ ] Add manual override options for special cases

## 4. Improved User Interface

### 4.1 Interactive Guidance
- [ ] Add step-by-step guidance for photo preparation
- [ ] Implement tooltips explaining each measurement
- [ ] Add visual examples of compliant photos

### 4.2 Enhanced Preview
- [ ] Add side-by-side comparison of original and cropped photos
- [ ] Implement real-time preview of changes
- [ ] Add zoom functionality for detailed inspection

### 4.3 Compliance Feedback
- [ ] Add clear visual indicators for compliance status
- [ ] Implement detailed feedback on specific compliance issues
- [ ] Add suggestions for fixing compliance problems

## 5. Advanced Photo Processing

### 5.1 Background Processing
- [ ] Add background color detection and adjustment
- [ ] Implement background replacement for non-compliant backgrounds
- [ ] Add uniform background option

### 5.2 Enhanced Image Transformation
- [ ] Implement proper affine transformation for rotated faces
- [ ] Add automatic image enhancement (brightness, contrast)
- [ ] Implement red-eye removal

### 5.3 Flexible Export Options
- [ ] Add multiple export formats (JPEG, PNG, PDF)
- [ ] Implement print layout with multiple copies
- [ ] Add digital format export for online submissions

## Implementation Progress

### Completed Features
1. ✅ Crown-to-top distance visualization (Date: Previous implementation)
2. ✅ Fix calculation for crop box center based on crownTop (Date: Today)
3. ✅ Add support for bottomEyeLine parameter (Date: Today)
4. ✅ Add face ellipse to visualize face proportions (Date: Today)
5. ✅ Display face height percentage relative to photo height (Date: Today)
6. ✅ Implement perpendicular guide lines at crown and chin points (Date: Today)
7. ✅ Add visual warnings when proportions don't meet standards (Date: Today)
8. ✅ Show numerical measurements for key distances (Date: Today)
9. ✅ Add face rotation angle indicator (Date: Today)
10. ✅ Add smoother dragging behavior for landmarks (Date: Today)

### In Progress
1. 🔄 Implement snapping to detected facial features

### Next Up
1. ⏱️ Add keyboard controls for fine-tuning landmark positions
2. ⏱️ Add support for pixel-based standards for digital submissions
