# Print Functionality Implementation Plan

This document outlines the steps needed to implement print layout functionality in the Optimum project, based on the analysis of the PPP project's implementation.

## Tasks

### Backend Implementation
- [x] Copy print-definitions.json from PPP project to Optimum's app/data folder
- [x] Create a PrintDefinition model in app/models/print_definition.py
- [x] Create a print definition service in app/services/print_definition_service.py
- [x] Enhance the photo_processing.py service to support print layouts
- [x] Create API endpoints for print functionality in app/routers/print.py

### Frontend Implementation
- [x] Create a print layout selection page
- [x] Implement print preview functionality
- [x] Add print layout editing capabilities
- [x] Integrate print functionality with the existing photo processing workflow
- [x] Add print layout functionality directly to the index.html page
- [x] Add additional buttons for print layout functionality
- [x] Improve print layout UI to match PPP app with tiled photo previews
- [x] Fix "Method Not Allowed" error when selecting print layouts
- [x] Add custom photo count functionality to print layouts

## Detailed Implementation Steps

### 1. Backend Models and Services

#### 1.1 Create PrintDefinition Model
Create a model that represents print layouts with properties like:
- Title
- Width and height
- Resolution
- Units (inch, cm, mm)
- Padding and gutter settings
- Custom photo count options (rows and columns)

#### 1.2 Print Definition Service
Implement a service that:
- Loads print definitions from the JSON file
- Provides methods to get, create, update, and delete print definitions
- Handles custom print definitions

#### 1.3 Enhance Photo Processing Service
Extend the existing photo processing service to:
- Create tiled prints based on print definitions
- Calculate optimal layout for photos on the print
- Handle different paper sizes and units
- Support custom photo count (rows and columns) in print layouts

#### 1.4 API Endpoints
Create endpoints for:
- Getting available print definitions
- Creating custom print definitions
- Generating print previews
- Creating final print outputs

### 2. Frontend Implementation

#### 2.1 Print Layout Selection Page
Create a page that:
- Displays available print layouts
- Shows previews of how photos will appear in each layout
- Allows selection of a layout

#### 2.2 Print Preview Functionality
Implement preview that:
- Shows how the final print will look
- Displays multiple photos tiled according to the selected layout
- Provides options to adjust layout settings

#### 2.3 Print Layout Editing
Add functionality to:
- Create custom print layouts
- Edit existing layouts
- Save custom layouts for future use
- Specify custom number of photos in a layout

#### 2.4 Integration with Workflow
Integrate print functionality with the existing workflow:
- Add a step for print layout selection after photo cropping
- Provide options to download or order prints
- Save print preferences for users

## Implementation Notes

- The print functionality should use the existing photo processing pipeline
- Print layouts should respect the photo standards selected by the user
- The implementation should support both digital and physical print sizes
- Custom print layouts should be stored in the user's browser local storage

## Custom Photo Count Feature

### Overview
The custom photo count feature allows users to specify exactly how many photos they want in a print layout, rather than automatically maximizing the number of photos that can fit on the page.

### Implementation Details
1. **Model Changes**:
   - Added `custom_photo_count` boolean field to the PrintDefinition model
   - Added `num_rows` and `num_cols` integer fields to specify the exact number of rows and columns

2. **UI Implementation**:
   - Added a checkbox to enable/disable custom photo count
   - Added input fields for specifying the number of rows and columns
   - Added validation to ensure the specified number of photos can fit on the page
   - Added visual indication of the custom photo count in the layout preview

3. **Backend Processing**:
   - Modified the photo processing service to respect the custom photo count when specified
   - Added fallback to standard calculation if the custom count doesn't fit on the page
   - Added logging to track the photo count calculation process

4. **User Experience**:
   - Custom photo count and maximize photos options are mutually exclusive
   - The layout preview updates in real-time when changing the photo count
   - The layout details display shows the custom photo count when enabled
