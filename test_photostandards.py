#!/usr/bin/env python3

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from app.services import photostandard_service

def test_photostandards():
    print("Testing photostandard service...")
    
    # Test loading photo standards
    standards = photostandard_service.load_photo_standards()
    assert len(standards) > 0
    print(f"Successfully loaded {len(standards)} photo standards")
    
    # Test getting a photo standard by ID
    standard = photostandard_service.get_photo_standard(standards[0].id)
    assert standard is not None
    print(f"Successfully got photo standard: {standard.id}")
    
    # Test getting photo standards by country
    country_standards = photostandard_service.get_photo_standards_by_country(standards[0].country)
    assert len(country_standards) > 0
    print(f"Successfully got {len(country_standards)} standards for country {standards[0].country}")
    
    # Test getting photo standards by document type
    doc_type_standards = photostandard_service.get_photo_standards_by_doc_type(standards[0].docType)
    assert len(doc_type_standards) > 0
    print(f"Successfully got {len(doc_type_standards)} standards for document type {standards[0].docType}")
    
    print("All photostandard service tests passed!")

if __name__ == "__main__":
    test_photostandards()
