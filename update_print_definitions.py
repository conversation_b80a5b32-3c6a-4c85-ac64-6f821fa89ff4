#!/usr/bin/env python3
import json
import os

# Path to the print definitions file
file_path = os.path.join('app', 'data', 'print-definitions.json')

# Read the current print definitions
with open(file_path, 'r') as f:
    definitions = json.load(f)

# Update each definition with the required fields
for definition in definitions:
    # Add missing fields with default values
    if 'gutter' not in definition:
        definition['gutter'] = 0
    if 'custom' not in definition:
        definition['custom'] = False
    if 'paper_color' not in definition:
        definition['paper_color'] = "#ffffff"
    if 'show_borders' not in definition:
        definition['show_borders'] = False
    if 'border_width' not in definition:
        definition['border_width'] = 0.02
    if 'border_color' not in definition:
        definition['border_color'] = "#000000"
    if 'maximize_photos' not in definition:
        definition['maximize_photos'] = False
    if 'custom_photo_count' not in definition:
        definition['custom_photo_count'] = False
    if 'num_rows' not in definition:
        definition['num_rows'] = 2
    if 'num_cols' not in definition:
        definition['num_cols'] = 3

# Write the updated definitions back to the file
with open(file_path, 'w') as f:
    json.dump(definitions, f, indent=2)

print(f"Updated {len(definitions)} print definitions with required fields.")
