#!/usr/bin/env python3
"""
Database setup script for Passport Photo Maker payment system.
This script creates the necessary tables in Supabase for payment processing.
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_database():
    """Set up the database tables for payment processing"""
    
    # Get Supabase credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        print("Please add your Supabase credentials to the .env file:")
        print("SUPABASE_URL=your_supabase_url_here")
        print("SUPABASE_KEY=your_supabase_anon_key_here")
        return False
    
    try:
        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Connected to Supabase successfully")
        
        # Read and execute the schema SQL
        with open('database/schema.sql', 'r') as f:
            schema_sql = f.read()
        
        print("📝 Creating database tables...")
        
        # Split the SQL into individual statements
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement:
                try:
                    # Execute each statement
                    result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                    print(f"✅ Executed statement {i+1}/{len(statements)}")
                except Exception as e:
                    print(f"⚠️  Warning: Statement {i+1} failed: {str(e)}")
                    # Continue with other statements
        
        print("🎉 Database setup completed successfully!")
        print("\nNext steps:")
        print("1. Add your IntaSend API keys to the .env file:")
        print("   INTASEND_PUBLISHABLE_KEY=your_publishable_key_here")
        print("   INTASEND_SECRET_KEY=your_secret_key_here")
        print("   INTASEND_TEST_MODE=true")
        print("2. Start the application: python -m uvicorn app.main:app --reload")
        print("3. Test the payment flow with a sample image")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up database: {str(e)}")
        return False

def check_tables():
    """Check if the tables were created successfully"""
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Check if tables exist by trying to select from them
        tables_to_check = ['users', 'orders', 'download_permissions']
        
        print("\n🔍 Checking created tables:")
        for table in tables_to_check:
            try:
                result = supabase.table(table).select("*").limit(1).execute()
                print(f"✅ Table '{table}' exists and is accessible")
            except Exception as e:
                print(f"❌ Table '{table}' check failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking tables: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 Setting up Passport Photo Maker Payment Database")
    print("=" * 50)
    
    # Check if schema file exists
    if not os.path.exists('database/schema.sql'):
        print("❌ Error: database/schema.sql file not found")
        print("Please make sure you're running this script from the project root directory")
        return
    
    # Setup database
    if setup_database():
        # Check tables
        check_tables()
        print("\n✅ Database setup completed successfully!")
    else:
        print("\n❌ Database setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
